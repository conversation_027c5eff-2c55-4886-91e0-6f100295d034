<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reçu #{{ sale.reference }}</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .receipt {
            max-width: 300px;
            margin: 0 auto;
            background: white;
            padding: 10px;
        }
        .header {
            text-align: center;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .company-info {
            font-size: 10px;
            margin-bottom: 2px;
        }
        .sale-info {
            margin-bottom: 10px;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
        }
        .items {
            margin-bottom: 10px;
        }
        .item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        .item-name {
            flex: 1;
        }
        .item-qty {
            width: 30px;
            text-align: center;
        }
        .item-price {
            width: 60px;
            text-align: right;
        }
        .totals {
            border-top: 1px dashed #000;
            padding-top: 10px;
            margin-top: 10px;
        }
        .total-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        .total-line.final {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #000;
            padding-top: 5px;
            margin-top: 5px;
        }
        .footer {
            text-align: center;
            margin-top: 15px;
            border-top: 1px dashed #000;
            padding-top: 10px;
            font-size: 10px;
        }
        @media print {
            body { margin: 0; padding: 0; }
            .receipt { max-width: none; }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <!-- En-tête -->
        <div class="header">
            <div class="company-name">
                {% if settings and settings.company_name %}
                    {{ settings.company_name }}
                {% else %}
                    Restaurant POS
                {% endif %}
            </div>
            {% if settings %}
                {% if settings.company_address %}
                    <div class="company-info">{{ settings.company_address }}</div>
                {% endif %}
                {% if settings.company_phone %}
                    <div class="company-info">Tél: {{ settings.company_phone }}</div>
                {% endif %}
                {% if settings.company_email %}
                    <div class="company-info">{{ settings.company_email }}</div>
                {% endif %}
            {% endif %}
        </div>

        <!-- Informations de la vente -->
        <div class="sale-info">
            <div><strong>Reçu #{{ sale.reference }}</strong></div>
            <div>Date: {{ sale.created_at.strftime('%d/%m/%Y %H:%M') }}</div>
            {% if sale.table %}
                <div>Table: {{ sale.table.number }}{% if sale.table.room %} ({{ sale.table.room.name }}){% endif %}</div>
            {% endif %}
            {% if sale.covers_count %}
                <div>Couverts: {{ sale.covers_count }}</div>
            {% endif %}
            <div>Serveur: {{ sale.user.username if sale.user else 'N/A' }}</div>
        </div>

        <!-- Articles -->
        <div class="items">
            <div class="item" style="font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 2px; margin-bottom: 5px;">
                <div class="item-name">Article</div>
                <div class="item-qty">Qté</div>
                <div class="item-price">Prix</div>
            </div>
            {% for item in sale.items %}
            <div class="item">
                <div class="item-name">{{ item.product.name }}</div>
                <div class="item-qty">{{ item.quantity }}</div>
                <div class="item-price">{{ "%.2f"|format(item.total) }}€</div>
            </div>
            {% endfor %}
        </div>

        <!-- Totaux -->
        <div class="totals">
            <div class="total-line">
                <span>Sous-total:</span>
                <span>{{ "%.2f"|format(sale.subtotal) }}€</span>
            </div>
            {% if sale.tax_amount > 0 %}
            <div class="total-line">
                <span>TVA ({{ "%.1f"|format(sale.tax_rate) }}%):</span>
                <span>{{ "%.2f"|format(sale.tax_amount) }}€</span>
            </div>
            {% endif %}
            {% if sale.discount_amount > 0 %}
            <div class="total-line">
                <span>Remise:</span>
                <span>-{{ "%.2f"|format(sale.discount_amount) }}€</span>
            </div>
            {% endif %}
            <div class="total-line final">
                <span>TOTAL:</span>
                <span>{{ "%.2f"|format(sale.total) }}€</span>
            </div>
        </div>

        <!-- Paiement -->
        {% if sale.payments %}
        <div class="totals">
            {% for payment in sale.payments %}
            <div class="total-line">
                <span>{{ payment.method.value|upper }}:</span>
                <span>{{ "%.2f"|format(payment.amount) }}€</span>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Pied de page -->
        <div class="footer">
            <div>Merci de votre visite !</div>
            <div>À bientôt</div>
            {% if settings and settings.company_website %}
                <div>{{ settings.company_website }}</div>
            {% endif %}
        </div>
    </div>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
