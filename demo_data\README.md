# Module de Données de Démonstration

Ce module permet de générer automatiquement des données de démonstration pour le système POS.

## Contenu

- `generate_demo_data.py` : Script principal qui génère des données de test pour tous les modules
  - Utilisateurs (différents rôles)
  - Catégories de produits
  - Ingrédients et catégories
  - Produits avec recettes
  - Fournisseurs
  - Clients
  - Tables et sections
  - Caisse
  - Ventes

## Utilisation

### Exécution automatique au démarrage

Pour lancer l'application avec les données de démonstration :

```bash
python run.py --demo
```

L'argument `--demo` indique au système de charger les données de démonstration après l'initialisation de la base de données.

### Exécution manuelle

Vous pouvez également exécuter le script manuellement :

```bash
python -m demo_data.generate_demo_data
```

## Désactivation

Pour désactiver complètement le chargement des données de démonstration, il suffit de :

1. Ne pas utiliser l'argument `--demo` lors du lancement de l'application, ou
2. Su<PERSON><PERSON><PERSON> ou renommer le dossier `demo_data`

## Comptes de démo

Le script crée les comptes utilisateurs suivants :

| Rôle | Email | Mot de passe |
|------|-------|--------------|
| Admin système | <EMAIL> | admin123 |
| Propriétaire | <EMAIL> | owner123 |
| Administrateur | <EMAIL> | admin_demo123 |
| Manager | <EMAIL> | manager123 |
| Caissier | <EMAIL> | caissier123 |
| Cuisine | <EMAIL> | cuisine123 |
| Serveur | <EMAIL> | serveur123 |
| Comptable | <EMAIL> | comptable123 |

## Remarque

Ce module est prévu uniquement pour le développement et les tests, il n'est pas recommandé de l'utiliser en production. 