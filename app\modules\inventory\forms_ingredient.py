from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON><PERSON>ield, FileAllowed
from wtforms import <PERSON><PERSON>ield, TextAreaField, FloatField, SelectField, DateField, SubmitField
from wtforms.validators import DataRequired, Length, Optional, NumberRange

class IngredientCategoryForm(FlaskForm):
    name = StringField('Nom de la catégorie', validators=[
        DataRequired(),
        Length(min=2, max=64)
    ])
    description = TextAreaField('Description')
    image = FileField('Image', validators=[
        FileAllowed(['jpg', 'png', 'jpeg', 'gif'], 'Images uniquement!')
    ])
    submit = SubmitField('Enregistrer')

class IngredientForm(FlaskForm):
    name = StringField('Nom de l\'ingrédient', validators=[
        DataRequired(),
        Length(min=2, max=64)
    ])
    description = TextAreaField('Description')
    category_id = SelectField('Catégorie', coerce=int, validators=[DataRequired()])
    unit = SelectField('Unité', choices=[
        ('kg', 'Kilogramme'),
        ('l', 'Litre'),
        ('unit', 'Unité')
    ], validators=[DataRequired()])
    price_per_unit = FloatField('Prix par unité', validators=[
        DataRequired(),
        NumberRange(min=0)
    ])
    stock_quantity = FloatField('Quantité en stock', validators=[
        Optional(),
        NumberRange(min=0)
    ])
    minimum_stock = FloatField('Stock minimum', validators=[
        Optional(),
        NumberRange(min=0)
    ])
    expiry_date = DateField('Date d\'expiration', validators=[Optional()])
    image = FileField('Image', validators=[
        FileAllowed(['jpg', 'png', 'jpeg', 'gif'], 'Images uniquement!')
    ])
    submit = SubmitField('Enregistrer')

class IngredientStockForm(FlaskForm):
    quantity = FloatField('Quantité', validators=[
        DataRequired(),
        NumberRange(min=0)
    ])
    operation = SelectField('Opération', choices=[
        ('add', 'Ajouter au stock'),
        ('subtract', 'Retirer du stock')
    ], validators=[DataRequired()])
    submit = SubmitField('Mettre à jour le stock') 