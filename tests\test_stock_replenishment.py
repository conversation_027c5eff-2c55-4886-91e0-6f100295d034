import pytest
from datetime import datetime, timedelta
from app import create_app, db
from app.modules.auth.models import User
from app.modules.inventory.models_purchase_order import PurchaseOrder, PurchaseOrderItem, PurchaseOrderStatus
from app.modules.inventory.models_supplier_invoice import SupplierInvoice, SupplierPayment, PaymentStatus, SupplierPaymentMethod
from app.modules.inventory.models_bank_account import BankAccount, BankOperation, BankOperationType
from app.modules.inventory.models_supplier import Supplier
from app.modules.inventory.models_product import Product, ProductCategory
from app.modules.inventory.models_ingredient import Ingredient, IngredientCategory
from app.modules.cash_register.models_cash_register import CashRegister, CashOperation, CashRegisterOperationType

@pytest.fixture
def app():
    """Créer une instance de l'application pour les tests"""
    app = create_app('testing')
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()

@pytest.fixture
def client(app):
    """Client de test"""
    return app.test_client()

@pytest.fixture
def user(app):
    """Utilisateur de test"""
    user = User(
        username='testuser',
        email='<EMAIL>',
        first_name='Test',
        last_name='User'
    )
    user.set_password('testpassword')
    db.session.add(user)
    db.session.commit()
    return user

@pytest.fixture
def supplier(app, user):
    """Fournisseur de test"""
    supplier = Supplier(
        name='Test Supplier',
        email='<EMAIL>',
        phone='0123456789',
        owner_id=user.id
    )
    db.session.add(supplier)
    db.session.commit()
    return supplier

@pytest.fixture
def product(app, user):
    """Produit de test"""
    category = ProductCategory(name='Test Category', owner_id=user.id)
    db.session.add(category)
    db.session.flush()
    
    product = Product(
        name='Test Product',
        price=10.0,
        cost_price=5.0,
        stock_quantity=100,
        minimum_stock=10,
        unit='pcs',
        category_id=category.id,
        has_recipe=False,
        owner_id=user.id
    )
    db.session.add(product)
    db.session.commit()
    return product

@pytest.fixture
def ingredient(app, user):
    """Ingrédient de test"""
    category = IngredientCategory(name='Test Ingredient Category', owner_id=user.id)
    db.session.add(category)
    db.session.flush()
    
    ingredient = Ingredient(
        name='Test Ingredient',
        price_per_unit=2.0,
        stock_quantity=50,
        minimum_stock=5,
        unit='kg',
        category_id=category.id,
        owner_id=user.id
    )
    db.session.add(ingredient)
    db.session.commit()
    return ingredient

@pytest.fixture
def bank_account(app, user):
    """Compte bancaire de test"""
    account = BankAccount(
        name='Test Bank Account',
        balance=1000.0,
        initial_balance=1000.0,
        is_default=True,
        owner_id=user.id
    )
    db.session.add(account)
    db.session.commit()
    return account

@pytest.fixture
def cash_register(app, user):
    """Caisse de test"""
    register = CashRegister(
        name='Test Register',
        current_balance=500.0,
        is_open=True,
        last_opened_at=datetime.utcnow(),
        owner_id=user.id
    )
    db.session.add(register)
    db.session.commit()
    return register

class TestPurchaseOrder:
    """Tests pour les commandes d'achat"""
    
    def test_create_purchase_order(self, app, user, supplier):
        """Test de création d'une commande d'achat"""
        with app.app_context():
            order = PurchaseOrder(
                reference=PurchaseOrder.generate_reference(),
                supplier_id=supplier.id,
                user_id=user.id,
                owner_id=user.id,
                total_amount=100.0
            )
            db.session.add(order)
            db.session.commit()
            
            assert order.id is not None
            assert order.status == PurchaseOrderStatus.PENDING
            assert order.supplier_name == supplier.name
            assert not order.is_paid
            assert not order.is_received
    
    def test_add_item_to_order(self, app, user, supplier, product):
        """Test d'ajout d'article à une commande"""
        with app.app_context():
            order = PurchaseOrder(
                reference=PurchaseOrder.generate_reference(),
                supplier_id=supplier.id,
                user_id=user.id,
                owner_id=user.id
            )
            db.session.add(order)
            db.session.flush()
            
            item = order.add_item(product=product, quantity=10, unit_price=5.0)
            
            assert item.product_id == product.id
            assert item.quantity == 10
            assert item.unit_price == 5.0
            assert item.total_price == 50.0
            assert order.total_amount == 50.0
    
    def test_mark_order_as_received(self, app, user, supplier, product):
        """Test de marquage d'une commande comme reçue"""
        with app.app_context():
            initial_stock = product.stock_quantity
            
            order = PurchaseOrder(
                reference=PurchaseOrder.generate_reference(),
                supplier_id=supplier.id,
                user_id=user.id,
                owner_id=user.id
            )
            db.session.add(order)
            db.session.flush()
            
            order.add_item(product=product, quantity=10, unit_price=5.0)
            
            success = order.mark_as_received()
            
            assert success
            assert order.status == PurchaseOrderStatus.RECEIVED
            assert order.actual_delivery_date is not None
            
            # Vérifier que le stock a été mis à jour
            db.session.refresh(product)
            assert product.stock_quantity == initial_stock + 10

class TestSupplierInvoice:
    """Tests pour les factures fournisseurs"""
    
    def test_create_supplier_invoice(self, app, user, supplier):
        """Test de création d'une facture fournisseur"""
        with app.app_context():
            invoice = SupplierInvoice(
                reference=SupplierInvoice.generate_reference(),
                supplier_id=supplier.id,
                amount=100.0,
                user_id=user.id,
                owner_id=user.id
            )
            db.session.add(invoice)
            db.session.commit()
            
            assert invoice.id is not None
            assert invoice.payment_status == PaymentStatus.PENDING
            assert invoice.remaining_amount == 100.0
            assert not invoice.is_overdue
    
    def test_add_payment_to_invoice(self, app, user, supplier):
        """Test d'ajout de paiement à une facture"""
        with app.app_context():
            invoice = SupplierInvoice(
                reference=SupplierInvoice.generate_reference(),
                supplier_id=supplier.id,
                amount=100.0,
                user_id=user.id,
                owner_id=user.id
            )
            db.session.add(invoice)
            db.session.flush()
            
            success, message = invoice.add_payment(
                amount=50.0,
                method=SupplierPaymentMethod.CASH_CAISSE,
                user_id=user.id
            )
            
            assert success
            assert invoice.paid_amount == 50.0
            assert invoice.remaining_amount == 50.0
            assert invoice.payment_status == PaymentStatus.PARTIAL
    
    def test_full_payment_invoice(self, app, user, supplier):
        """Test de paiement complet d'une facture"""
        with app.app_context():
            invoice = SupplierInvoice(
                reference=SupplierInvoice.generate_reference(),
                supplier_id=supplier.id,
                amount=100.0,
                user_id=user.id,
                owner_id=user.id
            )
            db.session.add(invoice)
            db.session.flush()
            
            success, message = invoice.add_payment(
                amount=100.0,
                method=SupplierPaymentMethod.CASH_CAISSE,
                user_id=user.id
            )
            
            assert success
            assert invoice.paid_amount == 100.0
            assert invoice.remaining_amount == 0.0
            assert invoice.payment_status == PaymentStatus.PAID
            assert invoice.payment_date is not None

class TestBankAccount:
    """Tests pour les comptes bancaires"""
    
    def test_create_bank_account(self, app, user):
        """Test de création d'un compte bancaire"""
        with app.app_context():
            account = BankAccount(
                name='Test Account',
                balance=1000.0,
                initial_balance=1000.0,
                owner_id=user.id
            )
            db.session.add(account)
            db.session.commit()
            
            assert account.id is not None
            assert account.balance == 1000.0
            assert account.available_balance == 1000.0
            assert not account.is_overdrawn
    
    def test_bank_transfer(self, app, user):
        """Test de virement entre comptes"""
        with app.app_context():
            account1 = BankAccount(
                name='Account 1',
                balance=1000.0,
                owner_id=user.id
            )
            account2 = BankAccount(
                name='Account 2',
                balance=500.0,
                owner_id=user.id
            )
            db.session.add_all([account1, account2])
            db.session.flush()
            
            success, message = account1.transfer_to(
                target_account=account2,
                amount=200.0,
                description='Test transfer',
                user_id=user.id
            )
            
            assert success
            assert account1.balance == 800.0
            assert account2.balance == 700.0
    
    def test_cash_deposit(self, app, user, cash_register):
        """Test de dépôt depuis la caisse"""
        with app.app_context():
            account = BankAccount(
                name='Test Account',
                balance=1000.0,
                owner_id=user.id
            )
            db.session.add(account)
            db.session.flush()
            
            initial_cash = cash_register.current_balance
            initial_bank = account.balance
            
            success, message = account.deposit_from_cash_register(
                amount=100.0,
                cash_register=cash_register,
                user_id=user.id
            )
            
            assert success
            assert account.balance == initial_bank + 100.0
            assert cash_register.current_balance == initial_cash - 100.0

class TestSupplierPayment:
    """Tests pour les paiements fournisseurs"""
    
    def test_cash_payment_processing(self, app, user, supplier, cash_register):
        """Test de traitement d'un paiement cash"""
        with app.app_context():
            invoice = SupplierInvoice(
                reference=SupplierInvoice.generate_reference(),
                supplier_id=supplier.id,
                amount=100.0,
                user_id=user.id,
                owner_id=user.id
            )
            db.session.add(invoice)
            db.session.flush()
            
            payment = SupplierPayment(
                invoice=invoice,
                amount=100.0,
                payment_method=SupplierPaymentMethod.CASH_CAISSE,
                user_id=user.id,
                owner_id=user.id
            )
            db.session.add(payment)
            db.session.flush()
            
            initial_balance = cash_register.current_balance
            
            success, message = payment.process_cash_register_operation()
            
            assert success
            assert cash_register.current_balance == initial_balance - 100.0
            assert payment.cash_register_operation_id is not None
    
    def test_bank_payment_processing(self, app, user, supplier, bank_account):
        """Test de traitement d'un paiement bancaire"""
        with app.app_context():
            invoice = SupplierInvoice(
                reference=SupplierInvoice.generate_reference(),
                supplier_id=supplier.id,
                amount=100.0,
                user_id=user.id,
                owner_id=user.id
            )
            db.session.add(invoice)
            db.session.flush()
            
            payment = SupplierPayment(
                invoice=invoice,
                amount=100.0,
                payment_method=SupplierPaymentMethod.VIREMENT_COMPTE_BANQUE,
                bank_account=bank_account,
                user_id=user.id,
                owner_id=user.id
            )
            db.session.add(payment)
            db.session.flush()
            
            initial_balance = bank_account.balance
            
            success, message = payment.process_bank_operation()
            
            assert success
            assert bank_account.balance == initial_balance - 100.0

class TestIntegration:
    """Tests d'intégration du système complet"""
    
    def test_complete_purchase_workflow(self, app, user, supplier, product, cash_register):
        """Test du workflow complet d'achat"""
        with app.app_context():
            # 1. Créer une commande
            order = PurchaseOrder(
                reference=PurchaseOrder.generate_reference(),
                supplier_id=supplier.id,
                user_id=user.id,
                owner_id=user.id
            )
            db.session.add(order)
            db.session.flush()
            
            # 2. Ajouter des articles
            initial_stock = product.stock_quantity
            order.add_item(product=product, quantity=20, unit_price=5.0)
            
            # 3. Marquer comme reçue
            order.mark_as_received()
            
            # 4. Créer une facture
            invoice = SupplierInvoice(
                reference=SupplierInvoice.generate_reference(),
                supplier_id=supplier.id,
                purchase_order_id=order.id,
                amount=order.total_amount,
                user_id=user.id,
                owner_id=user.id
            )
            db.session.add(invoice)
            db.session.flush()
            
            # 5. Effectuer le paiement
            initial_cash = cash_register.current_balance
            success, message = invoice.add_payment(
                amount=invoice.amount,
                method=SupplierPaymentMethod.CASH_CAISSE,
                user_id=user.id
            )
            
            # Traiter l'opération de caisse
            payment = invoice.payments.first()
            payment.process_cash_register_operation()
            
            # Vérifications finales
            assert order.status == PurchaseOrderStatus.RECEIVED
            assert invoice.payment_status == PaymentStatus.PAID
            
            db.session.refresh(product)
            assert product.stock_quantity == initial_stock + 20
            assert cash_register.current_balance == initial_cash - invoice.amount

if __name__ == '__main__':
    pytest.main([__file__])
