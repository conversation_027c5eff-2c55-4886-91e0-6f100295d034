{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ title }}</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.name.label(class="form-label") }}
                            {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                            {% for error in form.name.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="3") }}
                            {% for error in form.description.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.image.label(class="form-label") }}
                            {{ form.image(class="form-control" + (" is-invalid" if form.image.errors else ""), accept="image/*") }}
                            <small class="text-muted">Formats acceptés: JPG, PNG, GIF</small>
                            {% for error in form.image.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                            
                            {% if form.image.data %}
                            <div class="mt-2">
                                <img src="{{ url_for('static', filename=form.image.data) }}" class="img-thumbnail" style="max-height: 200px;">
                            </div>
                            {% endif %}
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('inventory.ingredient_categories') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.querySelector('input[type="file"]');
    const imagePreview = document.querySelector('.img-thumbnail');
    
    imageInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                if (imagePreview) {
                    imagePreview.src = e.target.result;
                } else {
                    const newPreview = document.createElement('img');
                    newPreview.src = e.target.result;
                    newPreview.className = 'img-thumbnail';
                    newPreview.style.maxHeight = '200px';
                    imageInput.parentNode.appendChild(newPreview);
                }
            }
            reader.readAsDataURL(this.files[0]);
        }
    });
});
</script>
{% endblock %} 