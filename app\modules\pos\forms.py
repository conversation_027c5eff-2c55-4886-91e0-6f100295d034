from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, FloatField, IntegerField, SelectField, SubmitField, TextAreaField, RadioField
from wtforms.validators import DataRequired, NumberRange, Optional
from app.modules.cash_register.models_cash_register import PaymentMethod

class SalePaymentForm(FlaskForm):
    payment_method = SelectField('Méthode de paiement', validators=[DataRequired()], 
                               choices=[(pm.name, pm.value) for pm in PaymentMethod])
    amount_tendered = FloatField('Montant reçu', validators=[NumberRange(min=0)], default=0)
    submit = SubmitField('Finaliser la vente')

class KitchenNoteForm(FlaskForm):
    note = TextAreaField('Note pour la cuisine', validators=[Optional()])
    submit = SubmitField('Envoyer')

class PaymentForm(FlaskForm):
    method = SelectField('Méthode de paiement', validators=[DataRequired()],
                        choices=[(pm.name, pm.value) for pm in PaymentMethod])
    amount = FloatField('Montant', validators=[DataRequired(), NumberRange(min=0)])
    reference = StringField('Référence', validators=[Optional()])
    submit = SubmitField('Procéder au paiement')