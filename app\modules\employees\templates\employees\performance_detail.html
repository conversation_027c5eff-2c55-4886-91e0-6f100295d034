{% extends "employees/base_hr.html" %}

{% block title %}Détails Évaluation - {{ performance.employee.full_name }}{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-star me-2"></i>Évaluation de {{ performance.employee.full_name }}
        <small class="text-muted">{{ performance.evaluation_date.strftime('%d/%m/%Y') }}</small>
    </h1>
    <div>
        {% if not performance.is_finalized %}
        <a href="{{ url_for('employees.edit_performance', performance_id=performance.id) }}" class="btn btn-warning me-2">
            <i class="fas fa-edit me-1"></i>Modifier
        </a>
        {% endif %}
        <a href="{{ url_for('employees.performance', id=performance.employee_id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour
        </a>
    </div>
</div>

<!-- Statut de l'évaluation -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-{{ 'success' if (performance.overall_score or 0) >= 4 else 'warning' if (performance.overall_score or 0) >= 3 else 'danger' }}">
                                {{ "%.1f"|format(performance.overall_score or 0) }}/5
                            </h3>
                            <p class="mb-0">Score Global</p>
                            <small class="text-muted">{{ performance.performance_level }}</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            {% if performance.is_finalized %}
                                <span class="badge bg-success fs-6">
                                    <i class="fas fa-check me-1"></i>Finalisée
                                </span>
                            {% else %}
                                <span class="badge bg-warning fs-6">
                                    <i class="fas fa-clock me-1"></i>En cours
                                </span>
                            {% endif %}
                            <p class="mb-0 mt-2">Statut</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            {% if performance.employee_acknowledged %}
                                <span class="badge bg-info fs-6">
                                    <i class="fas fa-user-check me-1"></i>Reconnue
                                </span>
                                <p class="mb-0 mt-2">{{ performance.acknowledged_at.strftime('%d/%m/%Y') if performance.acknowledged_at else '' }}</p>
                            {% else %}
                                <span class="badge bg-secondary fs-6">
                                    <i class="fas fa-user-clock me-1"></i>En attente
                                </span>
                                <p class="mb-0 mt-2">Reconnaissance employé</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Informations générales -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Informations Générales
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Employé:</strong></td>
                        <td>{{ performance.employee.full_name }}</td>
                    </tr>
                    <tr>
                        <td><strong>Poste:</strong></td>
                        <td>{{ performance.employee.position }}</td>
                    </tr>
                    <tr>
                        <td><strong>Date d'évaluation:</strong></td>
                        <td>{{ performance.evaluation_date.strftime('%d/%m/%Y') }}</td>
                    </tr>
                    <tr>
                        <td><strong>Période évaluée:</strong></td>
                        <td>{{ performance.evaluation_period_start.strftime('%d/%m/%Y') }} - {{ performance.evaluation_period_end.strftime('%d/%m/%Y') }}</td>
                    </tr>
                    <tr>
                        <td><strong>Évaluateur:</strong></td>
                        <td>{{ performance.evaluator.username if performance.evaluator else 'Non défini' }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-radar me-2"></i>Scores par Critère
                </h5>
            </div>
            <div class="card-body">
                <canvas id="skillsChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Détails des scores -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-star me-2"></i>Détail des Évaluations
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-primary">{{ performance.punctuality_score or 0 }}/5</h4>
                            <p class="mb-0">Ponctualité</p>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-primary">{{ performance.quality_of_work_score or 0 }}/5</h4>
                            <p class="mb-0">Qualité du Travail</p>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-primary">{{ performance.teamwork_score or 0 }}/5</h4>
                            <p class="mb-0">Travail d'Équipe</p>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-primary">{{ performance.communication_score or 0 }}/5</h4>
                            <p class="mb-0">Communication</p>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-primary">{{ performance.initiative_score or 0 }}/5</h4>
                            <p class="mb-0">Initiative</p>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-primary">{{ performance.customer_service_score or 0 }}/5</h4>
                            <p class="mb-0">Service Client</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Commentaires -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-thumbs-up me-2"></i>Points Forts
                </h5>
            </div>
            <div class="card-body">
                {% if performance.strengths %}
                    <p>{{ performance.strengths }}</p>
                {% else %}
                    <p class="text-muted">Aucun point fort spécifié.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-arrow-up me-2"></i>Axes d'Amélioration
                </h5>
            </div>
            <div class="card-body">
                {% if performance.areas_for_improvement %}
                    <p>{{ performance.areas_for_improvement }}</p>
                {% else %}
                    <p class="text-muted">Aucun axe d'amélioration spécifié.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bullseye me-2"></i>Objectifs Prochaine Période
                </h5>
            </div>
            <div class="card-body">
                {% if performance.goals_next_period %}
                    <p>{{ performance.goals_next_period }}</p>
                {% else %}
                    <p class="text-muted">Aucun objectif défini.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-comments me-2"></i>Commentaires de l'Évaluateur
                </h5>
            </div>
            <div class="card-body">
                {% if performance.evaluator_comments %}
                    <p>{{ performance.evaluator_comments }}</p>
                {% else %}
                    <p class="text-muted">Aucun commentaire de l'évaluateur.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Actions -->
{% if not performance.is_finalized or not performance.employee_acknowledged %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>Actions
                </h5>
            </div>
            <div class="card-body">
                {% if not performance.is_finalized %}
                <form method="POST" action="{{ url_for('employees.finalize_performance', performance_id=performance.id) }}" class="d-inline">
                    <button type="submit" class="btn btn-success me-2" onclick="return confirm('Finaliser cette évaluation ? Elle ne pourra plus être modifiée.')">
                        <i class="fas fa-check me-1"></i>Finaliser l'Évaluation
                    </button>
                </form>
                {% endif %}
                
                {% if performance.is_finalized and not performance.employee_acknowledged %}
                <form method="POST" action="{{ url_for('employees.acknowledge_performance', performance_id=performance.id) }}" class="d-inline">
                    <button type="submit" class="btn btn-info me-2">
                        <i class="fas fa-user-check me-1"></i>Marquer comme Reconnue
                    </button>
                </form>
                {% endif %}
                
                {% if not performance.is_finalized or not performance.employee_acknowledged %}
                <form method="POST" action="{{ url_for('employees.delete_performance', performance_id=performance.id) }}" class="d-inline" onsubmit="return confirm('Supprimer cette évaluation ? Cette action est irréversible.')">
                    <button type="submit" class="btn btn-outline-danger">
                        <i class="fas fa-trash me-1"></i>Supprimer
                    </button>
                </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Graphique radar des compétences
const skillsCtx = document.getElementById('skillsChart').getContext('2d');
const skillsChart = new Chart(skillsCtx, {
    type: 'radar',
    data: {
        labels: ['Ponctualité', 'Qualité', 'Équipe', 'Communication', 'Initiative', 'Service Client'],
        datasets: [{
            label: 'Scores',
            data: [
                {{ performance.punctuality_score or 0 }},
                {{ performance.quality_of_work_score or 0 }},
                {{ performance.teamwork_score or 0 }},
                {{ performance.communication_score or 0 }},
                {{ performance.initiative_score or 0 }},
                {{ performance.customer_service_score or 0 }}
            ],
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            r: {
                beginAtZero: true,
                max: 5
            }
        }
    }
});
</script>
{% endblock %}
