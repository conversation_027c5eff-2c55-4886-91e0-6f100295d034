{% extends "base.html" %}

{% block title %}Commande en ligne - Paramètres{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-globe me-2"></i>Commande en ligne
        </h1>
        {% if form.online_ordering_enabled.data and form.online_ordering_subdomain.data %}
            <a href="http://{{ form.online_ordering_subdomain.data }}.lvh.me:5000" target="_blank" class="btn btn-primary">
                <i class="fas fa-external-link-alt me-1"></i>Voir mon site
            </a>
        {% endif %}
    </div>

    <!-- Status Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-left-{% if form.online_ordering_enabled.data %}success{% else %}warning{% endif %}">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-{% if form.online_ordering_enabled.data %}success{% else %}warning{% endif %} text-uppercase mb-1">
                                Statut de la commande en ligne
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if form.online_ordering_enabled.data %}
                                    <i class="fas fa-check-circle text-success me-2"></i>Activé
                                {% else %}
                                    <i class="fas fa-times-circle text-warning me-2"></i>Désactivé
                                {% endif %}
                            </div>
                            {% if form.online_ordering_subdomain.data %}
                                <div class="text-muted small mt-1">
                                    Site accessible à: <strong>http://{{ form.online_ordering_subdomain.data }}.lvh.me:5000</strong>
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-globe fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <form method="POST">
        {{ form.hidden_tag() }}
        
        <div class="row">
            <!-- Configuration générale -->
            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-cog me-2"></i>Configuration générale
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Activation -->
                        <div class="form-group mb-3">
                            <div class="form-check form-switch">
                                {{ form.online_ordering_enabled(class="form-check-input") }}
                                {{ form.online_ordering_enabled.label(class="form-check-label") }}
                            </div>
                            <small class="form-text text-muted">
                                Active ou désactive complètement la commande en ligne pour votre restaurant
                            </small>
                        </div>

                        <!-- Sous-domaine -->
                        <div class="form-group mb-3">
                            {{ form.online_ordering_subdomain.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.online_ordering_subdomain(class="form-control") }}
                                <span class="input-group-text">.lvh.me:5000</span>
                            </div>
                            {% if form.online_ordering_subdomain.description %}
                                <small class="form-text text-muted">{{ form.online_ordering_subdomain.description }}</small>
                            {% endif %}
                            {% if form.online_ordering_subdomain.errors %}
                                <div class="text-danger small">
                                    {% for error in form.online_ordering_subdomain.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Nom du site -->
                        <div class="form-group mb-3">
                            {{ form.online_ordering_site_name.label(class="form-label") }}
                            {{ form.online_ordering_site_name(class="form-control") }}
                            {% if form.online_ordering_site_name.errors %}
                                <div class="text-danger small">
                                    {% for error in form.online_ordering_site_name.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Description -->
                        <div class="form-group mb-3">
                            {{ form.online_ordering_description.label(class="form-label") }}
                            {{ form.online_ordering_description(class="form-control", rows="3") }}
                            {% if form.online_ordering_description.errors %}
                                <div class="text-danger small">
                                    {% for error in form.online_ordering_description.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Personnalisation -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-palette me-2"></i>Personnalisation
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.online_ordering_primary_color.label(class="form-label") }}
                                    {{ form.online_ordering_primary_color(class="form-control", type="color") }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.online_ordering_secondary_color.label(class="form-label") }}
                                    {{ form.online_ordering_secondary_color(class="form-control", type="color") }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Aperçu:</strong> Les couleurs seront appliquées automatiquement à votre site de commande en ligne.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Services et livraison -->
            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-truck me-2"></i>Services disponibles
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    {{ form.delivery_enabled(class="form-check-input") }}
                                    {{ form.delivery_enabled.label(class="form-check-label") }}
                                </div>
                                <div class="form-check mb-3">
                                    {{ form.pickup_enabled(class="form-check-input") }}
                                    {{ form.pickup_enabled.label(class="form-check-label") }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    {{ form.dine_in_enabled(class="form-check-input") }}
                                    {{ form.dine_in_enabled.label(class="form-check-label") }}
                                </div>
                                <div class="form-check mb-3">
                                    {{ form.drive_through_enabled(class="form-check-input") }}
                                    {{ form.drive_through_enabled.label(class="form-check-label") }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-euro-sign me-2"></i>Configuration de livraison
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.delivery_fee.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.delivery_fee(class="form-control") }}
                                        <span class="input-group-text">€</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.minimum_order_amount.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.minimum_order_amount(class="form-control") }}
                                        <span class="input-group-text">€</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            {{ form.delivery_radius.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.delivery_radius(class="form-control") }}
                                <span class="input-group-text">km</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-bell me-2"></i>Notifications
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-2">
                            {{ form.notify_kitchen_online_orders(class="form-check-input") }}
                            {{ form.notify_kitchen_online_orders.label(class="form-check-label") }}
                        </div>
                        <div class="form-check mb-2">
                            {{ form.notify_customer_order_confirmed(class="form-check-input") }}
                            {{ form.notify_customer_order_confirmed.label(class="form-check-label") }}
                        </div>
                        <div class="form-check mb-2">
                            {{ form.notify_customer_order_ready(class="form-check-input") }}
                            {{ form.notify_customer_order_ready.label(class="form-check-label") }}
                        </div>
                        <div class="form-check mb-2">
                            {{ form.notify_customer_out_for_delivery(class="form-check-input") }}
                            {{ form.notify_customer_out_for_delivery.label(class="form-check-label") }}
                        </div>
                        <div class="form-check mb-2">
                            {{ form.auto_assign_deliverer(class="form-check-input") }}
                            {{ form.auto_assign_deliverer.label(class="form-check-label") }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Boutons d'action -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-body text-center">
                        {{ form.submit(class="btn btn-primary btn-lg me-3") }}
                        <a href="{{ url_for('settings.business') }}" class="btn btn-secondary btn-lg">
                            <i class="fas fa-arrow-left me-1"></i>Retour
                        </a>
                        
                        {% if form.online_ordering_enabled.data and form.online_ordering_subdomain.data %}
                            <a href="http://{{ form.online_ordering_subdomain.data }}.lvh.me:5000" target="_blank" class="btn btn-success btn-lg ms-3">
                                <i class="fas fa-external-link-alt me-1"></i>Voir mon site
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Prévisualisation des couleurs
    const primaryColor = document.getElementById('online_ordering_primary_color');
    const secondaryColor = document.getElementById('online_ordering_secondary_color');
    
    function updatePreview() {
        // Vous pouvez ajouter ici une logique de prévisualisation
        console.log('Couleur principale:', primaryColor.value);
        console.log('Couleur secondaire:', secondaryColor.value);
    }
    
    if (primaryColor) primaryColor.addEventListener('change', updatePreview);
    if (secondaryColor) secondaryColor.addEventListener('change', updatePreview);
});
</script>
{% endblock %}
