{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">{{ title }}</h2>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.business_name.label(class="form-label") }}
                            {{ form.business_name(class="form-control" + (" is-invalid" if form.business_name.errors else "")) }}
                            {% for error in form.business_name.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.business_type.label(class="form-label") }}
                            {{ form.business_type(class="form-control" + (" is-invalid" if form.business_type.errors else "")) }}
                            {% for error in form.business_type.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.address.label(class="form-label") }}
                            {{ form.address(class="form-control" + (" is-invalid" if form.address.errors else ""), rows=3) }}
                            {% for error in form.address.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.phone.label(class="form-label") }}
                                {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                                {% for error in form.phone.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-6 mb-3">
                                {{ form.email.label(class="form-label") }}
                                {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                {% for error in form.email.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.website.label(class="form-label") }}
                            {{ form.website(class="form-control" + (" is-invalid" if form.website.errors else "")) }}
                            {% for error in form.website.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.logo.label(class="form-label") }}
                            {{ form.logo(class="form-control" + (" is-invalid" if form.logo.errors else "")) }}
                            <small class="text-muted">Formats acceptés: PNG, JPG, JPEG</small>
                            {% for error in form.logo.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                            {% if settings and settings.logo_path %}
                            <div class="mt-2">
                                <img src="{{ url_for('static', filename=settings.logo_path) }}" 
                                     alt="Logo actuel" 
                                     class="img-thumbnail" 
                                     style="max-height: 100px;">
                            </div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                {{ form.currency.label(class="form-label") }}
                                {{ form.currency(class="form-select" + (" is-invalid" if form.currency.errors else "")) }}
                                {% for error in form.currency.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-4 mb-3">
                                {{ form.timezone.label(class="form-label") }}
                                {{ form.timezone(class="form-select" + (" is-invalid" if form.timezone.errors else "")) }}
                                {% for error in form.timezone.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-4 mb-3">
                                {{ form.language.label(class="form-label") }}
                                {{ form.language(class="form-select" + (" is-invalid" if form.language.errors else "")) }}
                                {% for error in form.language.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer les modifications
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 