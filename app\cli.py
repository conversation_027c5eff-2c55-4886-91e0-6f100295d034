import click
from flask.cli import with_appcontext
from app import db
from app.modules.auth.models import User, UserRole

@click.command('promote-to-system-admin')
@click.argument('username')
@with_appcontext
def promote_to_system_admin(username):
    """Promouvoir un utilisateur au rôle de SYSTEM_ADMIN"""
    user = User.query.filter_by(username=username).first()
    if not user:
        click.echo(f"Erreur: L'utilisateur '{username}' n'existe pas.")
        return
    
    if user.role == UserRole.SYSTEM_ADMIN:
        click.echo(f"L'utilisateur '{username}' est déjà un administrateur système.")
        return
    
    user.role = UserRole.SYSTEM_ADMIN
    db.session.commit()
    click.echo(f"L'utilisateur '{username}' a été promu au rôle d'administrateur système avec succès.")

def init_app(app):
    app.cli.add_command(promote_to_system_admin) 