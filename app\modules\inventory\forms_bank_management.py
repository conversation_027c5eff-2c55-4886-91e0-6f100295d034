from flask_wtf import F<PERSON>kForm
from wtforms import <PERSON>Field, FloatField, SelectField, TextAreaField, SubmitField, BooleanField, DateTimeField, DateField
from wtforms.validators import DataRequired, NumberRange, Optional, Length, Email
from app.modules.inventory.models_bank_account import BankOperationType
from flask_login import current_user

class BankAccountForm(FlaskForm):
    """Formulaire pour créer/modifier un compte bancaire"""
    name = StringField('Nom du compte', 
                      validators=[DataRequired(), Length(min=2, max=100)])
    
    account_number = StringField('Numéro de compte', 
                                validators=[Optional(), Length(max=50)])
    
    iban = StringField('IBAN', 
                      validators=[Optional(), Length(max=34)])
    
    bic = StringField('BIC/SWIFT', 
                     validators=[Optional(), Length(max=11)])
    
    bank_name = StringField('Nom de la banque',
                           validators=[Optional(), Length(max=100)])

    branch = StringField('Agence',
                        validators=[Optional(), Length(max=100)])

    bank_address = TextAreaField('Adresse de la banque',
                                validators=[Optional(), Length(max=500)])
    
    initial_balance = FloatField('Solde initial', 
                                validators=[Optional(), NumberRange(min=0)],
                                default=0)
    
    overdraft_limit = FloatField('Découvert autorisé', 
                                validators=[Optional(), NumberRange(min=0)],
                                default=0)
    
    currency = SelectField('Devise',
                          choices=[('EUR', 'Euro (€)'), ('USD', 'Dollar ($)'), ('GBP', 'Livre (£)')],
                          default='EUR',
                          validators=[DataRequired()])
    
    is_default = BooleanField('Compte par défaut')

    is_active = BooleanField('Compte actif', default=True)

    description = TextAreaField('Description',
                               validators=[Optional(), Length(max=500)])
    
    notes = TextAreaField('Notes', 
                         validators=[Optional(), Length(max=500)])
    
    submit = SubmitField('Enregistrer le compte')

class BankOperationForm(FlaskForm):
    """Formulaire pour enregistrer une opération bancaire"""
    bank_account_id = SelectField('Compte bancaire', 
                                 coerce=int, 
                                 validators=[DataRequired()])
    
    operation_type = SelectField('Type d\'opération',
                                choices=[(op.value, op.value.replace('_', ' ').title()) 
                                        for op in BankOperationType],
                                validators=[DataRequired()])
    
    amount = FloatField('Montant', 
                       validators=[DataRequired(), NumberRange(min=0.01)])
    
    description = TextAreaField('Description', 
                               validators=[DataRequired(), Length(min=5, max=500)])
    
    reference = StringField('Référence', 
                           validators=[Optional(), Length(max=100)])
    
    operation_date = DateField('Date de l\'opération',
                              validators=[Optional()],
                              format='%Y-%m-%d')

    value_date = DateField('Date de valeur',
                          validators=[Optional()],
                          format='%Y-%m-%d')
    
    notes = TextAreaField('Notes', 
                         validators=[Optional(), Length(max=500)])
    
    submit = SubmitField('Enregistrer l\'opération')
    
    def __init__(self, *args, **kwargs):
        super(BankOperationForm, self).__init__(*args, **kwargs)
        
        # Charger les comptes bancaires de l'utilisateur
        if current_user.is_authenticated:
            from app.modules.inventory.models_bank_account import BankAccount
            bank_accounts = BankAccount.query.filter_by(
                owner_id=current_user.id, 
                is_active=True
            ).all()
            self.bank_account_id.choices = [
                (account.id, f"{account.name} ({account.balance:.2f}€)")
                for account in bank_accounts
            ]

class BankTransferForm(FlaskForm):
    """Formulaire pour effectuer un virement entre comptes"""
    source_account_id = SelectField('Compte source', 
                                   coerce=int, 
                                   validators=[DataRequired()])
    
    target_account_id = SelectField('Compte destinataire', 
                                   coerce=int, 
                                   validators=[DataRequired()])
    
    amount = FloatField('Montant', 
                       validators=[DataRequired(), NumberRange(min=0.01)])
    
    description = TextAreaField('Description du virement', 
                               validators=[DataRequired(), Length(min=5, max=500)])
    
    reference = StringField('Référence', 
                           validators=[Optional(), Length(max=100)])
    
    notes = TextAreaField('Notes', 
                         validators=[Optional(), Length(max=500)])
    
    submit = SubmitField('Effectuer le virement')
    
    def __init__(self, *args, **kwargs):
        super(BankTransferForm, self).__init__(*args, **kwargs)
        
        # Charger les comptes bancaires de l'utilisateur
        if current_user.is_authenticated:
            from app.modules.inventory.models_bank_account import BankAccount
            bank_accounts = BankAccount.query.filter_by(
                owner_id=current_user.id, 
                is_active=True
            ).all()
            
            account_choices = [
                (account.id, f"{account.name} ({account.balance:.2f}€)")
                for account in bank_accounts
            ]
            
            self.source_account_id.choices = [(0, 'Sélectionner le compte source')] + account_choices
            self.target_account_id.choices = [(0, 'Sélectionner le compte destinataire')] + account_choices

class CashToBankDepositForm(FlaskForm):
    """Formulaire pour déposer de l'argent de la caisse vers la banque"""
    cash_register_id = SelectField('Caisse',
                                  coerce=int,
                                  validators=[DataRequired()])

    bank_account_id = SelectField('Compte bancaire',
                                 coerce=int,
                                 validators=[DataRequired()])

    amount = FloatField('Montant à déposer',
                       validators=[DataRequired(), NumberRange(min=0.01)])
    
    description = TextAreaField('Description', 
                               validators=[Optional(), Length(max=500)],
                               default='Dépôt depuis la caisse')
    
    reference = StringField('Référence du dépôt', 
                           validators=[Optional(), Length(max=100)])
    
    notes = TextAreaField('Notes', 
                         validators=[Optional(), Length(max=500)])
    
    submit = SubmitField('Effectuer le dépôt')
    
    def __init__(self, *args, **kwargs):
        super(CashToBankDepositForm, self).__init__(*args, **kwargs)
        
        # Charger les comptes bancaires de l'utilisateur
        if current_user.is_authenticated:
            from app.modules.inventory.models_bank_account import BankAccount
            bank_accounts = BankAccount.query.filter_by(
                owner_id=current_user.id, 
                is_active=True
            ).all()
            self.bank_account_id.choices = [
                (account.id, f"{account.name} ({account.balance:.2f}€)")
                for account in bank_accounts
            ]

class BankReconciliationForm(FlaskForm):
    """Formulaire pour la réconciliation bancaire"""
    bank_account_id = SelectField('Compte bancaire', 
                                 coerce=int, 
                                 validators=[DataRequired()])
    
    statement_date = DateField('Date du relevé',
                              validators=[DataRequired()],
                              format='%Y-%m-%d')
    
    statement_balance = FloatField('Solde du relevé', 
                                  validators=[DataRequired()])
    
    notes = TextAreaField('Notes de réconciliation', 
                         validators=[Optional(), Length(max=500)])
    
    submit = SubmitField('Effectuer la réconciliation')
    
    def __init__(self, *args, **kwargs):
        super(BankReconciliationForm, self).__init__(*args, **kwargs)
        
        # Charger les comptes bancaires de l'utilisateur
        if current_user.is_authenticated:
            from app.modules.inventory.models_bank_account import BankAccount
            bank_accounts = BankAccount.query.filter_by(
                owner_id=current_user.id, 
                is_active=True
            ).all()
            self.bank_account_id.choices = [
                (account.id, f"{account.name} (Solde: {account.balance:.2f}€)")
                for account in bank_accounts
            ]

class BankAccountSettingsForm(FlaskForm):
    """Formulaire pour les paramètres du compte bancaire"""
    is_active = BooleanField('Compte actif')
    is_default = BooleanField('Compte par défaut')
    overdraft_limit = FloatField('Découvert autorisé', 
                                validators=[Optional(), NumberRange(min=0)])
    
    # Notifications
    low_balance_alert = BooleanField('Alerte solde bas')
    low_balance_threshold = FloatField('Seuil d\'alerte', 
                                      validators=[Optional(), NumberRange(min=0)])
    
    overdraft_alert = BooleanField('Alerte découvert')
    
    # Contact pour les alertes
    alert_email = StringField('Email pour les alertes', 
                             validators=[Optional(), Email(), Length(max=120)])
    
    notes = TextAreaField('Notes', 
                         validators=[Optional(), Length(max=500)])
    
    submit = SubmitField('Mettre à jour les paramètres')

class BankStatementImportForm(FlaskForm):
    """Formulaire pour importer un relevé bancaire (future fonctionnalité)"""
    bank_account_id = SelectField('Compte bancaire', 
                                 coerce=int, 
                                 validators=[DataRequired()])
    
    statement_format = SelectField('Format du relevé',
                                  choices=[
                                      ('csv', 'CSV'),
                                      ('ofx', 'OFX'),
                                      ('qif', 'QIF'),
                                      ('manual', 'Saisie manuelle')
                                  ],
                                  validators=[DataRequired()])
    
    # Pour la saisie manuelle
    manual_operations = TextAreaField('Opérations (une par ligne)', 
                                     validators=[Optional()],
                                     render_kw={'rows': 10})
    
    notes = TextAreaField('Notes d\'import', 
                         validators=[Optional(), Length(max=500)])
    
    submit = SubmitField('Importer le relevé')
    
    def __init__(self, *args, **kwargs):
        super(BankStatementImportForm, self).__init__(*args, **kwargs)
        
        # Charger les comptes bancaires de l'utilisateur
        if current_user.is_authenticated:
            from app.modules.inventory.models_bank_account import BankAccount
            bank_accounts = BankAccount.query.filter_by(
                owner_id=current_user.id, 
                is_active=True
            ).all()
            self.bank_account_id.choices = [
                (account.id, account.name)
                for account in bank_accounts
            ]
