from datetime import datetime, timed<PERSON>ta
import logging
from flask import current_app
from flask_mail import Message
from app.extensions import mail, scheduler
from app.modules.settings.models_settings import Settings
from app.modules.auth.models import User
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from app import db
from app.utils.maintenance import (
    create_backup, cleanup_old_backups,
    update_system_metrics, optimize_database
)

scheduler = BackgroundScheduler()

def init_scheduler(app):
    """Initialize the task scheduler"""
    try:
        if not scheduler.running:
            scheduler.start()
            with app.app_context():
                setup_maintenance_jobs()
    except Exception as e:
        app.logger.error(f"Failed to initialize scheduler: {str(e)}")

def setup_maintenance_jobs():
    """Setup all maintenance jobs for each user"""
    try:
        # Clear existing jobs
        scheduler.remove_all_jobs()
        
        # Setup jobs for each user
        users = User.query.filter_by(role='owner').all()
        for user in users:
            settings = Settings.query.filter_by(owner_id=user.id).first()
            if settings:
                setup_user_jobs(user.id, settings)
    except Exception as e:
        current_app.logger.error(f"Failed to setup maintenance jobs: {str(e)}")

def setup_user_jobs(owner_id, settings):
    """Setup maintenance jobs for a specific user"""
    try:
        # Automated backups
        if settings.backup_enabled:
            # Initialize trigger before using it
            trigger = None
            
            if settings.backup_frequency == 'daily':
                trigger = CronTrigger(
                    hour=settings.backup_time.hour,
                    minute=settings.backup_time.minute
                )
            elif settings.backup_frequency == 'weekly':
                trigger = CronTrigger(
                    day_of_week='mon',
                    hour=settings.backup_time.hour,
                    minute=settings.backup_time.minute
                )
            elif settings.backup_frequency == 'monthly':
                trigger = CronTrigger(
                    day=1,
                    hour=settings.backup_time.hour,
                    minute=settings.backup_time.minute
                )
            
            if trigger:
                scheduler.add_job(
                    create_backup,
                    trigger=trigger,
                    args=[owner_id, True],
                    id=f'backup_{owner_id}',
                    replace_existing=True
                )
                
                # Cleanup old backups daily
                scheduler.add_job(
                    cleanup_old_backups,
                    trigger=CronTrigger(hour=0),
                    args=[owner_id, settings.backup_retention],
                    id=f'cleanup_backups_{owner_id}',
                    replace_existing=True
                )
        
        # System metrics update (every hour)
        scheduler.add_job(
            update_system_metrics,
            trigger=IntervalTrigger(hours=1),
            args=[owner_id],
            id=f'metrics_{owner_id}',
            replace_existing=True
        )
        
        # Database optimization (weekly)
        scheduler.add_job(
            optimize_database,
            trigger=CronTrigger(day_of_week='sun', hour=2),  # Sunday at 2 AM
            args=[owner_id],  # Add missing owner_id argument
            id=f'optimize_db_{owner_id}',
            replace_existing=True
        )
    except Exception as e:
        current_app.logger.error(f"Failed to setup user jobs for owner {owner_id}: {str(e)}")

def update_user_jobs(owner_id):
    """Update jobs for a specific user after settings change"""
    try:
        settings = Settings.query.filter_by(owner_id=owner_id).first()
        if settings:
            # Remove existing jobs safely
            for job_type in ['backup', 'cleanup_backups', 'metrics', 'optimize_db']:
                job_id = f'{job_type}_{owner_id}'
                try:
                    scheduler.remove_job(job_id)
                except:
                    pass  # Job might not exist
            
            # Setup new jobs
            setup_user_jobs(owner_id, settings)
    except Exception as e:
        current_app.logger.error(f"Failed to update user jobs for owner {owner_id}: {str(e)}")

def get_next_run_time(owner_id, job_type):
    """Get the next scheduled run time for a job"""
    try:
        job_id = f'{job_type}_{owner_id}'
        job = scheduler.get_job(job_id)
        return job.next_run_time if job else None
    except Exception as e:
        current_app.logger.error(f"Failed to get next run time for job {job_type}_{owner_id}: {str(e)}")
        return None