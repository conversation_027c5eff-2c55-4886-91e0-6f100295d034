{% extends "employees/base_hr.html" %}

{% block title %}Tableau de bord RH{% endblock %}

{% block hr_content %}
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-chart-dashboard me-2"></i>Tableau de bord RH
                </h1>
                <div>
                    <a href="{{ url_for('employees.index') }}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-users me-1"></i>Liste des employés
                    </a>
                    <a href="{{ url_for('employees.new') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Nouvel Employé
                    </a>
                </div>
            </div>

            <!-- Statistiques principales -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Total Employés
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_employees }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Employés Actifs
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_employees }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Nouvelles Embauches (30j)
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ recent_hires }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Présences Aujourd'hui
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ today_attendances }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>Actions Rapides
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <a href="{{ url_for('employees.new') }}" class="btn btn-primary w-100">
                                        <i class="fas fa-user-plus fa-2x mb-2 d-block"></i>
                                        Ajouter un Employé
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="#" class="btn btn-success w-100" onclick="showAttendanceModal()">
                                        <i class="fas fa-clock fa-2x mb-2 d-block"></i>
                                        Pointage Rapide
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="{{ url_for('employees.payroll_reports') }}" class="btn btn-info w-100">
                                        <i class="fas fa-money-bill-wave fa-2x mb-2 d-block"></i>
                                        Rapports Paie
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="{{ url_for('employees.performance_reports') }}" class="btn btn-warning w-100">
                                        <i class="fas fa-chart-line fa-2x mb-2 d-block"></i>
                                        Évaluations
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Graphiques et statistiques -->
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-pie me-2"></i>Répartition par Statut
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="statusChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-bar me-2"></i>Embauches par Mois
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="hiresChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alertes et notifications -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bell me-2"></i>Alertes et Notifications
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Rappel :</strong> Les évaluations trimestrielles sont dues dans 2 semaines.
                            </div>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Attention :</strong> 3 employés ont des documents qui expirent bientôt.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de pointage rapide -->
<div class="modal fade" id="attendanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Pointage Rapide</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="quickAttendanceForm">
                    <div class="mb-3">
                        <label for="employeeSelect" class="form-label">Sélectionner un employé</label>
                        <select class="form-select" id="employeeSelect" required>
                            <option value="">Choisir un employé...</option>
                            <!-- Options seront chargées dynamiquement -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="actionType" class="form-label">Action</label>
                        <select class="form-select" id="actionType" required>
                            <option value="clock_in">Arrivée</option>
                            <option value="clock_out">Départ</option>
                            <option value="break_start">Début de pause</option>
                            <option value="break_end">Fin de pause</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="submitQuickAttendance()">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
.text-gray-300 {
    color: #dddfeb !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Graphique de répartition par statut
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Actifs', 'Inactifs', 'Suspendus', 'Terminés'],
        datasets: [{
            data: [{{ active_employees }}, 0, 0, {{ total_employees - active_employees }}],
            backgroundColor: ['#1cc88a', '#858796', '#f6c23e', '#e74a3b']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// Graphique des embauches par mois (exemple)
const hiresCtx = document.getElementById('hiresChart').getContext('2d');
const hiresChart = new Chart(hiresCtx, {
    type: 'bar',
    data: {
        labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
        datasets: [{
            label: 'Embauches',
            data: [2, 1, 3, 0, 1, {{ recent_hires }}],
            backgroundColor: '#4e73df'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

function showAttendanceModal() {
    // Charger la liste des employés actifs
    fetch('/employees/api/active-employees')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('employeeSelect');
            select.innerHTML = '<option value="">Choisir un employé...</option>';
            data.forEach(employee => {
                select.innerHTML += `<option value="${employee.id}">${employee.full_name}</option>`;
            });
        });
    
    new bootstrap.Modal(document.getElementById('attendanceModal')).show();
}

function submitQuickAttendance() {
    const form = document.getElementById('quickAttendanceForm');
    const formData = new FormData(form);
    
    // Ici, vous pouvez ajouter la logique pour soumettre le pointage
    alert('Fonctionnalité de pointage rapide à implémenter');
}
</script>
{% endblock %}
