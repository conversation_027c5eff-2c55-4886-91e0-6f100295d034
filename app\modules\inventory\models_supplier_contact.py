from app import db
from datetime import datetime

class SupplierContact(db.Model):
    __tablename__ = 'supplier_contacts'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    supplier_id = db.Column(db.<PERSON>teger, db.<PERSON>ey('suppliers.id'), nullable=False)
    contact_type = db.Column(db.String(50), nullable=False)  # email, phone, meeting, note
    subject = db.Column(db.String(200))
    content = db.Column(db.Text, nullable=False)
    contact_date = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    is_important = db.Column(db.Bo<PERSON>an, default=False)
    follow_up_date = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='completed')  # completed, pending, cancelled
    
    # Relations
    supplier = db.relationship('Supplier', backref='contacts')
    user = db.relationship('User', backref='supplier_contacts')
    
    def __repr__(self):
        return f'<SupplierContact {self.contact_type} - {self.supplier.name}>'
    
    @property
    def contact_type_display(self):
        """Affichage du type de contact"""
        types = {
            'email': 'Email',
            'phone': 'Téléphone',
            'meeting': 'Réunion',
            'note': 'Note',
            'order': 'Commande',
            'complaint': 'Réclamation',
            'quote': 'Devis'
        }
        return types.get(self.contact_type, self.contact_type.title())
    
    @property
    def contact_type_icon(self):
        """Icône du type de contact"""
        icons = {
            'email': 'fas fa-envelope',
            'phone': 'fas fa-phone',
            'meeting': 'fas fa-handshake',
            'note': 'fas fa-sticky-note',
            'order': 'fas fa-shopping-cart',
            'complaint': 'fas fa-exclamation-triangle',
            'quote': 'fas fa-file-invoice-dollar'
        }
        return icons.get(self.contact_type, 'fas fa-comment')
    
    @property
    def status_badge_class(self):
        """Classe CSS pour le badge de statut"""
        classes = {
            'completed': 'bg-success',
            'pending': 'bg-warning',
            'cancelled': 'bg-danger'
        }
        return classes.get(self.status, 'bg-secondary')
    
    @property
    def is_overdue(self):
        """Vérifie si le suivi est en retard"""
        if self.follow_up_date and self.status == 'pending':
            return datetime.utcnow() > self.follow_up_date
        return False
