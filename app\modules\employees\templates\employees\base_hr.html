{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar RH -->
        <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
            <div class="position-sticky pt-3">
                <div class="sidebar-header mb-3">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span><i class="fas fa-users me-2"></i>Ressources Humaines</span>
                    </h6>
                </div>

                <!-- Navigation principale -->
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'employees.dashboard' }}" 
                           href="{{ url_for('employees.dashboard') }}">
                            <i class="fas fa-chart-dashboard me-2"></i>
                            Tableau de bord
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'employees.index' }}" 
                           href="{{ url_for('employees.index') }}">
                            <i class="fas fa-users me-2"></i>
                            Liste des Employés
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'employees.new' }}" 
                           href="{{ url_for('employees.new') }}">
                            <i class="fas fa-user-plus me-2"></i>
                            Nouvel Employé
                        </a>
                    </li>
                </ul>

                <!-- Gestion des présences -->
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span><i class="fas fa-clock me-2"></i>Présences & Plannings</span>
                </h6>
                <ul class="nav flex-column mb-2">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showQuickAttendance()">
                            <i class="fas fa-stopwatch me-2"></i>
                            Pointage Rapide
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showTodayAttendances()">
                            <i class="fas fa-calendar-day me-2"></i>
                            Présences du Jour
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showWeeklySchedules()">
                            <i class="fas fa-calendar-week me-2"></i>
                            Planning de la Semaine
                        </a>
                    </li>
                </ul>

                <!-- Paie et finances -->
                {% if current_user.can_view_employee_reports %}
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span><i class="fas fa-money-bill-wave me-2"></i>Paie & Finances</span>
                </h6>
                <ul class="nav flex-column mb-2">
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'employees.payroll_reports' }}" 
                           href="{{ url_for('employees.payroll_reports') }}">
                            <i class="fas fa-chart-bar me-2"></i>
                            Rapports de Paie
                        </a>
                    </li>
                    {% if current_user.can_manage_payroll %}
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPayrollActions()">
                            <i class="fas fa-calculator me-2"></i>
                            Calculs de Paie
                        </a>
                    </li>
                    {% endif %}
                </ul>
                {% endif %}

                <!-- Évaluations -->
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span><i class="fas fa-star me-2"></i>Évaluations</span>
                </h6>
                <ul class="nav flex-column mb-2">
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'employees.performance_reports' }}" 
                           href="{{ url_for('employees.performance_reports') }}">
                            <i class="fas fa-chart-line me-2"></i>
                            Rapports de Performance
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPendingEvaluations()">
                            <i class="fas fa-clipboard-list me-2"></i>
                            Évaluations en Attente
                        </a>
                    </li>
                </ul>

                <!-- Actions rapides -->
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span><i class="fas fa-bolt me-2"></i>Actions Rapides</span>
                </h6>
                <ul class="nav flex-column mb-2">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="exportEmployeeData()">
                            <i class="fas fa-download me-2"></i>
                            Exporter Données
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="generateReports()">
                            <i class="fas fa-file-pdf me-2"></i>
                            Générer Rapports
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="bulkActions()">
                            <i class="fas fa-tasks me-2"></i>
                            Actions en Lot
                        </a>
                    </li>
                </ul>

                <!-- Statistiques rapides -->
                <div class="mt-4 px-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body p-2">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="mb-0" id="totalEmployees">-</h6>
                                    <small>Employés Actifs</small>
                                </div>
                                <i class="fas fa-users fa-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-2 px-3">
                    <div class="card bg-success text-white">
                        <div class="card-body p-2">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="mb-0" id="todayPresent">-</h6>
                                    <small>Présents Aujourd'hui</small>
                                </div>
                                <i class="fas fa-check-circle fa-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Contenu principal -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            {% block hr_content %}
            <!-- Le contenu spécifique à chaque page RH sera inséré ici -->
            {% endblock %}
        </main>
    </div>
</div>

<!-- Modals pour actions rapides -->
<div class="modal fade" id="quickAttendanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Pointage Rapide</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="quickAttendanceForm">
                    <div class="mb-3">
                        <label for="employeeSelect" class="form-label">Employé</label>
                        <select class="form-select" id="employeeSelect" required>
                            <option value="">Sélectionner un employé...</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="actionType" class="form-label">Action</label>
                        <select class="form-select" id="actionType" required>
                            <option value="clock_in">Arrivée</option>
                            <option value="clock_out">Départ</option>
                            <option value="break_start">Début de pause</option>
                            <option value="break_end">Fin de pause</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="submitQuickAttendance()">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

<style>
.sidebar {
    position: fixed;
    top: 56px; /* Hauteur de la navbar */
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    overflow-y: auto;
}

.sidebar .nav-link {
    color: #333;
    padding: 0.75rem 1rem;
    border-radius: 0;
}

.sidebar .nav-link:hover {
    background-color: #f8f9fa;
    color: #007bff;
}

.sidebar .nav-link.active {
    background-color: #007bff;
    color: white;
}

.sidebar-heading {
    font-size: 0.75rem;
    text-transform: uppercase;
}

@media (max-width: 767.98px) {
    .sidebar {
        position: relative;
        top: 0;
    }
}
</style>

<script>
// Charger les statistiques de la sidebar
document.addEventListener('DOMContentLoaded', function() {
    loadSidebarStats();
});

function loadSidebarStats() {
    fetch('/employees/api/stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('totalEmployees').textContent = data.total_employees || '-';
            document.getElementById('todayPresent').textContent = data.today_present || '-';
        })
        .catch(error => console.error('Erreur lors du chargement des statistiques:', error));
}

function showQuickAttendance() {
    // Charger la liste des employés actifs
    fetch('/employees/api/active-employees')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('employeeSelect');
            select.innerHTML = '<option value="">Sélectionner un employé...</option>';
            data.forEach(employee => {
                select.innerHTML += `<option value="${employee.id}">${employee.full_name}</option>`;
            });
        });
    
    new bootstrap.Modal(document.getElementById('quickAttendanceModal')).show();
}

function submitQuickAttendance() {
    const employeeId = document.getElementById('employeeSelect').value;
    const actionType = document.getElementById('actionType').value;

    if (!employeeId || !actionType) {
        alert('Veuillez sélectionner un employé et une action.');
        return;
    }

    // Envoyer la requête à l'API
    fetch('/employees/api/quick-attendance', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || ''
        },
        body: JSON.stringify({
            employee_id: employeeId,
            action_type: actionType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`${data.message}\nHeure: ${data.time}\nDate: ${data.date}`);

            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('quickAttendanceModal'));
            modal.hide();

            // Recharger les statistiques
            loadSidebarStats();
        } else {
            alert('Erreur: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur lors de l\'enregistrement du pointage');
    });
}

function showTodayAttendances() {
    window.location.href = '/employees/attendance/today';
}

function showWeeklySchedules() {
    window.location.href = '/employees/schedules/week';
}

function showPayrollActions() {
    window.location.href = '/employees/payroll/actions';
}

function showPendingEvaluations() {
    window.location.href = '/employees/performance/pending';
}

function exportEmployeeData() {
    window.open('/employees/export/all', '_blank');
}

function generateReports() {
    window.open('/employees/reports/generate', '_blank');
}

function bulkActions() {
    // Rediriger vers une page d'actions en lot
    window.location.href = '/employees/bulk-actions';
}
</script>
{% endblock %}
