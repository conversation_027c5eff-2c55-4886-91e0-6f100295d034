<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tous les Restaurants - Plateforme de Commande</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #4e73df;
            --secondary-color: #858796;
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 80px 0;
        }
        
        .business-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            border: none;
            border-radius: 15px;
            overflow: hidden;
        }
        
        .business-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        
        .business-type-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            z-index: 2;
        }
        
        .filter-btn {
            margin: 5px;
            border-radius: 25px;
        }
        
        .filter-btn.active {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }
        
        .business-image {
            height: 200px;
            object-fit: cover;
            width: 100%;
        }
        
        .business-placeholder {
            height: 200px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark shadow">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#">
                <i class="fas fa-store me-2"></i>Tous les Restaurants
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    {% if current_user.is_authenticated and current_user.is_system_admin %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.index') }}">
                                <i class="fas fa-cog me-1"></i>Administration
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section text-center">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">
                <i class="fas fa-utensils me-3"></i>
                Découvrez nos Restaurants Partenaires
            </h1>
            <p class="lead mb-5">
                Commandez en ligne dans vos restaurants préférés et faites-vous livrer à domicile
            </p>
            
            <!-- Search Bar -->
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="input-group input-group-lg">
                        <input type="text" class="form-control" id="searchInput" 
                               placeholder="Rechercher un restaurant...">
                        <button class="btn btn-light" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Filters Section -->
    <section class="py-4 bg-light">
        <div class="container">
            <div class="text-center">
                <h5 class="mb-3">Filtrer par type :</h5>
                <button class="btn btn-outline-primary filter-btn active" data-filter="all">
                    <i class="fas fa-th me-1"></i>Tous
                </button>
                {% for business_type in businesses_by_type.keys() %}
                    <button class="btn btn-outline-primary filter-btn" data-filter="{{ business_type.lower() }}">
                        {% if business_type == 'Restaurant' %}
                            <i class="fas fa-utensils me-1"></i>
                        {% elif business_type == 'Café' %}
                            <i class="fas fa-coffee me-1"></i>
                        {% elif business_type == 'Boulangerie' %}
                            <i class="fas fa-bread-slice me-1"></i>
                        {% elif business_type == 'Pizzeria' %}
                            <i class="fas fa-pizza-slice me-1"></i>
                        {% else %}
                            <i class="fas fa-store me-1"></i>
                        {% endif %}
                        {{ business_type }}
                    </button>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Businesses Section -->
    <section class="py-5">
        <div class="container">
            {% for business_type, businesses in businesses_by_type.items() %}
                <div class="business-category" data-category="{{ business_type.lower() }}">
                    <h2 class="mb-4">
                        {% if business_type == 'Restaurant' %}
                            <i class="fas fa-utensils me-2 text-primary"></i>
                        {% elif business_type == 'Café' %}
                            <i class="fas fa-coffee me-2 text-primary"></i>
                        {% elif business_type == 'Boulangerie' %}
                            <i class="fas fa-bread-slice me-2 text-primary"></i>
                        {% elif business_type == 'Pizzeria' %}
                            <i class="fas fa-pizza-slice me-2 text-primary"></i>
                        {% else %}
                            <i class="fas fa-store me-2 text-primary"></i>
                        {% endif %}
                        {{ business_type }}s
                    </h2>
                    
                    <div class="row">
                        {% for business in businesses %}
                            <div class="col-lg-4 col-md-6 mb-4 business-item" 
                                 data-name="{{ business.settings.business_name.lower() }}"
                                 data-type="{{ business_type.lower() }}">
                                <div class="card business-card h-100 shadow-sm position-relative"
                                     onclick="window.open('http://{{ business.site.subdomain }}.lvh.me:5000', '_blank')">
                                    
                                    <!-- Business Type Badge -->
                                    <span class="badge bg-primary business-type-badge">
                                        {{ business_type }}
                                    </span>
                                    
                                    <!-- Business Image -->
                                    {% if business.site.banner_path %}
                                        <img src="{{ url_for('static', filename='uploads/' + business.site.banner_path) }}" 
                                             class="business-image" alt="{{ business.settings.business_name }}">
                                    {% else %}
                                        <div class="business-placeholder">
                                            <i class="fas fa-utensils fa-4x"></i>
                                        </div>
                                    {% endif %}
                                    
                                    <div class="card-body">
                                        <h5 class="card-title fw-bold">
                                            {{ business.settings.business_name or business.site.site_name }}
                                        </h5>
                                        
                                        {% if business.site.site_description %}
                                            <p class="card-text text-muted">
                                                {{ business.site.site_description[:100] }}...
                                            </p>
                                        {% endif %}
                                        
                                        <!-- Services Available -->
                                        <div class="mb-3">
                                            {% if business.site.allow_delivery %}
                                                <span class="badge bg-success me-1">
                                                    <i class="fas fa-truck me-1"></i>Livraison
                                                </span>
                                            {% endif %}
                                            {% if business.site.allow_pickup %}
                                                <span class="badge bg-info me-1">
                                                    <i class="fas fa-shopping-bag me-1"></i>À emporter
                                                </span>
                                            {% endif %}
                                            {% if business.site.allow_dine_in %}
                                                <span class="badge bg-warning me-1">
                                                    <i class="fas fa-chair me-1"></i>Sur place
                                                </span>
                                            {% endif %}
                                        </div>
                                        
                                        <!-- Address -->
                                        {% if business.settings.address %}
                                            <p class="card-text small text-muted">
                                                <i class="fas fa-map-marker-alt me-1"></i>
                                                {{ business.settings.address[:50] }}...
                                            </p>
                                        {% endif %}
                                        
                                        <!-- Delivery Info -->
                                        {% if business.site.allow_delivery %}
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    {% if business.site.delivery_fee > 0 %}
                                                        Livraison: {{ "%.2f"|format(business.site.delivery_fee) }}€
                                                    {% else %}
                                                        Livraison gratuite
                                                    {% endif %}
                                                </small>
                                                {% if business.site.minimum_order > 0 %}
                                                    <small class="text-muted">
                                                        Min: {{ "%.2f"|format(business.site.minimum_order) }}€
                                                    </small>
                                                {% endif %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="card-footer bg-transparent border-0">
                                        <div class="d-grid">
                                            <span class="btn btn-primary">
                                                <i class="fas fa-external-link-alt me-2"></i>
                                                Voir le menu
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endfor %}
            
            {% if not businesses_by_type %}
                <div class="text-center py-5">
                    <i class="fas fa-store fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">Aucun restaurant disponible</h3>
                    <p class="text-muted">Les restaurants apparaîtront ici une fois qu'ils auront activé leur site de commande en ligne.</p>
                </div>
            {% endif %}
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-store me-2"></i>Plateforme de Commande</h5>
                    <p class="text-muted">Votre solution complète pour commander en ligne</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">
                        © 2024 - Tous droits réservés
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const filterBtns = document.querySelectorAll('.filter-btn');
            const businessCategories = document.querySelectorAll('.business-category');
            const searchInput = document.getElementById('searchInput');
            
            // Filter by category
            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // Update active button
                    filterBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    const filter = this.getAttribute('data-filter');
                    
                    businessCategories.forEach(category => {
                        if (filter === 'all' || category.getAttribute('data-category') === filter) {
                            category.style.display = 'block';
                        } else {
                            category.style.display = 'none';
                        }
                    });
                });
            });
            
            // Search functionality
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const businessItems = document.querySelectorAll('.business-item');
                
                businessItems.forEach(item => {
                    const businessName = item.getAttribute('data-name');
                    if (businessName.includes(searchTerm)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>
