from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.modules.promotions.models_promotion import Promotion, PromotionType
from app import db
from app.modules.promotions.forms_promotion import PromotionForm
from datetime import datetime
from app.utils.decorators import permission_required
from app.modules.inventory.models_product import Product

from . import bp

@bp.route('/')
@login_required
@permission_required('can_manage_promotions')
def index():
    owner_id = current_user.get_owner_id
    promotions = Promotion.query.filter_by(owner_id=owner_id).order_by(Promotion.start_date.desc()).all()
    return render_template('promotions/index.html', promotions=promotions)

@bp.route('/new', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_promotions')
def new():
    form = PromotionForm()
    if form.validate_on_submit():
        promotion = Promotion(
            name=form.name.data,
            description=form.description.data,
            promotion_type=form.promotion_type.data,
            value=form.value.data,
            start_date=form.start_date.data,
            end_date=form.end_date.data,
            min_purchase=form.min_purchase.data,
            max_discount=form.max_discount.data,
            usage_limit=form.usage_limit.data,
            is_active=form.is_active.data,
            owner_id=current_user.get_owner_id
        )
        if form.promotion_type.data == PromotionType.BUY_X_GET_Y:
            promotion.buy_x = form.buy_x.data
            promotion.get_y = form.get_y.data
            
        db.session.add(promotion)
        db.session.commit()
        
        if form.products.data:
            for product_id in form.products.data:
                product = Product.query.get(product_id)
                if product:
                    promotion.products.append(product)
            db.session.commit()
            
        flash('Promotion créée avec succès!', 'success')
        return redirect(url_for('promotions.index'))
    return render_template('promotions/form.html', form=form, title="Nouvelle Promotion")

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_promotions')
def edit(id):
    promotion = Promotion.query.get_or_404(id)
    form = PromotionForm(obj=promotion)
    
    if form.validate_on_submit():
        promotion.name = form.name.data
        promotion.description = form.description.data
        promotion.promotion_type = form.promotion_type.data
        promotion.value = form.value.data
        promotion.start_date = form.start_date.data
        promotion.end_date = form.end_date.data
        promotion.min_purchase = form.min_purchase.data
        promotion.max_discount = form.max_discount.data
        promotion.usage_limit = form.usage_limit.data
        promotion.is_active = form.is_active.data
        
        if form.promotion_type.data == PromotionType.BUY_X_GET_Y:
            promotion.buy_x = form.buy_x.data
            promotion.get_y = form.get_y.data
            
        promotion.products = []
        if form.products.data:
            for product_id in form.products.data:
                product = Product.query.get(product_id)
                if product:
                    promotion.products.append(product)
                
        db.session.commit()
        flash('Promotion mise à jour avec succès!', 'success')
        return redirect(url_for('promotions.index'))
        
    return render_template('promotions/form.html', form=form, promotion=promotion, title="Modifier Promotion")

@bp.route('/<int:id>')
@login_required
@permission_required('can_manage_promotions')
def show(id):
    promotion = Promotion.query.get_or_404(id)
    return render_template('promotions/show.html', 
                         promotion=promotion,
                         datetime=datetime)

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
@permission_required('can_manage_promotions')
def delete(id):
    try:
        promotion = Promotion.query.get_or_404(id)
        db.session.delete(promotion)
        db.session.commit()
        flash('Promotion supprimée avec succès!', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Erreur lors de la suppression de la promotion.', 'error')
        print(f"Erreur de suppression: {str(e)}")
    return redirect(url_for('promotions.index'))

@bp.route('/<int:id>/toggle', methods=['POST'])
@login_required
@permission_required('can_manage_promotions')
def toggle(id):
    promotion = Promotion.query.get_or_404(id)
    promotion.is_active = not promotion.is_active
    db.session.commit()
    return jsonify({'success': True, 'is_active': promotion.is_active}) 