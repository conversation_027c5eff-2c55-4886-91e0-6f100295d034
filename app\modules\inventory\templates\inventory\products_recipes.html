{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-clipboard-list"></i>
                Fiches Techniques
            </h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ url_for('inventory.add_product') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Nouveau produit
            </a>
        </div>
    </div>
    
    <!-- Filtres -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" class="row align-items-center">
                <div class="col-md-3 mb-2">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search }}" placeholder="Rechercher...">
                        <button type="submit" class="btn btn-primary">
                            Rechercher
                        </button>
                    </div>
                </div>
                <div class="col-md-3 mb-2">
                    <select class="form-select" id="category_id" name="category_id" onchange="this.form.submit()">
                        <option value="">Toutes les catégories</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if selected_category == category.id %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-2">
                    <select class="form-select" id="sort" name="sort" onchange="this.form.submit()">
                        <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Nom</option>
                        <option value="price" {% if sort_by == 'price' %}selected{% endif %}>Prix</option>
                        <option value="category" {% if sort_by == 'category' %}selected{% endif %}>Catégorie</option>
                    </select>
                </div>
                <div class="col-md-2 mb-2">
                    <select class="form-select" id="order" name="order" onchange="this.form.submit()">
                        <option value="asc" {% if sort_order == 'asc' %}selected{% endif %}>Ascendant</option>
                        <option value="desc" {% if sort_order == 'desc' %}selected{% endif %}>Descendant</option>
                    </select>
                </div>
                <div class="col-md-1 mb-2">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="active_only" name="active_only" 
                               value="true" {% if active_only %}checked{% endif %} onchange="this.form.submit()">
                        <label class="form-check-label" for="active_only">
                            Actifs
                        </label>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des fiches techniques -->
    <div class="card shadow mb-4">
        <div class="card-header py-3" style="background-color: #c9c9c9;">
            <h6 class="m-0 font-weight-bold text-primary">
                Produits avec fiches techniques ({{ pagination.total }})
            </h6>
        </div>
        <div class="card-body" style="background-color: #c1ddae;">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead style="background-color: #c1ddae;">
                        <tr>
                            <th>Produit</th>
                            <th>Catégorie</th>
                            <th>Prix de vente</th>
                            <th>Coût de production</th>
                            <th>Marge</th>
                            <th>Stock disponible</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products %}
                        <tr>
                            <td>{{ product.name }}</td>
                            <td>{{ product.category.name if product.category else '-' }}</td>
                            <td>{{ "%.2f"|format(product.price) }} €</td>
                            <td>{{ "%.2f"|format(product.recipe.calculate_cost()) if product.recipe else '-' }} €</td>
                            <td>
                                {% if product.recipe %}
                                    {% set cost = product.recipe.calculate_cost() %}
                                    {% set margin = ((product.price - cost) / product.price * 100) if product.price > 0 else 0 %}
                                    {{ "%.1f"|format(margin) }}%
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                {% if product.recipe %}
                                    {% set possible_qty = product.recipe.get_possible_quantity() %}
                                    <span class="badge {% if possible_qty > product.minimum_stock %}bg-success{% elif possible_qty > 0 %}bg-warning{% else %}bg-danger{% endif %}">
                                        {{ possible_qty }} {{ product.unit }}
                                    </span>
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('inventory.recipe', product_id=product.id) }}" 
                                   class="btn btn-primary btn-sm">
                                    <i class="fas fa-edit"></i> Modifier
                                </a>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="7" class="text-center">
                                <p class="text-muted mb-0">Aucun produit avec fiche technique</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="row mt-4">
                <div class="col-12">
                    <nav aria-label="Navigation des fiches techniques">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('inventory.products_recipes', page=pagination.prev_num, per_page=per_page, search=search, category_id=selected_category, sort=sort_by, order=sort_order, active_only='true' if active_only else '') }}">Précédent</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Précédent</span>
                            </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages(left_edge=2, right_edge=2, left_current=1, right_current=2) %}
                                {% if page_num %}
                                    {% if page_num == pagination.page %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('inventory.products_recipes', page=page_num, per_page=per_page, search=search, category_id=selected_category, sort=sort_by, order=sort_order, active_only='true' if active_only else '') }}">{{ page_num }}</a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('inventory.products_recipes', page=pagination.next_num, per_page=per_page, search=search, category_id=selected_category, sort=sort_by, order=sort_order, active_only='true' if active_only else '') }}">Suivant</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Suivant</span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 