from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.modules.tables.models_table import Room, Table, TableStatus
from app.modules.rooms.forms_room import RoomForm, TablePositionForm, TableAppearanceForm
from app import db
from app.utils.decorators import permission_required

from . import bp

@bp.route('/')
@login_required
@permission_required('can_manage_tables')
def index():
    """Liste des salles"""
    owner_id = current_user.get_owner_id
    rooms = Room.query.filter_by(owner_id=owner_id, is_active=True).order_by(Room.name).all()
    return render_template('rooms/index.html', rooms=rooms)

@bp.route('/new', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_tables')
def new():
    """Créer une nouvelle salle"""
    form = RoomForm()
    if form.validate_on_submit():
        # Si c'est la salle par défaut, désactiver les autres
        if form.is_default.data:
            Room.query.filter_by(owner_id=current_user.id, is_default=True).update({'is_default': False})
        
        room = Room(
            owner_id=current_user.id,
            name=form.name.data,
            description=form.description.data,
            width=form.width.data,
            height=form.height.data,
            background_color=form.background_color.data,
            is_default=form.is_default.data,
            is_active=form.is_active.data
        )
        db.session.add(room)
        db.session.commit()
        flash('Salle créée avec succès!', 'success')
        return redirect(url_for('rooms.index'))
    return render_template('rooms/form.html', form=form, title="Nouvelle Salle")

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_tables')
def edit(id):
    """Modifier une salle"""
    room = Room.query.get_or_404(id)
    if room.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('rooms.index'))
    
    form = RoomForm(obj=room)
    if form.validate_on_submit():
        # Si c'est la salle par défaut, désactiver les autres
        if form.is_default.data and not room.is_default:
            Room.query.filter_by(owner_id=current_user.id, is_default=True).update({'is_default': False})
        
        room.name = form.name.data
        room.description = form.description.data
        room.width = form.width.data
        room.height = form.height.data
        room.background_color = form.background_color.data
        room.is_default = form.is_default.data
        room.is_active = form.is_active.data
        
        db.session.commit()
        flash('Salle mise à jour avec succès!', 'success')
        return redirect(url_for('rooms.index'))
    return render_template('rooms/form.html', form=form, room=room, title="Modifier Salle")

@bp.route('/<int:id>')
@login_required
@permission_required('can_manage_tables')
def show(id):
    """Afficher le plan d'une salle"""
    room = Room.query.get_or_404(id)
    if room.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('rooms.index'))
    
    tables = Table.query.filter_by(room_id=room.id).all()
    return render_template('rooms/layout.html', room=room, tables=tables)

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
@permission_required('can_manage_tables')
def delete(id):
    """Supprimer une salle"""
    room = Room.query.get_or_404(id)
    if room.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('rooms.index'))
    
    # Vérifier s'il y a des tables avec des commandes en cours
    occupied_tables = Table.query.filter_by(room_id=room.id, status=TableStatus.OCCUPIED).count()
    if occupied_tables > 0:
        flash('Impossible de supprimer une salle avec des tables occupées!', 'error')
        return redirect(url_for('rooms.index'))
    
    db.session.delete(room)
    db.session.commit()
    flash('Salle supprimée avec succès!', 'success')
    return redirect(url_for('rooms.index'))

@bp.route('/api/update_table_position', methods=['POST'])
@login_required
@permission_required('can_manage_tables')
def update_table_position():
    """API pour mettre à jour la position d'une table"""
    data = request.get_json()
    table_id = data.get('table_id')
    position_x = data.get('position_x')
    position_y = data.get('position_y')
    
    table = Table.query.get_or_404(table_id)
    if table.owner_id != current_user.get_owner_id:
        return jsonify({'success': False, 'message': 'Accès non autorisé'}), 403
    
    table.update_position(position_x, position_y)
    return jsonify({'success': True, 'message': 'Position mise à jour'})

@bp.route('/api/update_table_appearance', methods=['POST'])
@login_required
@permission_required('can_manage_tables')
def update_table_appearance():
    """API pour mettre à jour l'apparence d'une table"""
    data = request.get_json()
    table_id = data.get('table_id')
    
    table = Table.query.get_or_404(table_id)
    if table.owner_id != current_user.get_owner_id:
        return jsonify({'success': False, 'message': 'Accès non autorisé'}), 403
    
    table.update_appearance(
        shape=data.get('shape'),
        size=data.get('size'),
        color=data.get('color')
    )
    return jsonify({'success': True, 'message': 'Apparence mise à jour'})

@bp.route('/api/get_room_data/<int:room_id>')
@login_required
@permission_required('can_process_sales')
def get_room_data(room_id):
    """API pour récupérer les données d'une salle (pour le POS)"""
    room = Room.query.get_or_404(room_id)
    if room.owner_id != current_user.get_owner_id:
        return jsonify({'success': False, 'message': 'Accès non autorisé'}), 403
    
    tables_data = []
    for table in room.tables:
        tables_data.append({
            'id': table.id,
            'number': table.number,
            'capacity': table.capacity,
            'status': table.status,
            'current_covers': table.current_covers,
            'current_amount': table.current_amount,
            'remaining_amount': table.remaining_amount,
            'position_x': table.position_x,
            'position_y': table.position_y,
            'table_shape': table.table_shape,
            'table_size': table.table_size,
            'table_color': table.table_color,
            'table_image': table.table_image,
            'use_image': table.use_image,
            'image_url': table.image_url,
            'display_type': table.display_type,
            'display_name': table.display_name
        })
    
    return jsonify({
        'success': True,
        'room': {
            'id': room.id,
            'name': room.name,
            'width': room.width,
            'height': room.height,
            'background_color': room.background_color
        },
        'tables': tables_data
    })
