from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError, Optional
from app.modules.auth.models import User

class LoginForm(FlaskForm):
    username = String<PERSON>ield('Nom d\'utilisateur', validators=[DataRequired()])
    password = PasswordField('Mot de passe', validators=[DataRequired()])
    remember_me = BooleanField('Se souvenir de moi')
    submit = SubmitField('Se connecter')

class RegistrationForm(FlaskForm):
    username = <PERSON><PERSON>ield('Nom d\'utilisateur', validators=[
        DataRequired(),
        Length(min=2, max=64)
    ])
    email = StringField('Email', validators=[
        DataRequired(),
        Email(),
        Length(max=120)
    ])
    password = PasswordField('Mot de passe', validators=[
        DataRequired(),
        Length(min=6, message='Le mot de passe doit contenir au moins 6 caractères')
    ])
    password2 = PasswordField('Répéter le mot de passe', validators=[
        DataRequired(),
        EqualTo('password', message='Les mots de passe doivent être identiques')
    ])
    submit = SubmitField('S\'inscrire')

    def validate_username(self, field):
        user = User.query.filter_by(username=field.data).first()
        if user is not None:
            raise ValidationError('Ce nom d\'utilisateur est déjà utilisé.')

    def validate_email(self, field):
        user = User.query.filter_by(email=field.data).first()
        if user is not None:
            raise ValidationError('Cette adresse email est déjà utilisée.')

class ResetPasswordRequestForm(FlaskForm):
    email = StringField('Email', validators=[DataRequired(), Email()])
    submit = SubmitField('Demander la réinitialisation')

class ResetPasswordForm(FlaskForm):
    password = PasswordField('Nouveau mot de passe', validators=[
        DataRequired(),
        Length(min=6, message='Le mot de passe doit contenir au moins 6 caractères')
    ])
    password2 = PasswordField('Répéter le mot de passe', validators=[
        DataRequired(),
        EqualTo('password', message='Les mots de passe doivent être identiques')
    ])
    submit = SubmitField('Réinitialiser le mot de passe')

class ProfileForm(FlaskForm):
    username = StringField('Nom d\'utilisateur', validators=[
        DataRequired(),
        Length(min=2, max=64)
    ])
    email = StringField('Email', validators=[
        DataRequired(),
        Email()
    ])
    current_password = PasswordField('Mot de passe actuel')
    new_password = PasswordField('Nouveau mot de passe', validators=[
        Optional(),
        Length(min=6, message='Le mot de passe doit contenir au moins 6 caractères')
    ])
    confirm_password = PasswordField('Confirmer le nouveau mot de passe', validators=[
        EqualTo('new_password', message='Les mots de passe doivent correspondre')
    ])
    submit = SubmitField('Enregistrer les modifications')

    def __init__(self, user, *args, **kwargs):
        super(ProfileForm, self).__init__(*args, **kwargs)
        self.user = user

    def validate_username(self, field):
        if field.data != self.user.username:
            user = User.query.filter_by(username=field.data).first()
            if user is not None:
                raise ValidationError('Ce nom d\'utilisateur est déjà utilisé.')

    def validate_email(self, field):
        if field.data != self.user.email:
            user = User.query.filter_by(email=field.data).first()
            if user is not None:
                raise ValidationError('Cette adresse email est déjà utilisée.')

    def validate_current_password(self, field):
        if self.new_password.data and not self.user.check_password(field.data):
            raise ValidationError('Mot de passe incorrect.') 