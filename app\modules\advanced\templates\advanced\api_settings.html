{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">API & Intégrations</h1>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- API Keys Card -->
        <div class="col-xl-6 col-md-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Clés API</h6>
                    <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#newApiKeyModal">
                        <i class="fas fa-plus fa-sm"></i> Nouvelle clé
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Clé</th>
                                    <th>Créée le</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="4" class="text-center">Aucune clé API</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Webhooks Card -->
        <div class="col-xl-6 col-md-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Webhooks</h6>
                    <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#newWebhookModal">
                        <i class="fas fa-plus fa-sm"></i> Nouveau webhook
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>URL</th>
                                    <th>Événement</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="4" class="text-center">Aucun webhook configuré</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Intégrations Card -->
        <div class="col-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Intégrations disponibles</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Stripe Integration -->
                        <div class="col-xl-4 col-md-6 mb-4">
                            <div class="card border-left-primary h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">Stripe</div>
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                Paiements en ligne</div>
                                        </div>
                                        <div class="col-auto">
                                            <button class="btn btn-primary btn-sm">Configurer</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Google Analytics Integration -->
                        <div class="col-xl-4 col-md-6 mb-4">
                            <div class="card border-left-success h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">Google Analytics</div>
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                Analytique</div>
                                        </div>
                                        <div class="col-auto">
                                            <button class="btn btn-success btn-sm">Configurer</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Mailchimp Integration -->
                        <div class="col-xl-4 col-md-6 mb-4">
                            <div class="card border-left-info h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">Mailchimp</div>
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                Marketing par email</div>
                                        </div>
                                        <div class="col-auto">
                                            <button class="btn btn-info btn-sm">Configurer</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New API Key Modal -->
<div class="modal fade" id="newApiKeyModal" tabindex="-1" aria-labelledby="newApiKeyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newApiKeyModalLabel">Nouvelle clé API</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="apiKeyName" class="form-label">Nom de la clé</label>
                        <input type="text" class="form-control" id="apiKeyName" required>
                    </div>
                    <div class="mb-3">
                        <label for="apiKeyExpiration" class="form-label">Expiration</label>
                        <select class="form-select" id="apiKeyExpiration">
                            <option value="never">Jamais</option>
                            <option value="30">30 jours</option>
                            <option value="90">90 jours</option>
                            <option value="365">1 an</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary">Générer</button>
            </div>
        </div>
    </div>
</div>

<!-- New Webhook Modal -->
<div class="modal fade" id="newWebhookModal" tabindex="-1" aria-labelledby="newWebhookModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newWebhookModalLabel">Nouveau webhook</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="webhookUrl" class="form-label">URL du webhook</label>
                        <input type="url" class="form-control" id="webhookUrl" required>
                    </div>
                    <div class="mb-3">
                        <label for="webhookEvent" class="form-label">Événement</label>
                        <select class="form-select" id="webhookEvent">
                            <option value="sale.created">Nouvelle vente</option>
                            <option value="sale.updated">Mise à jour vente</option>
                            <option value="stock.low">Stock bas</option>
                            <option value="customer.created">Nouveau client</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="webhookSecret" class="form-label">Secret (optionnel)</label>
                        <input type="text" class="form-control" id="webhookSecret">
                        <div class="form-text">Utilisé pour vérifier l'authenticité des requêtes</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary">Créer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %} 