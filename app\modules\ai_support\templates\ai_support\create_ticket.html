{% extends "ai_support/base.html" %}

{% block support_content %}
<div class="row">
    <div class="col-12">
        <h2><PERSON>réer un nouveau ticket de support</h2>
        <p class="text-muted">Décrivez votre problème ou votre question. Notre IA analysera votre demande et vous fournira une assistance personnalisée.</p>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ticket-alt"></i> Informations du ticket
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.title.label(class="form-label") }}
                        {{ form.title(class="form-control" + (" is-invalid" if form.title.errors else "")) }}
                        {% if form.title.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.title.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Résumez votre problème en quelques mots</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.category.label(class="form-label") }}
                                {{ form.category(class="form-select" + (" is-invalid" if form.category.errors else "")) }}
                                {% if form.category.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.category.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.priority.label(class="form-label") }}
                                {{ form.priority(class="form-select" + (" is-invalid" if form.priority.errors else "")) }}
                                {% if form.priority.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.priority.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="6") }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Décrivez votre problème en détail. Plus vous donnez d'informations, mieux notre IA pourra vous aider.
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('ai_support.tickets') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Retour
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Conseils -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb"></i> Conseils pour un bon ticket
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success"></i>
                        <strong>Titre clair :</strong> Résumez le problème en une phrase
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success"></i>
                        <strong>Description détaillée :</strong> Expliquez ce qui ne fonctionne pas
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success"></i>
                        <strong>Étapes à reproduire :</strong> Comment reproduire le problème
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success"></i>
                        <strong>Messages d'erreur :</strong> Copiez les messages exacts
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success"></i>
                        <strong>Contexte :</strong> Quand le problème a-t-il commencé ?
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Catégories d'aide -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-question-circle"></i> Besoin d'aide ?
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('ai_support.chat') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-comments"></i> Chat en direct
                    </a>
                    <a href="{{ url_for('ai_support.knowledge_base') }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-book"></i> Base de connaissances
                    </a>
                </div>
                
                <hr>
                
                <h6>Questions fréquentes :</h6>
                <ul class="list-unstyled small">
                    <li><a href="#" class="text-decoration-none">Comment utiliser la caisse ?</a></li>
                    <li><a href="#" class="text-decoration-none">Gérer l'inventaire</a></li>
                    <li><a href="#" class="text-decoration-none">Problèmes de connexion</a></li>
                    <li><a href="#" class="text-decoration-none">Générer des rapports</a></li>
                </ul>
            </div>
        </div>
        
        <!-- Priorités -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-flag"></i> Guide des priorités
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <span class="badge bg-success">Faible</span>
                    <small class="text-muted d-block">Questions générales, demandes d'information</small>
                </div>
                <div class="mb-2">
                    <span class="badge bg-warning">Moyenne</span>
                    <small class="text-muted d-block">Problèmes qui affectent l'utilisation normale</small>
                </div>
                <div class="mb-2">
                    <span class="badge bg-danger">Élevée</span>
                    <small class="text-muted d-block">Problèmes qui bloquent des fonctionnalités importantes</small>
                </div>
                <div class="mb-2">
                    <span class="badge bg-dark">Urgente</span>
                    <small class="text-muted d-block">Problèmes critiques qui empêchent l'utilisation</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ super() }}
<script>
$(document).ready(function() {
    // Auto-resize textarea
    $('textarea').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Suggestions basées sur la catégorie
    $('#category').on('change', function() {
        const category = $(this).val();
        const titleField = $('#title');
        const descField = $('#description');
        
        // Suggestions de titre selon la catégorie
        const suggestions = {
            'technical': 'Problème technique avec...',
            'billing': 'Question sur la facturation...',
            'general': 'Question générale sur...',
            'feature_request': 'Demande de fonctionnalité...',
            'bug_report': 'Bug détecté dans...',
            'training': 'Besoin de formation sur...',
            'integration': 'Problème d\'intégration avec...'
        };
        
        if (category && suggestions[category] && !titleField.val()) {
            titleField.attr('placeholder', suggestions[category]);
        }
    });
    
    // Validation en temps réel
    $('form').on('submit', function(e) {
        let isValid = true;
        
        // Vérifier le titre
        const title = $('#title').val().trim();
        if (title.length < 5) {
            isValid = false;
            $('#title').addClass('is-invalid');
        } else {
            $('#title').removeClass('is-invalid');
        }
        
        // Vérifier la description
        const description = $('#description').val().trim();
        if (description.length < 10) {
            isValid = false;
            $('#description').addClass('is-invalid');
        } else {
            $('#description').removeClass('is-invalid');
        }
        
        if (!isValid) {
            e.preventDefault();
            alert('Veuillez corriger les erreurs dans le formulaire.');
        }
    });
});
</script>
{% endblock %}
