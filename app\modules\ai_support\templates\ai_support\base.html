{% extends "base.html" %}

{% block title %}Support AI{% endblock %}

{% block extra_css %}
<style>
    .support-sidebar {
        background-color: #f8f9fa;
        min-height: calc(100vh - 56px);
        padding: 20px 0;
    }
    
    .support-nav .nav-link {
        color: #495057;
        padding: 10px 20px;
        border-radius: 0;
        margin-bottom: 2px;
    }
    
    .support-nav .nav-link:hover,
    .support-nav .nav-link.active {
        background-color: #007bff;
        color: white;
    }
    
    .chat-container {
        height: 500px;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
    }
    
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 15px;
        background-color: #f8f9fa;
    }
    
    .chat-input {
        padding: 15px;
        border-top: 1px solid #dee2e6;
        background-color: white;
    }
    
    .message {
        margin-bottom: 15px;
        padding: 10px 15px;
        border-radius: 18px;
        max-width: 70%;
        word-wrap: break-word;
    }
    
    .message.user {
        background-color: #007bff;
        color: white;
        margin-left: auto;
        text-align: right;
    }
    
    .message.ai {
        background-color: #e9ecef;
        color: #495057;
        margin-right: auto;
    }
    
    .message.system {
        background-color: #fff3cd;
        color: #856404;
        margin: 0 auto;
        text-align: center;
        font-style: italic;
    }
    
    .message-time {
        font-size: 0.8em;
        opacity: 0.7;
        margin-top: 5px;
    }
    
    .ticket-status {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8em;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .status-open { background-color: #d4edda; color: #155724; }
    .status-in_progress { background-color: #d1ecf1; color: #0c5460; }
    .status-waiting_ai { background-color: #fff3cd; color: #856404; }
    .status-waiting_human { background-color: #f8d7da; color: #721c24; }
    .status-resolved { background-color: #d1ecf1; color: #0c5460; }
    .status-closed { background-color: #e2e3e5; color: #383d41; }
    
    .priority-low { color: #28a745; }
    .priority-medium { color: #ffc107; }
    .priority-high { color: #fd7e14; }
    .priority-urgent { color: #dc3545; }
    
    .ai-confidence {
        font-size: 0.8em;
        color: #6c757d;
        margin-top: 5px;
    }
    
    .confidence-high { color: #28a745; }
    .confidence-medium { color: #ffc107; }
    .confidence-low { color: #dc3545; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 support-sidebar">
            <h5 class="px-3 mb-3">Support AI</h5>
            <nav class="nav flex-column support-nav">
                <a class="nav-link {% if request.endpoint == 'ai_support.index' %}active{% endif %}" 
                   href="{{ url_for('ai_support.index') }}">
                    <i class="fas fa-home"></i> Accueil
                </a>
                <a class="nav-link {% if request.endpoint == 'ai_support.chat' %}active{% endif %}" 
                   href="{{ url_for('ai_support.chat') }}">
                    <i class="fas fa-comments"></i> Chat en direct
                </a>
                <a class="nav-link {% if request.endpoint == 'ai_support.tickets' %}active{% endif %}" 
                   href="{{ url_for('ai_support.tickets') }}">
                    <i class="fas fa-ticket-alt"></i> Mes tickets
                </a>
                <a class="nav-link {% if request.endpoint == 'ai_support.create_ticket' %}active{% endif %}" 
                   href="{{ url_for('ai_support.create_ticket') }}">
                    <i class="fas fa-plus"></i> Nouveau ticket
                </a>
                <a class="nav-link {% if request.endpoint == 'ai_support.knowledge_base' %}active{% endif %}" 
                   href="{{ url_for('ai_support.knowledge_base') }}">
                    <i class="fas fa-book"></i> Base de connaissances
                </a>
                
                {% if current_user.is_admin %}
                <hr>
                <h6 class="px-3 mb-2 text-muted">Administration</h6>
                <a class="nav-link {% if request.endpoint == 'ai_support.admin_dashboard' %}active{% endif %}" 
                   href="{{ url_for('ai_support.admin_dashboard') }}">
                    <i class="fas fa-chart-bar"></i> Tableau de bord
                </a>
                {% endif %}
            </nav>
        </div>
        
        <!-- Main content -->
        <div class="col-md-9">
            <div class="p-4">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                {% block support_content %}{% endblock %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Fonctions utilitaires pour le support
function formatTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('fr-FR');
}

function getConfidenceClass(confidence) {
    if (confidence >= 0.8) return 'confidence-high';
    if (confidence >= 0.6) return 'confidence-medium';
    return 'confidence-low';
}

function getConfidenceText(confidence) {
    const percentage = Math.round(confidence * 100);
    return `Confiance: ${percentage}%`;
}

// Auto-refresh pour les pages de chat
function startAutoRefresh(interval = 5000) {
    setInterval(function() {
        if (document.hidden) return; // Ne pas rafraîchir si l'onglet n'est pas visible
        
        // Rafraîchir le contenu selon la page
        if (window.location.pathname.includes('/chat')) {
            refreshChatMessages();
        }
    }, interval);
}

// Fonction pour rafraîchir les messages de chat
function refreshChatMessages() {
    // Cette fonction sera implémentée dans les templates spécifiques
}
</script>
{% endblock %}
