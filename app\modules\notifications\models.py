"""Modèles pour le système de notifications"""

from datetime import datetime
from app import db
from enum import Enum

class NotificationType(Enum):
    """Types de notifications"""
    ORDER_RECEIVED = "order_received"
    ORDER_CONFIRMED = "order_confirmed"
    ORDER_PREPARING = "order_preparing"
    ORDER_READY = "order_ready"
    ORDER_OUT_FOR_DELIVERY = "order_out_for_delivery"
    ORDER_DELIVERED = "order_delivered"
    ORDER_CANCELLED = "order_cancelled"
    PAYMENT_RECEIVED = "payment_received"
    PAYMENT_FAILED = "payment_failed"

class NotificationStatus(Enum):
    """Statuts des notifications"""
    PENDING = "pending"
    SENT = "sent"
    READ = "read"
    FAILED = "failed"

class Notification(db.Model):
    """Modèle pour les notifications"""
    __tablename__ = 'notifications'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Type et contenu
    type = db.Column(db.Enum(NotificationType), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    
    # Destinataire
    recipient_type = db.Column(db.String(50), nullable=False)  # 'customer', 'restaurant', 'delivery'
    recipient_id = db.Column(db.Integer, nullable=False)  # ID du destinataire
    
    # Commande associée
    order_id = db.Column(db.Integer, db.ForeignKey('sales.id'), nullable=True)
    
    # Statut et timing
    status = db.Column(db.Enum(NotificationStatus), default=NotificationStatus.PENDING)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    sent_at = db.Column(db.DateTime, nullable=True)
    read_at = db.Column(db.DateTime, nullable=True)
    
    # Métadonnées
    data = db.Column(db.JSON, nullable=True)  # Données supplémentaires
    
    # Relations
    order = db.relationship('Sale', backref='notifications')
    
    def __repr__(self):
        return f'<Notification {self.id}: {self.type.value} to {self.recipient_type}:{self.recipient_id}>'
    
    def to_dict(self):
        """Convertir en dictionnaire pour JSON"""
        return {
            'id': self.id,
            'type': self.type.value,
            'title': self.title,
            'message': self.message,
            'recipient_type': self.recipient_type,
            'recipient_id': self.recipient_id,
            'order_id': self.order_id,
            'status': self.status.value,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'read_at': self.read_at.isoformat() if self.read_at else None,
            'data': self.data
        }
    
    def mark_as_sent(self):
        """Marquer comme envoyée"""
        self.status = NotificationStatus.SENT
        self.sent_at = datetime.utcnow()
        db.session.commit()
    
    def mark_as_read(self):
        """Marquer comme lue"""
        self.status = NotificationStatus.READ
        self.read_at = datetime.utcnow()
        db.session.commit()

class NotificationPreference(db.Model):
    """Préférences de notification par utilisateur"""
    __tablename__ = 'notification_preferences'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    user_type = db.Column(db.String(50), nullable=False)  # 'customer', 'restaurant', 'delivery'
    
    # Préférences par type de notification
    email_enabled = db.Column(db.Boolean, default=True)
    browser_enabled = db.Column(db.Boolean, default=True)
    sound_enabled = db.Column(db.Boolean, default=True)
    
    # Types de notifications activées
    order_updates = db.Column(db.Boolean, default=True)
    payment_updates = db.Column(db.Boolean, default=True)
    promotional = db.Column(db.Boolean, default=False)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<NotificationPreference {self.user_type}:{self.user_id}>'
