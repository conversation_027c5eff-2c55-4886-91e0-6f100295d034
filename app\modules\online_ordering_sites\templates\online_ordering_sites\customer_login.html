{% extends "online_ordering_sites/base.html" %}

{% block title %}Connexion - {{ site.site_name or site.owner.username }}{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Connexion
                    </h4>
                    <p class="mb-0 mt-2">Accédez à votre compte</p>
                </div>
                
                <div class="card-body p-4">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="form-group mb-3">
                            {{ form.email.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else ""), placeholder="<EMAIL>") }}
                            </div>
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            {{ form.password.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""), placeholder="Votre mot de passe") }}
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye" id="togglePasswordIcon"></i>
                                </button>
                            </div>
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-check mb-4">
                            {{ form.remember_me(class="form-check-input") }}
                            {{ form.remember_me.label(class="form-check-label") }}
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Se connecter
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="card-footer text-center">
                    <p class="mb-2">
                        <a href="#" class="text-muted small">
                            <i class="fas fa-key me-1"></i>
                            Mot de passe oublié ?
                        </a>
                    </p>
                    <p class="mb-0">
                        Pas encore de compte ?
                        <a href="{{ url_for('online_ordering_sites.customer_register') }}" class="text-primary">
                            Inscrivez-vous ici
                        </a>
                    </p>
                </div>
            </div>
            
            <!-- Avantages de se connecter -->
            <div class="card mt-4 border-0 bg-light">
                <div class="card-body text-center">
                    <h6 class="card-title text-primary">Avec votre compte</h6>
                    <div class="row mt-3">
                        <div class="col-4">
                            <i class="fas fa-shipping-fast fa-2x text-success mb-2"></i>
                            <p class="small text-muted">Commande rapide</p>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-map-marker-alt fa-2x text-info mb-2"></i>
                            <p class="small text-muted">Adresses sauvées</p>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-bell fa-2x text-warning mb-2"></i>
                            <p class="small text-muted">Suivi commandes</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    const passwordField = document.getElementById('password');
    const toggleIcon = document.getElementById('togglePasswordIcon');
    
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);
            
            if (type === 'password') {
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            } else {
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            }
        });
    }
    
    // Form validation
    const form = document.querySelector('form');
    const emailField = document.getElementById('email');
    
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Email validation
        if (!emailField.value.includes('@')) {
            emailField.classList.add('is-invalid');
            isValid = false;
        } else {
            emailField.classList.remove('is-invalid');
        }
        
        // Password validation
        if (passwordField.value.length < 6) {
            passwordField.classList.add('is-invalid');
            isValid = false;
        } else {
            passwordField.classList.remove('is-invalid');
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });
    
    // Real-time validation
    emailField.addEventListener('input', function() {
        if (this.value.includes('@')) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
    });
    
    passwordField.addEventListener('input', function() {
        if (this.value.length >= 6) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
    });
});
</script>
{% endblock %}
