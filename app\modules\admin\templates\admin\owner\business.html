{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Informations Entreprise</h1>
        <button class="btn btn-primary" onclick="saveAllChanges()">
            <i class="fas fa-save fa-sm"></i> Enregistrer les modifications
        </button>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- General Information Card -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informations générales</h6>
                </div>
                <div class="card-body">
                    <form id="generalInfoForm">
                        <div class="mb-3">
                            <label for="businessName" class="form-label">Nom de l'entreprise</label>
                            <input type="text" class="form-control" id="businessName" required>
                        </div>
                        <div class="mb-3">
                            <label for="legalForm" class="form-label">Forme juridique</label>
                            <select class="form-select" id="legalForm" required>
                                <option value="SARL">SARL</option>
                                <option value="SAS">SAS</option>
                                <option value="SA">SA</option>
                                <option value="EI">Entreprise Individuelle</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="siret" class="form-label">SIRET</label>
                            <input type="text" class="form-control" id="siret" required>
                        </div>
                        <div class="mb-3">
                            <label for="rcs" class="form-label">RCS</label>
                            <input type="text" class="form-control" id="rcs">
                        </div>
                        <div class="mb-3">
                            <label for="creationDate" class="form-label">Date de création</label>
                            <input type="date" class="form-control" id="creationDate">
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Contact Information Card -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Coordonnées</h6>
                </div>
                <div class="card-body">
                    <form id="contactInfoForm">
                        <div class="mb-3">
                            <label for="address" class="form-label">Adresse</label>
                            <textarea class="form-control" id="address" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="postalCode" class="form-label">Code postal</label>
                            <input type="text" class="form-control" id="postalCode" required>
                        </div>
                        <div class="mb-3">
                            <label for="city" class="form-label">Ville</label>
                            <input type="text" class="form-control" id="city" required>
                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label">Téléphone</label>
                            <input type="tel" class="form-control" id="phone" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="website" class="form-label">Site web</label>
                            <input type="url" class="form-control" id="website">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Tax Information Card -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informations fiscales</h6>
                </div>
                <div class="card-body">
                    <form id="taxInfoForm">
                        <div class="mb-3">
                            <label for="vatNumber" class="form-label">Numéro de TVA</label>
                            <input type="text" class="form-control" id="vatNumber">
                        </div>
                        <div class="mb-3">
                            <label for="vatRate" class="form-label">Taux de TVA par défaut</label>
                            <select class="form-select" id="vatRate">
                                <option value="20">20%</option>
                                <option value="10">10%</option>
                                <option value="5.5">5.5%</option>
                                <option value="2.1">2.1%</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="fiscalYear" class="form-label">Exercice fiscal</label>
                            <div class="row">
                                <div class="col">
                                    <select class="form-select" id="fiscalYearStart">
                                        <option value="1">Janvier</option>
                                        <option value="7">Juillet</option>
                                    </select>
                                </div>
                                <div class="col">
                                    <select class="form-select" id="fiscalYearEnd">
                                        <option value="12">Décembre</option>
                                        <option value="6">Juin</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="vatEnabled">
                                <label class="form-check-label" for="vatEnabled">
                                    Assujetti à la TVA
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Bank Information Card -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informations bancaires</h6>
                </div>
                <div class="card-body">
                    <form id="bankInfoForm">
                        <div class="mb-3">
                            <label for="bankName" class="form-label">Banque</label>
                            <input type="text" class="form-control" id="bankName">
                        </div>
                        <div class="mb-3">
                            <label for="iban" class="form-label">IBAN</label>
                            <input type="text" class="form-control" id="iban">
                        </div>
                        <div class="mb-3">
                            <label for="bic" class="form-label">BIC</label>
                            <input type="text" class="form-control" id="bic">
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="displayBankInfo">
                                <label class="form-check-label" for="displayBankInfo">
                                    Afficher sur les factures
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Save all changes
    window.saveAllChanges = function() {
        const generalInfo = Object.fromEntries(new FormData(document.getElementById('generalInfoForm')));
        const contactInfo = Object.fromEntries(new FormData(document.getElementById('contactInfoForm')));
        const taxInfo = Object.fromEntries(new FormData(document.getElementById('taxInfoForm')));
        const bankInfo = Object.fromEntries(new FormData(document.getElementById('bankInfoForm')));

        // TODO: Send data to server
        console.log({ generalInfo, contactInfo, taxInfo, bankInfo });
    };

    // Form validation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            if (!form.checkValidity()) {
                e.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
});
</script>
{% endblock %} 