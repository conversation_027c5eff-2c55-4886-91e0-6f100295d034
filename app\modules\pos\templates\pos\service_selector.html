<!-- Modal de sélection du type de service -->
<div class="modal fade" id="serviceModal" tabindex="-1" aria-labelledby="serviceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="serviceModalLabel">
                    <i class="fas fa-utensils"></i> Type de service
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <!-- Sur place -->
                    <div class="col-md-6">
                        <div class="service-option" data-service="dine_in">
                            <div class="service-card h-100">
                                <div class="service-icon">
                                    <i class="fas fa-chair fa-3x"></i>
                                </div>
                                <h5>Sur place</h5>
                                <p class="text-muted">Service à table dans le restaurant</p>
                            </div>
                        </div>
                    </div>

                    <!-- À emporter -->
                    <div class="col-md-6">
                        <div class="service-option" data-service="takeaway">
                            <div class="service-card h-100">
                                <div class="service-icon">
                                    <i class="fas fa-shopping-bag fa-3x"></i>
                                </div>
                                <h5>À emporter</h5>
                                <p class="text-muted">Commande à emporter</p>
                            </div>
                        </div>
                    </div>

                    <!-- Livraison -->
                    <div class="col-md-6">
                        <div class="service-option" data-service="delivery">
                            <div class="service-card h-100">
                                <div class="service-icon">
                                    <i class="fas fa-motorcycle fa-3x"></i>
                                </div>
                                <h5>Livraison</h5>
                                <p class="text-muted">Livraison à domicile</p>
                            </div>
                        </div>
                    </div>

                    <!-- Service au volant -->
                    <div class="col-md-6">
                        <div class="service-option" data-service="drive_thru">
                            <div class="service-card h-100">
                                <div class="service-icon">
                                    <i class="fas fa-car fa-3x"></i>
                                </div>
                                <h5>Service au volant</h5>
                                <p class="text-muted">Commande depuis le véhicule</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de sélection de salle et table -->
<div class="modal fade" id="roomTableModal" tabindex="-1" aria-labelledby="roomTableModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="roomTableModalLabel">
                    <i class="fas fa-map"></i> Sélectionner une table
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Sélecteur de salle -->
                <div class="mb-3">
                    <label class="form-label">Salle :</label>
                    <div class="btn-group" role="group" id="roomSelector">
                        <!-- Sera rempli dynamiquement -->
                    </div>
                </div>

                <!-- Plan de la salle -->
                <div id="roomPlan" class="room-plan-container">
                    <!-- Sera rempli dynamiquement -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de sélection du nombre de couverts -->
<div class="modal fade" id="coversModal" tabindex="-1" aria-labelledby="coversModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="coversModalLabel">
                    <i class="fas fa-users"></i> Nombre de couverts
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <p class="mb-4">Combien de personnes seront servies à cette table ?</p>
                    
                    <div class="covers-selector">
                        <div class="row g-2">
                            <div class="col-3" data-covers="1">
                                <div class="cover-option">
                                    <i class="fas fa-user"></i>
                                    <div>1</div>
                                </div>
                            </div>
                            <div class="col-3" data-covers="2">
                                <div class="cover-option">
                                    <i class="fas fa-users"></i>
                                    <div>2</div>
                                </div>
                            </div>
                            <div class="col-3" data-covers="3">
                                <div class="cover-option">
                                    <i class="fas fa-users"></i>
                                    <div>3</div>
                                </div>
                            </div>
                            <div class="col-3" data-covers="4">
                                <div class="cover-option">
                                    <i class="fas fa-users"></i>
                                    <div>4</div>
                                </div>
                            </div>
                            <div class="col-3" data-covers="5">
                                <div class="cover-option">
                                    <i class="fas fa-users"></i>
                                    <div>5</div>
                                </div>
                            </div>
                            <div class="col-3" data-covers="6">
                                <div class="cover-option">
                                    <i class="fas fa-users"></i>
                                    <div>6</div>
                                </div>
                            </div>
                            <div class="col-3" data-covers="7">
                                <div class="cover-option">
                                    <i class="fas fa-users"></i>
                                    <div>7</div>
                                </div>
                            </div>
                            <div class="col-3" data-covers="8">
                                <div class="cover-option">
                                    <i class="fas fa-users"></i>
                                    <div>8</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <label class="form-label">Ou saisir un nombre personnalisé :</label>
                        <input type="number" class="form-control text-center" id="customCovers" min="1" max="50" placeholder="Nombre de couverts">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="confirmCovers">Confirmer</button>
            </div>
        </div>
    </div>
</div>

<style>
.service-option {
    cursor: pointer;
    transition: transform 0.2s;
}

.service-option:hover {
    transform: translateY(-2px);
}

.service-card {
    border: 2px solid #dee2e6;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background: white;
}

.service-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.15);
}

.service-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.service-icon {
    color: #007bff;
    margin-bottom: 1rem;
}

.room-plan-container {
    min-height: 400px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    background: #f8f9fa;
}

.covers-selector .cover-option {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.covers-selector .cover-option:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.covers-selector .cover-option.selected {
    border-color: #007bff;
    background-color: #007bff;
    color: white;
}

.covers-selector .cover-option i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.table-mini {
    width: 50px;
    height: 50px;
    border: 2px solid;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 5px;
    cursor: pointer;
    font-weight: bold;
    font-size: 10px;
    transition: all 0.3s ease;
    text-align: center;
    line-height: 1.1;
}

.table-mini.available {
    border-color: #28a745;
    background-color: #8B4513;
    color: white;
}

.table-mini.occupied {
    border-color: #dc3545;
    background-color: #8B4513;
    color: white;
}

.table-mini:hover {
    transform: scale(1.1);
}

.table-mini.selected {
    border-color: #007bff;
    box-shadow: 0 0 10px rgba(0,123,255,0.5);
}

.table-mini.occupied:hover {
    cursor: pointer;
    transform: scale(1.15);
    box-shadow: 0 0 15px rgba(220,53,69,0.5);
}

/* Tables mini avec images */
.table-mini-image {
    border-radius: 8px !important;
    overflow: hidden;
    position: relative;
    width: 50px !important;
    height: 50px !important;
}

.table-mini-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 9px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    text-align: center;
    line-height: 1.1;
}

.table-mini-overlay small {
    font-size: 8px;
    margin: 1px 0;
}

.product-card {
    cursor: pointer;
    transition: all 0.2s ease;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.room-plan-container {
    max-height: 400px;
    overflow: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 10px;
}

#currentOrderItems .input-group {
    width: 120px;
}

#currentOrderItems .input-group input {
    width: 50px;
}

.temp-cart-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 8px;
    margin-bottom: 8px;
    background-color: #f8f9fa;
}

.modal-xl .modal-dialog {
    max-width: 1200px;
}

.alert.position-fixed {
    max-width: 400px;
}
</style>

<!-- Modal de gestion de commande en cours -->
<div class="modal fade" id="currentOrderModal" tabindex="-1" aria-labelledby="currentOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="currentOrderModalLabel">
                    <i class="fas fa-utensils"></i> Commande en cours - <span id="currentOrderTableName"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Informations de la commande -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-info-circle"></i> Informations</h6>
                            </div>
                            <div class="card-body">
                                <div id="currentOrderInfo">
                                    <!-- Sera rempli dynamiquement -->
                                </div>

                                <!-- Gestion des couverts -->
                                <div class="mt-3">
                                    <label class="form-label">Nombre de couverts :</label>
                                    <div class="input-group">
                                        <button class="btn btn-outline-secondary" type="button" id="decreaseCovers">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" class="form-control text-center" id="currentCoversCount" min="1" max="20" readonly>
                                        <button class="btn btn-outline-secondary" type="button" id="increaseCovers">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="mt-3">
                                    <button class="btn btn-warning btn-sm w-100 mb-2" id="moveTableBtn">
                                        <i class="fas fa-exchange-alt"></i> Déplacer vers autre table
                                    </button>
                                    <button class="btn btn-info btn-sm w-100 mb-2" id="printOrderBtn">
                                        <i class="fas fa-print"></i> Imprimer commande
                                    </button>
                                    <button class="btn btn-danger btn-sm w-100" id="cancelOrderBtn">
                                        <i class="fas fa-times"></i> Annuler commande
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Articles de la commande -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-list"></i> Articles commandés</h6>
                                <button class="btn btn-success btn-sm" id="editInPosBtn">
                                    <i class="fas fa-edit"></i> Modifier dans POS
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Article</th>
                                                <th>Prix unit.</th>
                                                <th>Qté</th>
                                                <th>Total</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="currentOrderItems">
                                            <!-- Sera rempli dynamiquement -->
                                        </tbody>
                                        <tfoot>
                                            <tr class="table-primary">
                                                <th colspan="3">Total</th>
                                                <th id="currentOrderTotal">0.00 €</th>
                                                <th></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>

                                <!-- Note cuisine -->
                                <div class="mt-3">
                                    <label class="form-label">Note pour la cuisine :</label>
                                    <textarea class="form-control" id="currentOrderNote" rows="2" placeholder="Instructions spéciales..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" id="updateOrderBtn">
                    <i class="fas fa-save"></i> Enregistrer modifications
                </button>
                <button type="button" class="btn btn-warning" id="sendUpdatesToKitchenBtn">
                    <i class="fas fa-utensils"></i> Envoyer modifications en cuisine
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de sélection de nouvelle table pour déplacement -->
<div class="modal fade" id="moveTableModal" tabindex="-1" aria-labelledby="moveTableModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="moveTableModalLabel">
                    <i class="fas fa-exchange-alt"></i> Déplacer vers une autre table
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">Sélectionnez la nouvelle table pour déplacer la commande :</p>

                <!-- Sélecteur de salle pour déplacement -->
                <div class="mb-3">
                    <label class="form-label">Salle :</label>
                    <div class="btn-group" role="group" id="moveRoomSelector">
                        <!-- Sera rempli dynamiquement -->
                    </div>
                </div>

                <!-- Plan de la salle pour déplacement -->
                <div id="moveRoomPlan" class="room-plan-container">
                    <!-- Sera rempli dynamiquement -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="confirmMoveBtn" disabled>
                    <i class="fas fa-check"></i> Confirmer déplacement
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'ajout d'articles -->
<div class="modal fade" id="addItemsModal" tabindex="-1" aria-labelledby="addItemsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addItemsModalLabel">
                    <i class="fas fa-plus"></i> Ajouter des articles à la commande
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Catégories -->
                    <div class="col-md-3">
                        <div class="list-group" id="addItemsCategories">
                            <!-- Sera rempli dynamiquement -->
                        </div>
                    </div>

                    <!-- Produits -->
                    <div class="col-md-6">
                        <div class="row" id="addItemsProducts">
                            <!-- Sera rempli dynamiquement -->
                        </div>
                    </div>

                    <!-- Panier temporaire -->
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Articles à ajouter</h6>
                            </div>
                            <div class="card-body">
                                <div id="tempCartItems">
                                    <p class="text-muted text-center">Aucun article sélectionné</p>
                                </div>
                                <div class="mt-3">
                                    <strong>Total : <span id="tempCartTotal">0.00 €</span></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="confirmAddItemsBtn">
                    <i class="fas fa-plus"></i> Ajouter à la commande
                </button>
            </div>
        </div>
    </div>
</div>