"""Initial migration with online ordering

Revision ID: de811428c20d
Revises: 
Create Date: 2025-06-19 21:27:29.785547

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'de811428c20d'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('settings', schema=None) as batch_op:
        batch_op.add_column(sa.Column('online_ordering_enabled', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('online_ordering_subdomain', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('online_ordering_site_name', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('online_ordering_description', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('online_ordering_primary_color', sa.String(length=7), nullable=True))
        batch_op.add_column(sa.Column('online_ordering_secondary_color', sa.String(length=7), nullable=True))
        batch_op.add_column(sa.Column('online_ordering_logo_path', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('online_ordering_banner_path', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('delivery_enabled', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('pickup_enabled', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('dine_in_enabled', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('drive_through_enabled', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('delivery_fee', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('minimum_order_amount', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('delivery_radius', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('online_ordering_hours', sa.JSON(), nullable=True))
        batch_op.add_column(sa.Column('delivery_hours', sa.JSON(), nullable=True))
        batch_op.add_column(sa.Column('pickup_hours', sa.JSON(), nullable=True))
        batch_op.add_column(sa.Column('notify_kitchen_online_orders', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('notify_customer_order_confirmed', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('notify_customer_order_ready', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('notify_customer_out_for_delivery', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('auto_assign_deliverer', sa.Boolean(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('settings', schema=None) as batch_op:
        batch_op.drop_column('auto_assign_deliverer')
        batch_op.drop_column('notify_customer_out_for_delivery')
        batch_op.drop_column('notify_customer_order_ready')
        batch_op.drop_column('notify_customer_order_confirmed')
        batch_op.drop_column('notify_kitchen_online_orders')
        batch_op.drop_column('pickup_hours')
        batch_op.drop_column('delivery_hours')
        batch_op.drop_column('online_ordering_hours')
        batch_op.drop_column('delivery_radius')
        batch_op.drop_column('minimum_order_amount')
        batch_op.drop_column('delivery_fee')
        batch_op.drop_column('drive_through_enabled')
        batch_op.drop_column('dine_in_enabled')
        batch_op.drop_column('pickup_enabled')
        batch_op.drop_column('delivery_enabled')
        batch_op.drop_column('online_ordering_banner_path')
        batch_op.drop_column('online_ordering_logo_path')
        batch_op.drop_column('online_ordering_secondary_color')
        batch_op.drop_column('online_ordering_primary_color')
        batch_op.drop_column('online_ordering_description')
        batch_op.drop_column('online_ordering_site_name')
        batch_op.drop_column('online_ordering_subdomain')
        batch_op.drop_column('online_ordering_enabled')

    # ### end Alembic commands ###
