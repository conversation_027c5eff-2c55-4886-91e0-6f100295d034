from app import db
from datetime import datetime
import enum

class BankOperationType(enum.Enum):
    """Types d'opérations bancaires"""
    DEPOSIT = "deposit"                    # Dépôt/Versement
    WITHDRAWAL = "withdrawal"              # Retrait
    TRANSFER_IN = "transfer_in"           # Virement entrant
    TRANSFER_OUT = "transfer_out"         # Virement sortant
    SUPPLIER_PAYMENT = "supplier_payment"  # Paiement fournisseur
    CHECK_PAYMENT = "check_payment"       # Paiement par chèque
    BANK_FEES = "bank_fees"               # Frais bancaires
    INTEREST = "interest"                 # Intérêts
    OTHER = "other"                       # Autre

    def __str__(self):
        return self.value

    @classmethod
    def from_str(cls, value):
        try:
            return cls(value.lower())
        except ValueError:
            return cls.OTHER

class BankAccount(db.Model):
    """Modèle pour les comptes bancaires"""
    __tablename__ = 'bank_accounts'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # Nom du compte
    account_number = db.Column(db.String(50))  # Numéro de compte
    iban = db.Column(db.String(34))  # IBAN
    bic = db.Column(db.String(11))   # BIC/SWIFT
    
    # Informations bancaires
    bank_name = db.Column(db.String(100))
    bank_address = db.Column(db.Text)
    
    # Solde et limites
    balance = db.Column(db.Float, default=0)
    initial_balance = db.Column(db.Float, default=0)
    overdraft_limit = db.Column(db.Float, default=0)  # Découvert autorisé
    
    # Configuration
    is_active = db.Column(db.Boolean, default=True)
    is_default = db.Column(db.Boolean, default=False)  # Compte par défaut
    currency = db.Column(db.String(3), default='EUR')
    
    # Informations complémentaires
    description = db.Column(db.Text)
    notes = db.Column(db.Text)
    
    # Utilisateurs
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    operations = db.relationship('BankOperation', backref='bank_account', lazy='dynamic', cascade='all, delete-orphan')
    owner = db.relationship('User', backref='bank_accounts')
    
    def __repr__(self):
        return f'<BankAccount {self.name}>'
    
    @property
    def available_balance(self):
        """Solde disponible (solde + découvert autorisé)"""
        return self.balance + self.overdraft_limit
    
    @property
    def is_overdrawn(self):
        """Vérifie si le compte est à découvert"""
        return self.balance < 0
    
    @property
    def overdraft_amount(self):
        """Montant du découvert"""
        return abs(self.balance) if self.is_overdrawn else 0
    
    def can_withdraw(self, amount):
        """Vérifie si un retrait est possible"""
        return (self.balance - amount) >= -self.overdraft_limit
    
    def add_operation(self, operation_type, amount, description, reference=None, user_id=None):
        """Ajoute une opération bancaire"""
        operation = BankOperation(
            bank_account=self,
            type=operation_type,
            amount=amount,
            description=description,
            reference=reference,
            user_id=user_id,
            owner_id=self.owner_id
        )
        
        # Mettre à jour le solde
        self.balance += amount
        
        db.session.add(operation)
        db.session.commit()
        
        return operation
    
    def transfer_to(self, target_account, amount, description, reference=None, user_id=None):
        """Effectue un virement vers un autre compte"""
        if not self.can_withdraw(amount):
            return False, "Solde insuffisant pour effectuer le virement"
        
        # Opération de sortie sur le compte source
        self.add_operation(
            operation_type=BankOperationType.TRANSFER_OUT,
            amount=-amount,
            description=f"Virement vers {target_account.name}: {description}",
            reference=reference,
            user_id=user_id
        )
        
        # Opération d'entrée sur le compte cible
        target_account.add_operation(
            operation_type=BankOperationType.TRANSFER_IN,
            amount=amount,
            description=f"Virement depuis {self.name}: {description}",
            reference=reference,
            user_id=user_id
        )
        
        return True, "Virement effectué avec succès"
    
    def deposit_from_cash_register(self, amount, cash_register, user_id=None):
        """Effectue un dépôt depuis la caisse"""
        if amount <= 0:
            return False, "Le montant doit être positif"

        # Calculer le solde total EXACTEMENT comme dans la page caisse
        total_sales = 0
        total_deposits = 0
        total_withdrawals = 0

        if cash_register.last_opened_at:
            from app.modules.cash_register.models_cash_register import CashOperation, CashRegisterOperationType
            all_operations = CashOperation.query.filter(
                CashOperation.register_id == cash_register.id,
                CashOperation.date >= cash_register.last_opened_at
            ).all()

            for op in all_operations:
                if op.type == CashRegisterOperationType.SALE:
                    total_sales += op.amount
                elif op.type == CashRegisterOperationType.CASH_IN:
                    total_deposits += op.amount
                elif op.type == CashRegisterOperationType.CASH_OUT:
                    total_withdrawals += abs(op.amount)
                elif op.type == CashRegisterOperationType.SUPPLIER_PAYMENT:
                    total_withdrawals += abs(op.amount)
                elif op.type == CashRegisterOperationType.BANK_DEPOSIT:
                    total_withdrawals += abs(op.amount)

        # Calcul final identique à la page caisse
        total_balance = cash_register.float_amount + total_sales + total_deposits - total_withdrawals

        if amount > total_balance:
            return False, f"Solde insuffisant en caisse (disponible: {total_balance:.2f}€)"
        
        # Opération bancaire
        self.add_operation(
            operation_type=BankOperationType.DEPOSIT,
            amount=amount,
            description="Dépôt depuis la caisse",
            user_id=user_id
        )
        
        # Opération de caisse
        from app.modules.cash_register.models_cash_register import CashOperation, CashRegisterOperationType
        
        operation = CashOperation(
            register_id=cash_register.id,
            type=CashRegisterOperationType.BANK_DEPOSIT,
            amount=-amount,
            note=f"Versement en banque - Compte {self.name}",
            user_id=user_id,
            owner_id=self.owner_id
        )
        
        cash_register.current_balance -= amount
        db.session.add(operation)
        db.session.commit()
        
        return True, "Dépôt effectué avec succès"
    
    @staticmethod
    def get_default(owner_id):
        """Récupère le compte bancaire par défaut"""
        return BankAccount.query.filter_by(owner_id=owner_id, is_default=True, is_active=True).first()
    
    @staticmethod
    def set_default(account_id, owner_id):
        """Définit un compte comme compte par défaut"""
        # Retirer le statut par défaut des autres comptes
        BankAccount.query.filter_by(owner_id=owner_id).update({'is_default': False})
        
        # Définir le nouveau compte par défaut
        account = BankAccount.query.get(account_id)
        if account and account.owner_id == owner_id:
            account.is_default = True
            db.session.commit()
            return True
        return False

class BankOperation(db.Model):
    """Modèle pour les opérations bancaires"""
    __tablename__ = 'bank_operations'
    
    id = db.Column(db.Integer, primary_key=True)
    bank_account_id = db.Column(db.Integer, db.ForeignKey('bank_accounts.id'), nullable=False)
    
    # Type et montant
    type = db.Column(db.Enum(BankOperationType), nullable=False)
    amount = db.Column(db.Float, nullable=False)  # Positif pour crédit, négatif pour débit
    
    # Informations de l'opération
    description = db.Column(db.Text, nullable=False)
    reference = db.Column(db.String(100))  # Référence externe (numéro de chèque, virement, etc.)
    
    # Solde après opération
    balance_after = db.Column(db.Float)
    
    # Informations complémentaires
    notes = db.Column(db.Text)
    
    # Utilisateurs
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Timestamps
    operation_date = db.Column(db.DateTime, default=datetime.utcnow)
    value_date = db.Column(db.DateTime)  # Date de valeur
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    user = db.relationship('User', foreign_keys=[user_id], backref='bank_operations')
    owner = db.relationship('User', foreign_keys=[owner_id], backref='owned_bank_operations')
    
    def __init__(self, **kwargs):
        super(BankOperation, self).__init__(**kwargs)
        # Calculer le solde après opération
        if self.bank_account:
            self.balance_after = self.bank_account.balance
    
    def __repr__(self):
        return f'<BankOperation {self.type} {self.amount}€>'
    
    @property
    def type_display(self):
        """Affichage lisible du type d'opération"""
        type_names = {
            BankOperationType.DEPOSIT: "Dépôt",
            BankOperationType.WITHDRAWAL: "Retrait",
            BankOperationType.TRANSFER_IN: "Virement entrant",
            BankOperationType.TRANSFER_OUT: "Virement sortant",
            BankOperationType.SUPPLIER_PAYMENT: "Paiement fournisseur",
            BankOperationType.CHECK_PAYMENT: "Paiement par chèque",
            BankOperationType.BANK_FEES: "Frais bancaires",
            BankOperationType.INTEREST: "Intérêts",
            BankOperationType.OTHER: "Autre"
        }
        return type_names.get(self.type, "Inconnu")
    
    @property
    def is_credit(self):
        """Vérifie si l'opération est un crédit"""
        return self.amount > 0
    
    @property
    def is_debit(self):
        """Vérifie si l'opération est un débit"""
        return self.amount < 0
    
    @property
    def absolute_amount(self):
        """Montant absolu de l'opération"""
        return abs(self.amount)
