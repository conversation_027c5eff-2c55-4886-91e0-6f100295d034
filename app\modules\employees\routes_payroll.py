"""
Routes pour la gestion de la paie des employés
"""
from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app.modules.employees.models import Employee, Payroll, Attendance, AttendanceStatus
from app.modules.employees.forms import PayrollForm
from app import db
from app.utils.decorators import permission_required
from datetime import date, timedelta, datetime
from decimal import Decimal
from . import bp

@bp.route('/<int:id>/payroll')
@login_required
@permission_required('can_view_employee_reports')
def payroll(id):
    """Liste des fiches de paie d'un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    # Récupérer toutes les fiches de paie pour les statistiques
    payrolls = Payroll.query.filter_by(employee_id=id).order_by(
        Payroll.pay_period_end.desc()
    ).all()

    # Calculer les statistiques
    total_net_pay = sum(p.net_pay or 0 for p in payrolls)
    total_hours = sum((p.regular_hours or 0) + (p.overtime_hours or 0) for p in payrolls)
    avg_monthly_pay = total_net_pay / len(payrolls) if payrolls else 0

    # Année et mois actuels pour les filtres
    from datetime import date
    current_year = date.today().year
    current_month = date.today().month

    return render_template('employees/payroll.html',
                         employee=employee,
                         payrolls=payrolls,
                         total_net_pay=total_net_pay,
                         total_hours=total_hours,
                         avg_monthly_pay=avg_monthly_pay,
                         current_year=current_year,
                         current_month=current_month)

@bp.route('/<int:id>/payroll/new', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_payroll')
def new_payroll(id):
    """Créer une nouvelle fiche de paie"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))
    
    form = PayrollForm()
    
    # Pré-remplir avec les taux de l'employé
    if request.method == 'GET':
        form.regular_rate.data = employee.hourly_rate or employee.base_salary
        form.overtime_rate.data = (employee.hourly_rate * Decimal('1.5')) if employee.hourly_rate else None
    
    if form.validate_on_submit():
        # Calculer automatiquement les heures basées sur les présences
        regular_hours, overtime_hours = calculate_hours_for_period(
            employee.id, form.pay_period_start.data, form.pay_period_end.data
        )
        
        payroll = Payroll(
            employee_id=employee.id,
            owner_id=current_user.get_owner_id,
            pay_period_start=form.pay_period_start.data,
            pay_period_end=form.pay_period_end.data,
            pay_date=form.pay_date.data,
            regular_hours=form.regular_hours.data or regular_hours,
            overtime_hours=form.overtime_hours.data or overtime_hours,
            regular_rate=form.regular_rate.data,
            overtime_rate=form.overtime_rate.data,
            tax_deduction=form.tax_deduction.data,
            social_security_deduction=form.social_security_deduction.data,
            health_insurance_deduction=form.health_insurance_deduction.data,
            other_deductions=form.other_deductions.data,
            bonus=form.bonus.data,
            commission=form.commission.data,
            adjustments=form.adjustments.data,
            notes=form.notes.data,
            processed_by_id=current_user.id
        )
        
        # Calculer automatiquement la paie
        payroll.calculate_pay()
        
        db.session.add(payroll)
        db.session.commit()
        
        flash('Fiche de paie créée avec succès!', 'success')
        return redirect(url_for('employees.payroll', id=employee.id))
    
    return render_template('employees/payroll_form.html', form=form, employee=employee, title="Nouvelle Fiche de Paie")

@bp.route('/payroll/<int:payroll_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_payroll')
def edit_payroll(payroll_id):
    """Modifier une fiche de paie"""
    payroll = Payroll.query.get_or_404(payroll_id)
    if payroll.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))
    
    if payroll.is_processed:
        flash('Cette fiche de paie a déjà été traitée et ne peut plus être modifiée.', 'error')
        return redirect(url_for('employees.payroll', id=payroll.employee_id))
    
    form = PayrollForm(obj=payroll)
    
    if form.validate_on_submit():
        form.populate_obj(payroll)
        payroll.calculate_pay()  # Recalculer
        
        db.session.commit()
        
        flash('Fiche de paie mise à jour avec succès!', 'success')
        return redirect(url_for('employees.payroll', id=payroll.employee_id))
    
    return render_template('employees/payroll_form.html', 
                         form=form, 
                         employee=payroll.employee, 
                         payroll=payroll,
                         title="Modifier Fiche de Paie")

@bp.route('/payroll/<int:payroll_id>/process', methods=['POST'])
@login_required
@permission_required('can_manage_payroll')
def process_payroll(payroll_id):
    """Marquer une fiche de paie comme traitée"""
    payroll = Payroll.query.get_or_404(payroll_id)
    if payroll.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))
    
    payroll.is_processed = True
    payroll.processed_at = datetime.now()
    payroll.processed_by_id = current_user.id
    
    db.session.commit()
    
    flash('Fiche de paie marquée comme traitée.', 'success')
    return redirect(url_for('employees.payroll', id=payroll.employee_id))

@bp.route('/payroll/<int:payroll_id>/delete', methods=['POST'])
@login_required
@permission_required('can_manage_payroll')
def delete_payroll(payroll_id):
    """Supprimer une fiche de paie"""
    payroll = Payroll.query.get_or_404(payroll_id)
    if payroll.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))
    
    if payroll.is_processed:
        flash('Cette fiche de paie a déjà été traitée et ne peut pas être supprimée.', 'error')
        return redirect(url_for('employees.payroll', id=payroll.employee_id))
    
    employee_id = payroll.employee_id
    db.session.delete(payroll)
    db.session.commit()
    
    flash('Fiche de paie supprimée avec succès!', 'success')
    return redirect(url_for('employees.payroll', id=employee_id))

def calculate_hours_for_period(employee_id, start_date, end_date):
    """Calcule les heures normales et supplémentaires pour une période donnée"""
    attendances = Attendance.query.filter(
        Attendance.employee_id == employee_id,
        Attendance.date >= start_date,
        Attendance.date <= end_date,
        Attendance.status == AttendanceStatus.PRESENT
    ).all()
    
    total_regular_hours = Decimal('0')
    total_overtime_hours = Decimal('0')
    
    for attendance in attendances:
        if attendance.hours_worked:
            if attendance.hours_worked <= 8:
                total_regular_hours += attendance.hours_worked
            else:
                total_regular_hours += Decimal('8')
                total_overtime_hours += (attendance.hours_worked - Decimal('8'))
        
        if attendance.overtime_hours:
            total_overtime_hours += attendance.overtime_hours
    
    return total_regular_hours, total_overtime_hours

# Routes manquantes pour les actions de paie
@bp.route('/payroll/generate-all', methods=['POST'])
@login_required
@permission_required('can_manage_payroll')
def generate_all_payrolls():
    """Générer toutes les fiches de paie manquantes"""
    owner_id = current_user.get_owner_id

    # Employés sans fiche de paie ce mois
    today = date.today()
    current_month = today.replace(day=1)

    employees_without_payroll = Employee.query.filter(
        Employee.owner_id == owner_id,
        Employee.status == 'ACTIVE',
        ~Employee.payrolls.any(
            Payroll.pay_period_start >= current_month
        )
    ).all()

    count = 0
    for employee in employees_without_payroll:
        # Calculer les heures automatiquement
        regular_hours, overtime_hours = calculate_hours_for_period(
            employee.id, current_month, today
        )

        payroll = Payroll(
            employee_id=employee.id,
            owner_id=owner_id,
            pay_period_start=current_month,
            pay_period_end=today,
            pay_date=today,
            regular_hours=regular_hours,
            overtime_hours=overtime_hours,
            regular_rate=employee.hourly_rate or employee.base_salary,
            overtime_rate=(employee.hourly_rate * Decimal('1.5')) if employee.hourly_rate else employee.base_salary,
            gross_pay=(regular_hours * (employee.hourly_rate or employee.base_salary)) +
                     (overtime_hours * ((employee.hourly_rate * Decimal('1.5')) if employee.hourly_rate else employee.base_salary)),
            net_pay=(regular_hours * (employee.hourly_rate or employee.base_salary)) +
                    (overtime_hours * ((employee.hourly_rate * Decimal('1.5')) if employee.hourly_rate else employee.base_salary))
        )

        db.session.add(payroll)
        count += 1

    db.session.commit()

    return {'success': True, 'count': count}

@bp.route('/payroll/process-all', methods=['POST'])
@login_required
@permission_required('can_manage_payroll')
def process_all_payrolls():
    """Marquer toutes les fiches de paie comme traitées"""
    owner_id = current_user.get_owner_id

    unprocessed_payrolls = Payroll.query.filter_by(
        owner_id=owner_id,
        is_processed=False
    ).all()

    count = 0
    for payroll in unprocessed_payrolls:
        payroll.is_processed = True
        payroll.processed_at = datetime.now()
        payroll.processed_by_id = current_user.id
        count += 1

    db.session.commit()

    return {'success': True, 'count': count}

@bp.route('/<int:id>/payroll/generate', methods=['POST'])
@login_required
@permission_required('can_manage_payroll')
def generate_payroll(id):
    """Générer automatiquement une fiche de paie pour un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        return {'success': False, 'message': 'Accès non autorisé'}, 403

    # Vérifier qu'il n'y a pas déjà une fiche de paie ce mois
    today = date.today()
    current_month = today.replace(day=1)

    existing_payroll = Payroll.query.filter(
        Payroll.employee_id == id,
        Payroll.pay_period_start >= current_month
    ).first()

    if existing_payroll:
        return {'success': False, 'message': 'Une fiche de paie existe déjà pour ce mois'}

    # Calculer les heures automatiquement
    regular_hours, overtime_hours = calculate_hours_for_period(
        id, current_month, today
    )

    payroll = Payroll(
        employee_id=id,
        owner_id=current_user.get_owner_id,
        pay_period_start=current_month,
        pay_period_end=today,
        pay_date=today,
        regular_hours=regular_hours,
        overtime_hours=overtime_hours,
        regular_rate=employee.hourly_rate or employee.base_salary,
        overtime_rate=(employee.hourly_rate * Decimal('1.5')) if employee.hourly_rate else employee.base_salary,
        gross_pay=(regular_hours * (employee.hourly_rate or employee.base_salary)) +
                 (overtime_hours * ((employee.hourly_rate * Decimal('1.5')) if employee.hourly_rate else employee.base_salary)),
        net_pay=(regular_hours * (employee.hourly_rate or employee.base_salary)) +
                (overtime_hours * ((employee.hourly_rate * Decimal('1.5')) if employee.hourly_rate else employee.base_salary))
    )

    db.session.add(payroll)
    db.session.commit()

    return {'success': True, 'message': 'Fiche de paie générée avec succès'}

@bp.route('/<int:id>/payroll/export')
@login_required
@permission_required('can_view_employee_reports')
def export_payroll(id):
    """Exporter les fiches de paie d'un employé"""
    # TODO: Implémenter l'export des fiches de paie
    flash('Fonctionnalité d\'export des fiches de paie en cours de développement', 'info')
    return redirect(url_for('employees.payroll', id=id))

@bp.route('/payroll/<int:payroll_id>/download')
@login_required
@permission_required('can_view_employee_reports')
def download_payslip(payroll_id):
    """Télécharger une fiche de paie en PDF"""
    # TODO: Implémenter le téléchargement de fiche de paie
    flash('Fonctionnalité de téléchargement de fiche de paie en cours de développement', 'info')
    payroll = Payroll.query.get_or_404(payroll_id)
    return redirect(url_for('employees.payroll', id=payroll.employee_id))

# Routes pour les rapports de paie
@bp.route('/payroll/reports')
@login_required
@permission_required('can_view_employee_reports')
def payroll_reports():
    """Rapports de paie globaux"""
    owner_id = current_user.get_owner_id
    
    # Statistiques du mois en cours
    today = date.today()
    current_month = today.replace(day=1)
    if today.month == 12:
        next_month = date(today.year + 1, 1, 1)
    else:
        next_month = today.replace(month=today.month + 1, day=1)
    
    monthly_payrolls = Payroll.query.filter(
        Payroll.owner_id == owner_id,
        Payroll.pay_period_start >= current_month,
        Payroll.pay_period_end < next_month
    ).all()
    
    total_gross_pay = sum(p.gross_pay for p in monthly_payrolls if p.gross_pay)
    total_net_pay = sum(p.net_pay for p in monthly_payrolls if p.net_pay)
    total_deductions = sum(p.total_deductions for p in monthly_payrolls if p.total_deductions)
    
    return render_template('employees/payroll_reports.html',
                         monthly_payrolls=monthly_payrolls,
                         total_gross_pay=total_gross_pay,
                         total_net_pay=total_net_pay,
                         total_deductions=total_deductions,
                         current_month=current_month)
