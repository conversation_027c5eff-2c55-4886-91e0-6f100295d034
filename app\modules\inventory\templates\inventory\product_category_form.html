{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-tag"></i>
                    {{ title }}
                </h1>
                <a href="{{ url_for('inventory.product_categories') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Retour aux catégories
                </a>
            </div>
        </div>
    </div>

    <!-- Form Card -->
    <div class="row">
        <div class="col-lg-8 col-xl-6 mx-auto">
            <div class="card shadow">
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.name.label(class="form-label") }}
                            {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                            {% for error in form.name.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="3") }}
                            {% for error in form.description.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.color.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.color(class="form-control" + (" is-invalid" if form.color.errors else ""), type="color") }}
                                <span class="input-group-text">{{ form.color.data or '#6c757d' }}</span>
                            </div>
                            {% for error in form.color.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-4">
                            {{ form.image.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.image(class="form-control" + (" is-invalid" if form.image.errors else ""), accept="image/*") }}
                                {% if category and category.image_path %}
                                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteImageModal">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                            <small class="text-muted">Formats acceptés: JPG, PNG, GIF</small>
                            {% for error in form.image.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                            
                            {% if category and category.image_path %}
                            <div class="mt-2">
                                <img src="{{ url_for('static', filename=category.image_path) }}" class="img-thumbnail" style="max-height: 200px;">
                            </div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                Enregistrer
                            </button>
                            <a href="{{ url_for('inventory.product_categories') }}" class="btn btn-outline-secondary">
                                Annuler
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% if category and category.image_path %}
<!-- Delete Image Modal -->
<div class="modal fade" id="deleteImageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer l'image de cette catégorie ?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form action="{{ url_for('inventory.delete_product_category_image', category_id=category.id) }}" method="POST">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i>
                        Supprimer l'image
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Preview image before upload
    const imageInput = document.querySelector('input[type="file"]');
    imageInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.createElement('img');
                preview.src = e.target.result;
                preview.className = 'img-thumbnail mt-2';
                preview.style.maxHeight = '200px';
                
                const existingPreview = imageInput.parentElement.nextElementSibling.nextElementSibling;
                if (existingPreview && existingPreview.tagName === 'DIV') {
                    existingPreview.replaceWith(preview);
                } else {
                    imageInput.parentElement.nextElementSibling.after(preview);
                }
            }
            reader.readAsDataURL(this.files[0]);
        }
    });
});
</script>
{% endblock %} 