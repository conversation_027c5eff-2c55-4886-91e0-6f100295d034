{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-receipt"></i>
            Paramètres des tickets
        </h1>
        <div>
            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#previewModal">
                <i class="fas fa-eye"></i>
                Aperçu
            </button>
            <button type="submit" form="receiptForm" class="btn btn-primary">
                <i class="fas fa-save"></i>
                Enregistrer
            </button>
        </div>
    </div>

    <!-- Settings Form -->
    <form id="receiptForm" method="POST">
        {{ form.hidden_tag() }}

        <div class="row">
            <!-- Receipt Content -->
            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-file-alt"></i>
                            Contenu du ticket
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            {{ form.header_text.label(class="form-label") }}
                            {{ form.header_text(class="form-control" + (" is-invalid" if form.header_text.errors else ""), rows="3") }}
                            {% for error in form.header_text.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                            <small class="text-muted">
                                Texte affiché en haut du ticket (ex: slogan, message de bienvenue)
                            </small>
                        </div>

                        <div class="mb-3">
                            {{ form.footer_text.label(class="form-label") }}
                            {{ form.footer_text(class="form-control" + (" is-invalid" if form.footer_text.errors else ""), rows="3") }}
                            {% for error in form.footer_text.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                            <small class="text-muted">
                                Texte affiché en bas du ticket (ex: remerciements, conditions)
                            </small>
                        </div>

                        <div class="mb-3">
                            {{ form.show_logo.label(class="form-label") }}
                            <div>
                                <div class="form-check form-switch">
                                    {{ form.show_logo(class="form-check-input") }}
                                    <label class="form-check-label" for="show_logo">
                                        Afficher le logo de l'entreprise
                                    </label>
                                </div>
                            </div>
                            {% for error in form.show_logo.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.show_tax_details.label(class="form-label") }}
                            <div>
                                <div class="form-check form-switch">
                                    {{ form.show_tax_details(class="form-check-input") }}
                                    <label class="form-check-label" for="show_tax_details">
                                        Afficher le détail des taxes
                                    </label>
                                </div>
                            </div>
                            {% for error in form.show_tax_details.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.show_payment_details.label(class="form-label") }}
                            <div>
                                <div class="form-check form-switch">
                                    {{ form.show_payment_details(class="form-check-input") }}
                                    <label class="form-check-label" for="show_payment_details">
                                        Afficher le détail du paiement
                                    </label>
                                </div>
                            </div>
                            {% for error in form.show_payment_details.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.show_server_name.label(class="form-label") }}
                            <div>
                                <div class="form-check form-switch">
                                    {{ form.show_server_name(class="form-check-input") }}
                                    <label class="form-check-label" for="show_server_name">
                                        Afficher le nom du serveur
                                    </label>
                                </div>
                            </div>
                            {% for error in form.show_server_name.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.show_order_number.label(class="form-label") }}
                            <div>
                                <div class="form-check form-switch">
                                    {{ form.show_order_number(class="form-check-input") }}
                                    <label class="form-check-label" for="show_order_number">
                                        Afficher le numéro de commande
                                    </label>
                                </div>
                            </div>
                            {% for error in form.show_order_number.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.show_table_number.label(class="form-label") }}
                            <div>
                                <div class="form-check form-switch">
                                    {{ form.show_table_number(class="form-check-input") }}
                                    <label class="form-check-label" for="show_table_number">
                                        Afficher le numéro de table
                                    </label>
                                </div>
                            </div>
                            {% for error in form.show_table_number.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Printing Settings -->
            <div class="col-lg-6">
                <!-- Layout -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-columns"></i>
                            Mise en page
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            {{ form.paper_size.label(class="form-label") }}
                            {{ form.paper_size(class="form-select" + (" is-invalid" if form.paper_size.errors else "")) }}
                            {% for error in form.paper_size.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.paper_width.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.paper_width(class="form-control" + (" is-invalid" if form.paper_width.errors else "")) }}
                                <span class="input-group-text">mm</span>
                            </div>
                            {% for error in form.paper_width.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.font_family.label(class="form-label") }}
                            {{ form.font_family(class="form-select" + (" is-invalid" if form.font_family.errors else "")) }}
                            {% for error in form.font_family.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.font_size.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.font_size(class="form-control" + (" is-invalid" if form.font_size.errors else "")) }}
                                <span class="input-group-text">pt</span>
                            </div>
                            {% for error in form.font_size.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.line_spacing.label(class="form-label") }}
                            {{ form.line_spacing(class="form-control" + (" is-invalid" if form.line_spacing.errors else "")) }}
                            {% for error in form.line_spacing.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.margin_top.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.margin_top(class="form-control" + (" is-invalid" if form.margin_top.errors else "")) }}
                                <span class="input-group-text">mm</span>
                            </div>
                            {% for error in form.margin_top.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.margin_bottom.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.margin_bottom(class="form-control" + (" is-invalid" if form.margin_bottom.errors else "")) }}
                                <span class="input-group-text">mm</span>
                            </div>
                            {% for error in form.margin_bottom.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.margin_left.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.margin_left(class="form-control" + (" is-invalid" if form.margin_left.errors else "")) }}
                                <span class="input-group-text">mm</span>
                            </div>
                            {% for error in form.margin_left.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.margin_right.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.margin_right(class="form-control" + (" is-invalid" if form.margin_right.errors else "")) }}
                                <span class="input-group-text">mm</span>
                            </div>
                            {% for error in form.margin_right.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Printer -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-print"></i>
                            Imprimante
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            {{ form.printer_name.label(class="form-label") }}
                            {{ form.printer_name(class="form-select" + (" is-invalid" if form.printer_name.errors else "")) }}
                            {% for error in form.printer_name.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.copies.label(class="form-label") }}
                            {{ form.copies(class="form-control" + (" is-invalid" if form.copies.errors else ""), type="number", min="1") }}
                            {% for error in form.copies.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.auto_print.label(class="form-label") }}
                            <div>
                                <div class="form-check form-switch">
                                    {{ form.auto_print(class="form-check-input") }}
                                    <label class="form-check-label" for="auto_print">
                                        Imprimer automatiquement
                                    </label>
                                </div>
                            </div>
                            {% for error in form.auto_print.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.cut_paper.label(class="form-label") }}
                            <div>
                                <div class="form-check form-switch">
                                    {{ form.cut_paper(class="form-check-input") }}
                                    <label class="form-check-label" for="cut_paper">
                                        Couper le papier
                                    </label>
                                </div>
                            </div>
                            {% for error in form.cut_paper.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.open_cash_drawer.label(class="form-label") }}
                            <div>
                                <div class="form-check form-switch">
                                    {{ form.open_cash_drawer(class="form-check-input") }}
                                    <label class="form-check-label" for="open_cash_drawer">
                                        Ouvrir le tiroir-caisse
                                    </label>
                                </div>
                            </div>
                            {% for error in form.open_cash_drawer.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Aperçu du ticket</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary" id="zoomOut">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="zoomReset">
                            100%
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="zoomIn">
                            <i class="fas fa-search-plus"></i>
                        </button>
                    </div>
                </div>
                <div class="preview-container" style="overflow: auto;">
                    <div id="preview" style="transform-origin: top left;">
                        <!-- Preview content will be loaded here -->
                        <div class="text-center text-muted">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" id="printPreview">
                    <i class="fas fa-print"></i>
                    Imprimer
                </button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Paper size handling
    const paperSizeSelect = document.getElementById('paper_size');
    const paperWidthInput = document.getElementById('paper_width');

    paperSizeSelect.addEventListener('change', function() {
        paperWidthInput.disabled = this.value !== 'custom';
    });
    paperWidthInput.disabled = paperSizeSelect.value !== 'custom';

    // Preview modal
    const previewModal = document.getElementById('previewModal');
    const previewContainer = document.getElementById('preview');
    let currentZoom = 1;

    previewModal.addEventListener('show.bs.modal', function() {
        // Load preview content
        fetch('{{ url_for("settings.receipt_preview") }}')
            .then(response => response.text())
            .then(html => {
                previewContainer.innerHTML = html;
            });
    });

    // Zoom controls
    document.getElementById('zoomIn').addEventListener('click', function() {
        currentZoom = Math.min(currentZoom + 0.1, 2);
        updateZoom();
    });

    document.getElementById('zoomOut').addEventListener('click', function() {
        currentZoom = Math.max(currentZoom - 0.1, 0.5);
        updateZoom();
    });

    document.getElementById('zoomReset').addEventListener('click', function() {
        currentZoom = 1;
        updateZoom();
    });

    function updateZoom() {
        previewContainer.style.transform = `scale(${currentZoom})`;
        document.getElementById('zoomReset').textContent = `${Math.round(currentZoom * 100)}%`;
    }

    // Print preview
    document.getElementById('printPreview').addEventListener('click', function() {
        window.open('{{ url_for("settings.receipt_print") }}', '_blank');
    });
});
</script>
{% endblock %}
{% endblock %} 