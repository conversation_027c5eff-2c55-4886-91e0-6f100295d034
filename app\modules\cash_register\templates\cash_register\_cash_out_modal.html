<div class="modal fade" id="cashOutModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="post" action="{{ url_for('cash_register.cash_out') }}" id="cashOutForm">
                {{ cash_out_form.csrf_token }}
                <div class="modal-header">
                    <h5 class="modal-title">Sortie de caisse</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        {% if register %}
                            Solde disponible: {{ register.current_balance }}€
                        {% else %}
                            Aucune caisse n'est ouverte
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            Cette opération va diminuer le solde de la caisse.
                            Assurez-vous d'avoir sélectionné le bon motif et de renseigner une note explicative.
                        </div>
                        <label for="amount" class="form-label">{{ cash_out_form.amount.label }}</label>
                        {{ cash_out_form.amount(class="form-control", type="number", step="0.01", id="amount") }}
                    </div>

                    <div class="mb-3">
                        <label for="reason" class="form-label">{{ cash_out_form.reason.label }}</label>
                        {{ cash_out_form.reason(class="form-select", id="reason") }}
                    </div>

                    <!-- Section fournisseurs -->
                    <div id="supplierSection" class="mb-3" style="display: none;">
                        <label class="form-label">Fournisseurs</label>
                        <select class="form-select select2" name="supplier_ids[]" multiple>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" id="supplierRequired" name="supplier_required">
                            <label class="form-check-label" for="supplierRequired">
                                Fournisseur obligatoire
                            </label>
                        </div>
                    </div>

                    <!-- Section produits/ingrédients -->
                    <div id="purchaseSection" class="mb-3" style="display: none;">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="matchAmount" name="match_amount">
                            <label class="form-check-label" for="matchAmount">
                                Le montant doit correspondre au total des achats
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="redirectToForm" name="redirect_to_form">
                            <label class="form-check-label" for="redirectToForm">
                                Rediriger vers le formulaire d'ajout après validation
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="note" class="form-label">{{ cash_out_form.note.label }}</label>
                        {{ cash_out_form.note(class="form-control", rows=3, id="note") }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-danger">Valider la sortie</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const reasonSelect = document.getElementById('reason');
    const supplierSection = document.getElementById('supplierSection');
    const purchaseSection = document.getElementById('purchaseSection');
    const cashOutForm = document.getElementById('cashOutForm');
    
    // Vérifier si les éléments existent avant d'ajouter les écouteurs d'événements
    if (reasonSelect && supplierSection && purchaseSection) {
        // Initialiser Select2 pour la sélection multiple si jQuery et Select2 sont chargés
        if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });
        }

        reasonSelect.addEventListener('change', function() {
            const reason = this.value;
            
            // Afficher/masquer les sections appropriées
            supplierSection.style.display = 
                reason === 'supplier_payment' ? 'block' : 'none';
            
            purchaseSection.style.display = 
                ['product_purchase', 'ingredient_purchase'].includes(reason) ? 'block' : 'none';
        });

        // Déclencher l'événement change pour initialiser l'affichage
        reasonSelect.dispatchEvent(new Event('change'));
    }

    if (cashOutForm) {
        cashOutForm.addEventListener('submit', function(e) {
            if (reasonSelect) {
                const reason = reasonSelect.value;
                const supplierRequired = document.getElementById('supplierRequired');
                const matchAmount = document.getElementById('matchAmount');
                const supplierSelect = supplierSection ? supplierSection.querySelector('select') : null;
                
                // Validation pour les paiements fournisseurs
                if (reason === 'supplier_payment' && supplierRequired && supplierRequired.checked && 
                    supplierSelect && (!supplierSelect.value || supplierSelect.selectedOptions.length === 0)) {
                    e.preventDefault();
                    alert('Veuillez sélectionner au moins un fournisseur.');
                    return;
                }
            }
        });
    }
});
</script>
