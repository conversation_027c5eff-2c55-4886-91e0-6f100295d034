from flask import current_app
from flask_login import current_user
from app.modules.cash_register.models_cash_register import CashRegister
from app.modules.cash_register.forms import (
    OpenRegisterForm, CloseRegisterForm, CashInForm, 
    CashOutForm, BankDepositForm, ResetRegisterForm
)

def format_currency(value):
    """Formate un montant en euros avec 2 décimales"""
    if value is None:
        return "0,00 €"
    return f"{value:,.2f} €".replace(",", " ").replace(".", ",")

def inject_cash_register_forms():
    """Injecte les formulaires de caisse dans toutes les pages"""
    if not current_user.is_authenticated:
        return {}

    # Récupérer la caisse actuelle
    register = CashRegister.get_current(current_user.id) if current_user.is_authenticated else None

    return {
        'register': register,  # Ajout du register dans le contexte
        'register_form': OpenRegisterForm(),
        'close_form': CloseRegisterForm(),
        'cash_in_form': CashInForm(),
        'cash_out_form': CashOutForm(),
        'bank_deposit_form': BankDepositForm(),
        'reset_form': ResetRegisterForm(),
        'format_currency': format_currency  # Ajout de la fonction de formatage
    } 