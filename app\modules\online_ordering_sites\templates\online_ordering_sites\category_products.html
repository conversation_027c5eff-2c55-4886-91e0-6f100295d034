{% extends "online_ordering_sites/base.html" %}

{% block title %}{{ category.name }} - {{ site.site_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('online_ordering_sites.index') }}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('online_ordering_sites.menu') }}">Menu</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ category.name }}</li>
        </ol>
    </nav>

    <!-- Category Header -->
    <div class="row align-items-center mb-4">
        <div class="col">
            <h1 class="h2 text-primary mb-0">
                {% if category.image_path %}
                    <img src="{{ url_for('static', filename='uploads/' + category.image_path) }}" 
                         alt="{{ category.name }}" class="me-2" style="width: 40px; height: 40px; object-fit: cover; border-radius: 8px;">
                {% else %}
                    <i class="fas fa-utensils me-2"></i>
                {% endif %}
                {{ category.name }}
            </h1>
            {% if category.description %}
                <p class="text-muted mb-0">{{ category.description }}</p>
            {% endif %}
        </div>
        <div class="col-auto">
            <span class="badge bg-secondary">{{ products|length }} produit{{ 's' if products|length > 1 else '' }}</span>
        </div>
    </div>

    <!-- Products Grid -->
    {% if products %}
        <div class="row">
            {% for product in products %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card product-card h-100 border-0 shadow-sm">
                        {% if product.image_path %}
                            <img src="{{ url_for('static', filename='uploads/' + product.image_path) }}" 
                                 class="card-img-top" alt="{{ product.name }}" 
                                 style="height: 200px; object-fit: cover;">
                        {% else %}
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                 style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                        
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">{{ product.name }}</h5>
                            {% if product.description %}
                                <p class="card-text text-muted small flex-grow-1">
                                    {{ product.description[:100] }}{% if product.description|length > 100 %}...{% endif %}
                                </p>
                            {% endif %}
                            
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="h5 text-primary mb-0">{{ "%.2f"|format(product.price) }}€</span>
                                    {% if not product.is_available %}
                                        <span class="badge bg-danger">Indisponible</span>
                                    {% elif product.stock_quantity is defined and product.stock_quantity <= 5 %}
                                        <span class="badge bg-warning">Stock limité</span>
                                    {% endif %}
                                </div>
                                
                                {% if product.is_available %}
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="input-group input-group-sm" style="width: 120px;">
                                            <button class="btn btn-outline-secondary quantity-btn" type="button" data-action="decrease">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <input type="number" class="form-control text-center quantity-input" 
                                                   value="1" min="1" max="10" data-product-id="{{ product.id }}">
                                            <button class="btn btn-outline-secondary quantity-btn" type="button" data-action="increase">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                        <button class="btn btn-primary flex-grow-1 add-to-cart-btn" 
                                                data-product-id="{{ product.id }}"
                                                data-product-name="{{ product.name }}"
                                                data-product-price="{{ product.price }}">
                                            <i class="fas fa-cart-plus me-1"></i>
                                            Ajouter
                                        </button>
                                    </div>
                                {% else %}
                                    <button class="btn btn-secondary w-100" disabled>
                                        <i class="fas fa-times me-1"></i>
                                        Indisponible
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">Aucun produit dans cette catégorie</h4>
            <p class="text-muted">Cette catégorie ne contient actuellement aucun produit disponible.</p>
            <a href="{{ url_for('online_ordering_sites.menu') }}" class="btn btn-primary">
                <i class="fas fa-arrow-left me-1"></i>
                Retour au menu
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quantity controls
    document.addEventListener('click', function(e) {
        if (e.target.closest('.quantity-btn')) {
            const btn = e.target.closest('.quantity-btn');
            const action = btn.getAttribute('data-action');
            const input = btn.parentElement.querySelector('.quantity-input');
            let value = parseInt(input.value);
            
            if (action === 'increase' && value < 10) {
                input.value = value + 1;
            } else if (action === 'decrease' && value > 1) {
                input.value = value - 1;
            }
        }
    });
    
    // Add to cart functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.add-to-cart-btn')) {
            const btn = e.target.closest('.add-to-cart-btn');
            const productId = btn.getAttribute('data-product-id');
            const productName = btn.getAttribute('data-product-name');
            const productPrice = parseFloat(btn.getAttribute('data-product-price'));
            const quantityInput = btn.parentElement.querySelector('.quantity-input');
            const quantity = parseInt(quantityInput.value);
            
            addToCart(productId, productName, productPrice, quantity);
            
            // Reset quantity to 1
            quantityInput.value = 1;
            
            // Visual feedback
            btn.innerHTML = '<i class="fas fa-check me-1"></i>Ajouté !';
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-success');
            
            setTimeout(() => {
                btn.innerHTML = '<i class="fas fa-cart-plus me-1"></i>Ajouter';
                btn.classList.remove('btn-success');
                btn.classList.add('btn-primary');
            }, 1500);
        }
    });
});
</script>
{% endblock %}
