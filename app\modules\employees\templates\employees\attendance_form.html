{% extends "employees/base_hr.html" %}

{% block title %}{{ title }}{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-clock me-2"></i>{{ title }}
        <small class="text-muted">{{ employee.full_name }}</small>
    </h1>
    <a href="{{ url_for('employees.detail', id=employee.id) }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>Retour
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-clock me-2"></i>Informations de Présence
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.date.label(class="form-label") }}
                            {{ form.date(class="form-control" + (" is-invalid" if form.date.errors else "")) }}
                            {% if form.date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.date.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.status.label(class="form-label") }}
                            {{ form.status(class="form-select" + (" is-invalid" if form.status.errors else "")) }}
                            {% if form.status.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.status.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.clock_in_time.label(class="form-label") }}
                            {{ form.clock_in_time(class="form-control" + (" is-invalid" if form.clock_in_time.errors else "")) }}
                            {% if form.clock_in_time.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.clock_in_time.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.clock_out_time.label(class="form-label") }}
                            {{ form.clock_out_time(class="form-control" + (" is-invalid" if form.clock_out_time.errors else "")) }}
                            {% if form.clock_out_time.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.clock_out_time.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.break_duration.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.break_duration(class="form-control" + (" is-invalid" if form.break_duration.errors else "")) }}
                                <span class="input-group-text">minutes</span>
                            </div>
                            {% if form.break_duration.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.break_duration.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.hours_worked.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.hours_worked(class="form-control" + (" is-invalid" if form.hours_worked.errors else ""), readonly=true) }}
                                <span class="input-group-text">heures</span>
                            </div>
                            <small class="form-text text-muted">Calculé automatiquement</small>
                            {% if form.hours_worked.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.hours_worked.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="3") }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('employees.detail', id=employee.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const clockInInput = document.getElementById('clock_in_time');
    const clockOutInput = document.getElementById('clock_out_time');
    const breakDurationInput = document.getElementById('break_duration');
    const hoursWorkedInput = document.getElementById('hours_worked');

    function calculateHours() {
        const clockIn = clockInInput.value;
        const clockOut = clockOutInput.value;
        const breakDuration = parseInt(breakDurationInput.value) || 0;

        if (clockIn && clockOut) {
            const start = new Date(`2000-01-01T${clockIn}`);
            const end = new Date(`2000-01-01T${clockOut}`);
            
            if (end > start) {
                const diffMs = end - start;
                const diffHours = diffMs / (1000 * 60 * 60);
                const workHours = diffHours - (breakDuration / 60);
                
                if (workHours > 0) {
                    hoursWorkedInput.value = workHours.toFixed(2);
                } else {
                    hoursWorkedInput.value = '0.00';
                }
            }
        }
    }

    clockInInput.addEventListener('change', calculateHours);
    clockOutInput.addEventListener('change', calculateHours);
    breakDurationInput.addEventListener('input', calculateHours);

    // Calculer au chargement si les valeurs sont déjà présentes
    calculateHours();
});
</script>
{% endblock %}
