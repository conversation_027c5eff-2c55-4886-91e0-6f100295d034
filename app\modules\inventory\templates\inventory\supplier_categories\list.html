{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-tags"></i> Catégories de Fournisseurs</h1>
        <div>
            <a href="{{ url_for('inventory.suppliers') }}" class="btn btn-outline-primary me-2">
                <i class="fas fa-truck"></i> Fournisseurs
            </a>
            <a href="{{ url_for('inventory.add_supplier_category') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouvelle Catégorie
            </a>
        </div>
    </div>

    {% if categories %}
        <div class="row">
            {% for category in categories %}
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header" style="background-color: {{ category.color }}; color: white;">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="{{ category.icon }}"></i> {{ category.name }}
                            </h5>
                            <span class="badge bg-light text-dark">{{ category.supplier_count }}</span>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if category.description %}
                            <p class="card-text">{{ category.description }}</p>
                        {% else %}
                            <p class="card-text text-muted">Aucune description</p>
                        {% endif %}
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-truck"></i> {{ category.supplier_count }} fournisseur(s)
                            </small>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100" role="group">
                            <a href="{{ url_for('inventory.supplier_category_details', id=category.id) }}" 
                               class="btn btn-outline-info btn-sm">
                                <i class="fas fa-eye"></i> Voir
                            </a>
                            <a href="{{ url_for('inventory.edit_supplier_category', id=category.id) }}" 
                               class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-edit"></i> Modifier
                            </a>
                            <button type="button" class="btn btn-outline-danger btn-sm" 
                                    onclick="confirmDelete({{ category.id }}, '{{ category.name }}')">
                                <i class="fas fa-trash"></i> Supprimer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
            <h3 class="text-muted">Aucune catégorie de fournisseur</h3>
            <p class="text-muted">Commencez par créer votre première catégorie de fournisseur.</p>
            <a href="{{ url_for('inventory.add_supplier_category') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Créer une catégorie
            </a>
        </div>
    {% endif %}
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la catégorie <strong id="categoryName"></strong> ?</p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    Cette action ne peut pas être annulée.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteForm" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(categoryId, categoryName) {
    document.getElementById('categoryName').textContent = categoryName;
    document.getElementById('deleteForm').action = `/inventory/supplier-categories/${categoryId}/delete`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
