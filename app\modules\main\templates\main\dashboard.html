{% extends "base.html" %}

{% block content %}
<div class="row">
    <!-- Statistiques principales -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Ventes Globales du jour</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_sales }}</div>
                        <div class="text-xs text-muted">
                            POS: {{ pos_sales_total }} | En ligne: {{ online_sales_total }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-euro-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Commandes Globales du jour</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_orders }}</div>
                        <div class="text-xs text-muted">
                            POS: {{ pos_orders_count }} | En ligne: {{ online_orders_count }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Dépenses du jour</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ today_expenses }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-wallet fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Résultat du jour</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ daily_result }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Détails par canal de vente -->
<div class="row">
    <!-- Ventes POS -->
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Ventes POS</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pos_sales_total }}</div>
                        <div class="text-xs text-muted">{{ pos_orders_count }} commandes</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-cash-register fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ventes en ligne -->
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Ventes en ligne</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ online_sales_total }}</div>
                        <div class="text-xs text-muted">{{ online_orders_count }} commandes</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-globe fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Résultat net -->
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-{{ 'success' if daily_result|replace(',', '')|replace(' €', '')|float > 0 else 'danger' }} shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-{{ 'success' if daily_result|replace(',', '')|replace(' €', '')|float > 0 else 'danger' }} text-uppercase mb-1">
                            Résultat Net du jour</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ daily_result }}</div>
                        <div class="text-xs text-muted">Ventes - Dépenses</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Produits en rupture -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-exclamation-triangle"></i>
                    Produits en rupture de stock
                </h6>
            </div>
            <div class="card-body">
                {% if low_stock_products %}
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Stock actuel</th>
                                <th>Stock minimum</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in low_stock_products %}
                            <tr>
                                <td>{{ product.name }}</td>
                                <td>{{ product.get_available_quantity() }}</td>
                                <td>{{ product.minimum_stock }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-success mb-0">
                    <i class="fas fa-check-circle"></i>
                    Tous les produits sont bien approvisionnés
                </p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Ingrédients en rupture -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-exclamation-triangle"></i>
                    Ingrédients en rupture de stock
                </h6>
            </div>
            <div class="card-body">
                {% if low_stock_ingredients %}
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Ingrédient</th>
                                <th>Stock actuel</th>
                                <th>Stock minimum</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for ingredient in low_stock_ingredients %}
                            <tr>
                                <td>{{ ingredient.name }}</td>
                                <td>{{ ingredient.stock_quantity }} {{ ingredient.unit }}</td>
                                <td>{{ ingredient.minimum_stock }} {{ ingredient.unit }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-success mb-0">
                    <i class="fas fa-check-circle"></i>
                    Tous les ingrédients sont bien approvisionnés
                </p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Graphique des ventes -->
<div class="row">
    <div class="col-xl-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-line"></i>
                    Évolution des ventes aujourd'hui
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-area" style="height: 300px;">
                    <canvas id="salesChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="row">
    <div class="col-xl-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt"></i>
                    Actions rapides
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('pos.index') }}" class="btn btn-primary btn-block">
                            <i class="fas fa-cash-register"></i>
                            Nouvelle vente
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('inventory.add_product') }}" class="btn btn-success btn-block">
                            <i class="fas fa-plus"></i>
                            Ajouter un produit
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('inventory.add_ingredient') }}" class="btn btn-info btn-block">
                            <i class="fas fa-plus"></i>
                            Ajouter un ingrédient
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('reports.sales') }}" class="btn btn-secondary btn-block">
                            <i class="fas fa-chart-bar"></i>
                            Voir les rapports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <footer class="footer mt-4 py-3 bg-light">
        <div class="container">
            <div class="row">
                <!-- Company Info -->
                <div class="col-md-4 mb-3">
                    <h5>NEW POS System</h5>
                    <p class="text-muted">Version 1.0.0</p>
                    <p>Développé par Youssef Yaakoubi</p>
                </div>

                <!-- Contact -->
                <div class="col-md-4 mb-3">
                    <h5>Contact</h5>
                    <p>Youssef Yaakoubi</p>
                    <p><a href="#" class="text-decoration-none">FAQ</a></p>
                </div>

                <!-- Legal -->
                <div class="col-md-4 mb-3">
                    <h5>Informations légales</h5>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-decoration-none">Mentions légales</a></li>
                        <li><a href="#" class="text-decoration-none">Politique de confidentialité</a></li>
                        <li><a href="#" class="text-decoration-none">Cookies</a></li>
                    </ul>
                </div>
            </div>

            <div class="row">
                <div class="col-12 text-center border-top pt-3">
                    <small class="text-muted">Copyright © 2025 - Tous droits réservés</small>
                </div>
            </div>
        </div>
    </footer>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: .25rem solid #4e73df!important;
}
.border-left-success {
    border-left: .25rem solid #1cc88a!important;
}
.border-left-warning {
    border-left: .25rem solid #f6c23e!important;
}
.border-left-info {
    border-left: .25rem solid #36b9cc!important;
}
.btn-block {
    display: block;
    width: 100%;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configuration du graphique des ventes avec 3 lignes
    const ctx = document.getElementById('salesChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: {{ hourly_labels|tojson }},
            datasets: [
                {
                    label: 'Total Global',
                    data: {{ hourly_total_data|tojson }},
                    borderColor: 'rgb(78, 115, 223)',
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    tension: 0.3,
                    fill: false,
                    borderWidth: 3
                },
                {
                    label: 'Ventes POS',
                    data: {{ hourly_pos_data|tojson }},
                    borderColor: 'rgb(54, 185, 204)',
                    backgroundColor: 'rgba(54, 185, 204, 0.1)',
                    tension: 0.3,
                    fill: false,
                    borderWidth: 2
                },
                {
                    label: 'Ventes en ligne',
                    data: {{ hourly_online_data|tojson }},
                    borderColor: 'rgb(246, 194, 62)',
                    backgroundColor: 'rgba(246, 194, 62, 0.1)',
                    tension: 0.3,
                    fill: false,
                    borderWidth: 2
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value + ' €';
                        }
                    }
                }
            }
        }
    });
});
</script>
{% endblock %}