from app import db
from datetime import datetime

class SupplierCategory(db.Model):
    __tablename__ = 'supplier_categories'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    description = db.Column(db.Text)
    color = db.Column(db.String(7), default='#6c757d')  # Couleur hex pour l'affichage
    icon = db.Column(db.String(50), default='fas fa-truck')  # Icône FontAwesome
    is_active = db.Column(db.<PERSON><PERSON>, default=True)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    suppliers = db.relationship('Supplier', backref='category', lazy='dynamic')
    
    def __repr__(self):
        return f'<SupplierCategory {self.name}>'
    
    @property
    def supplier_count(self):
        """Nombre de fournisseurs dans cette catégorie"""
        return self.suppliers.filter_by(is_active=True).count()
    
    @property
    def active_suppliers(self):
        """Fournisseurs actifs dans cette catégorie"""
        return self.suppliers.filter_by(is_active=True).all()
