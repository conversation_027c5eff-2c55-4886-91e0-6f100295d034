{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Automatisation</h1>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newTaskModal">
            <i class="fas fa-plus fa-sm"></i> Nouvelle tâche
        </button>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Active Tasks Card -->
        <div class="col-xl-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Tâches actives</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Type</th>
                                    <th>Fréquence</th>
                                    <th>Dernière exécution</th>
                                    <th>Prochaine exécution</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Sauvegarde quotidienne</td>
                                    <td>Sauvegarde</td>
                                    <td>Tous les jours à 00:00</td>
                                    <td>2024-03-20 00:00:00</td>
                                    <td>2024-03-21 00:00:00</td>
                                    <td><span class="badge bg-success">Actif</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-info" title="Éditer">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="Suspendre">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Task History Card -->
        <div class="col-xl-8 col-lg-7 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Historique d'exécution</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Tâche</th>
                                    <th>Date d'exécution</th>
                                    <th>Durée</th>
                                    <th>Statut</th>
                                    <th>Détails</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Sauvegarde quotidienne</td>
                                    <td>2024-03-20 00:00:00</td>
                                    <td>2m 30s</td>
                                    <td><span class="badge bg-success">Succès</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#taskDetailsModal">
                                            <i class="fas fa-info-circle"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Card -->
        <div class="col-xl-4 col-lg-5 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Statistiques</h6>
                </div>
                <div class="card-body">
                    <div class="row no-gutters align-items-center mb-3">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Tâches actives</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">4</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tasks fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="row no-gutters align-items-center mb-3">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Taux de succès</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">98%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Temps moyen d'exécution</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">1m 45s</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Task Modal -->
<div class="modal fade" id="newTaskModal" tabindex="-1" aria-labelledby="newTaskModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newTaskModalLabel">Nouvelle tâche automatisée</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="taskName" class="form-label">Nom de la tâche</label>
                        <input type="text" class="form-control" id="taskName" required>
                    </div>
                    <div class="mb-3">
                        <label for="taskType" class="form-label">Type de tâche</label>
                        <select class="form-select" id="taskType" required>
                            <option value="">Sélectionner...</option>
                            <option value="backup">Sauvegarde</option>
                            <option value="report">Rapport</option>
                            <option value="cleanup">Nettoyage</option>
                            <option value="notification">Notification</option>
                            <option value="custom">Personnalisé</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="schedule" class="form-label">Planification</label>
                        <select class="form-select" id="schedule" required>
                            <option value="daily">Quotidien</option>
                            <option value="weekly">Hebdomadaire</option>
                            <option value="monthly">Mensuel</option>
                            <option value="custom">Personnalisé</option>
                        </select>
                    </div>
                    <div class="mb-3" id="customSchedule" style="display: none;">
                        <label for="cronExpression" class="form-label">Expression Cron</label>
                        <input type="text" class="form-control" id="cronExpression" placeholder="*/5 * * * *">
                        <div class="form-text">Format: minute heure jour mois jour_semaine</div>
                    </div>
                    <div class="mb-3">
                        <label for="taskConfig" class="form-label">Configuration</label>
                        <textarea class="form-control" id="taskConfig" rows="5" placeholder="{
  // Configuration JSON
}"></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="notifyOnError">
                            <label class="form-check-label" for="notifyOnError">
                                Notifier en cas d'erreur
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary">Créer</button>
            </div>
        </div>
    </div>
</div>

<!-- Task Details Modal -->
<div class="modal fade" id="taskDetailsModal" tabindex="-1" aria-labelledby="taskDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="taskDetailsModalLabel">Détails de l'exécution</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <h6>Informations</h6>
                    <dl class="row">
                        <dt class="col-sm-3">Tâche</dt>
                        <dd class="col-sm-9">Sauvegarde quotidienne</dd>
                        
                        <dt class="col-sm-3">Date de début</dt>
                        <dd class="col-sm-9">2024-03-20 00:00:00</dd>
                        
                        <dt class="col-sm-3">Date de fin</dt>
                        <dd class="col-sm-9">2024-03-20 00:02:30</dd>
                        
                        <dt class="col-sm-3">Durée</dt>
                        <dd class="col-sm-9">2m 30s</dd>
                        
                        <dt class="col-sm-3">Statut</dt>
                        <dd class="col-sm-9"><span class="badge bg-success">Succès</span></dd>
                    </dl>
                </div>
                <div class="mb-3">
                    <h6>Logs</h6>
                    <pre class="bg-light p-3" style="max-height: 200px; overflow-y: auto;">
[00:00:00] Démarrage de la sauvegarde...
[00:00:01] Création de l'archive...
[00:02:29] Sauvegarde terminée avec succès
[00:02:30] Nettoyage des fichiers temporaires...</pre>
                </div>
                <div class="mb-3">
                    <h6>Résultats</h6>
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Fichiers traités
                            <span class="badge bg-primary rounded-pill">150</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Taille totale
                            <span class="badge bg-primary rounded-pill">2.5 GB</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary">Télécharger les logs</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide custom schedule input based on schedule selection
    const schedule = document.getElementById('schedule');
    const customSchedule = document.getElementById('customSchedule');
    
    schedule.addEventListener('change', function() {
        if (this.value === 'custom') {
            customSchedule.style.display = 'block';
        } else {
            customSchedule.style.display = 'none';
        }
    });
    
    // Show/hide task-specific configuration based on task type
    const taskType = document.getElementById('taskType');
    const taskConfig = document.getElementById('taskConfig');
    
    taskType.addEventListener('change', function() {
        let defaultConfig = {};
        
        switch(this.value) {
            case 'backup':
                defaultConfig = {
                    "target": "database",
                    "retention": "7",
                    "compress": true
                };
                break;
            case 'report':
                defaultConfig = {
                    "type": "sales",
                    "format": "pdf",
                    "recipients": []
                };
                break;
            case 'cleanup':
                defaultConfig = {
                    "older_than": "30d",
                    "types": ["logs", "temp"]
                };
                break;
            case 'notification':
                defaultConfig = {
                    "channel": "email",
                    "template": "default",
                    "conditions": {}
                };
                break;
        }
        
        taskConfig.value = JSON.stringify(defaultConfig, null, 2);
    });
});
</script>
{% endblock %} 