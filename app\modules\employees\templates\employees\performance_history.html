{% extends "employees/base_hr.html" %}

{% block title %}Historique Performance - {{ employee.full_name }}{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-chart-line me-2"></i>Historique Performance - {{ employee.full_name }}
    </h1>
    <div>
        <button class="btn btn-outline-success me-2" onclick="exportPerformanceHistory()">
            <i class="fas fa-download me-1"></i>Exporter
        </button>
        <a href="{{ url_for('employees.performance', id=employee.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour
        </a>
    </div>
</div>

{% if performances %}
<!-- Statistiques générales -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ performances|length }}</h4>
                        <p class="mb-0">Évaluations</p>
                    </div>
                    <i class="fas fa-clipboard-list fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        {% set avg_score = (performances|sum(attribute='overall_score') / performances|length) if performances else 0 %}
                        <h4 class="mb-0">{{ "%.1f"|format(avg_score) }}/5</h4>
                        <p class="mb-0">Score Moyen</p>
                    </div>
                    <i class="fas fa-star fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        {% set latest_score = performances[-1].overall_score if performances else 0 %}
                        <h4 class="mb-0">{{ "%.1f"|format(latest_score) }}/5</h4>
                        <p class="mb-0">Dernière Éval.</p>
                    </div>
                    <i class="fas fa-chart-line fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        {% set trend = 'up' if performances|length > 1 and performances[-1].overall_score > performances[-2].overall_score else 'down' if performances|length > 1 and performances[-1].overall_score < performances[-2].overall_score else 'stable' %}
                        <h4 class="mb-0">
                            <i class="fas fa-arrow-{{ 'up' if trend == 'up' else 'down' if trend == 'down' else 'right' }}"></i>
                        </h4>
                        <p class="mb-0">Tendance</p>
                    </div>
                    <i class="fas fa-trending-{{ 'up' if trend == 'up' else 'down' if trend == 'down' else 'up' }} fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Graphiques -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>Évolution des Scores
                </h5>
            </div>
            <div class="card-body">
                <canvas id="evolutionChart" height="100"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-radar me-2"></i>Profil Actuel
                </h5>
            </div>
            <div class="card-body">
                <canvas id="currentSkillsChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Comparaison des compétences -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Évolution par Compétence
                </h5>
            </div>
            <div class="card-body">
                <canvas id="skillsEvolutionChart" height="80"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Timeline des évaluations -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-timeline me-2"></i>Timeline des Évaluations
        </h5>
    </div>
    <div class="card-body">
        <div class="timeline">
            {% for performance in performances|reverse %}
            <div class="timeline-item">
                <div class="timeline-marker bg-{{ 'success' if (performance.overall_score or 0) >= 4 else 'warning' if (performance.overall_score or 0) >= 3 else 'danger' }}"></div>
                <div class="timeline-content">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">
                                Évaluation du {{ performance.evaluation_date.strftime('%d/%m/%Y') }}
                                <span class="badge bg-{{ 'success' if (performance.overall_score or 0) >= 4 else 'warning' if (performance.overall_score or 0) >= 3 else 'danger' }} ms-2">
                                    {{ "%.1f"|format(performance.overall_score or 0) }}/5
                                </span>
                            </h6>
                            <p class="text-muted mb-2">
                                Période: {{ performance.evaluation_period_start.strftime('%d/%m/%Y') }} - {{ performance.evaluation_period_end.strftime('%d/%m/%Y') }}
                            </p>
                            {% if performance.strengths %}
                            <div class="mb-2">
                                <strong>Points forts:</strong>
                                <p class="mb-0">{{ performance.strengths[:150] }}{% if performance.strengths|length > 150 %}...{% endif %}</p>
                            </div>
                            {% endif %}
                            {% if performance.areas_for_improvement %}
                            <div class="mb-2">
                                <strong>Axes d'amélioration:</strong>
                                <p class="mb-0">{{ performance.areas_for_improvement[:150] }}{% if performance.areas_for_improvement|length > 150 %}...{% endif %}</p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="text-end">
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('employees.performance_detail', performance_id=performance.id) }}" 
                                   class="btn btn-sm btn-outline-primary" title="Voir détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if not performance.is_finalized %}
                                <a href="{{ url_for('employees.edit_performance', performance_id=performance.id) }}" 
                                   class="btn btn-sm btn-outline-warning" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Détail des scores -->
                    <div class="row mt-3">
                        <div class="col-md-2">
                            <small class="text-muted">Ponctualité</small><br>
                            <strong>{{ performance.punctuality_score or 0 }}/5</strong>
                        </div>
                        <div class="col-md-2">
                            <small class="text-muted">Qualité</small><br>
                            <strong>{{ performance.quality_of_work_score or 0 }}/5</strong>
                        </div>
                        <div class="col-md-2">
                            <small class="text-muted">Équipe</small><br>
                            <strong>{{ performance.teamwork_score or 0 }}/5</strong>
                        </div>
                        <div class="col-md-2">
                            <small class="text-muted">Communication</small><br>
                            <strong>{{ performance.communication_score or 0 }}/5</strong>
                        </div>
                        <div class="col-md-2">
                            <small class="text-muted">Initiative</small><br>
                            <strong>{{ performance.initiative_score or 0 }}/5</strong>
                        </div>
                        <div class="col-md-2">
                            <small class="text-muted">Service Client</small><br>
                            <strong>{{ performance.customer_service_score or 0 }}/5</strong>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

{% else %}
<div class="text-center py-5">
    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
    <h5 class="text-muted">Aucune évaluation finalisée</h5>
    <p class="text-muted">L'historique des performances apparaîtra ici une fois les évaluations finalisées.</p>
    <a href="{{ url_for('employees.new_performance', id=employee.id) }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>Créer une évaluation
    </a>
</div>
{% endif %}

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
{% if performances %}
// Données pour les graphiques
const performanceData = {{ performance_data|tojson|safe }};

// Graphique d'évolution globale
const evolutionCtx = document.getElementById('evolutionChart').getContext('2d');
new Chart(evolutionCtx, {
    type: 'line',
    data: {
        labels: performanceData.map(p => p.date),
        datasets: [{
            label: 'Score Global',
            data: performanceData.map(p => p.overall_score),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 5
            }
        }
    }
});

// Graphique radar actuel
{% set latest = performances[-1] %}
const currentSkillsCtx = document.getElementById('currentSkillsChart').getContext('2d');
new Chart(currentSkillsCtx, {
    type: 'radar',
    data: {
        labels: ['Ponctualité', 'Qualité', 'Équipe', 'Communication', 'Initiative', 'Service'],
        datasets: [{
            label: 'Scores Actuels',
            data: [
                {{ latest.punctuality_score or 0 }},
                {{ latest.quality_of_work_score or 0 }},
                {{ latest.teamwork_score or 0 }},
                {{ latest.communication_score or 0 }},
                {{ latest.initiative_score or 0 }},
                {{ latest.customer_service_score or 0 }}
            ],
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            r: {
                beginAtZero: true,
                max: 5
            }
        }
    }
});

// Graphique d'évolution par compétence
const skillsEvolutionCtx = document.getElementById('skillsEvolutionChart').getContext('2d');
new Chart(skillsEvolutionCtx, {
    type: 'line',
    data: {
        labels: performanceData.map(p => p.date),
        datasets: [
            {
                label: 'Ponctualité',
                data: performanceData.map(p => p.punctuality),
                borderColor: 'rgb(255, 99, 132)',
                tension: 0.1
            },
            {
                label: 'Qualité',
                data: performanceData.map(p => p.quality),
                borderColor: 'rgb(54, 162, 235)',
                tension: 0.1
            },
            {
                label: 'Équipe',
                data: performanceData.map(p => p.teamwork),
                borderColor: 'rgb(255, 205, 86)',
                tension: 0.1
            },
            {
                label: 'Communication',
                data: performanceData.map(p => p.communication),
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            },
            {
                label: 'Initiative',
                data: performanceData.map(p => p.initiative),
                borderColor: 'rgb(153, 102, 255)',
                tension: 0.1
            },
            {
                label: 'Service Client',
                data: performanceData.map(p => p.customer_service),
                borderColor: 'rgb(255, 159, 64)',
                tension: 0.1
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 5
            }
        }
    }
});
{% endif %}

function exportPerformanceHistory() {
    const url = `/employees/{{ employee.id }}/performance/export`;
    window.open(url, '_blank');
}
</script>
{% endblock %}
