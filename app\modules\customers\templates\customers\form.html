{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">{{ title }}</h2>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.csrf_token }}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.first_name.label(class="form-label") }}
                                    {{ form.first_name(class="form-control") }}
                                    {% if form.first_name.errors %}
                                        {% for error in form.first_name.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.last_name.label(class="form-label") }}
                                    {{ form.last_name(class="form-control") }}
                                    {% if form.last_name.errors %}
                                        {% for error in form.last_name.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.email.label(class="form-label") }}
                                    {{ form.email(class="form-control") }}
                                    {% if form.email.errors %}
                                        {% for error in form.email.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.phone.label(class="form-label") }}
                                    {{ form.phone(class="form-control") }}
                                    {% if form.phone.errors %}
                                        {% for error in form.phone.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            {{ form.address.label(class="form-label") }}
                            {{ form.address(class="form-control", rows=3) }}
                            {% if form.address.errors %}
                                {% for error in form.address.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                {% endfor %}
                            {% endif %}
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('customers.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 