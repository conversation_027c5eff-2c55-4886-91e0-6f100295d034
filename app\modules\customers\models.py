from datetime import datetime
from app import db

class Customer(db.Model):
    __tablename__ = 'customers'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)
    phone = db.Column(db.String(20), unique=True, nullable=True)
    address = db.Column(db.String(200), nullable=True)
    loyalty_points = db.Column(db.Integer, default=0)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_visit = db.Column(db.DateTime, nullable=True)
    
    # Relations
    sales = db.relationship('Sale', backref='customer', lazy=True)
    owner = db.relationship('User', backref='customers')
    
    def __repr__(self):
        return f'<Customer {self.first_name} {self.last_name}>'
    
    def add_points(self, points):
        self.loyalty_points += points
        db.session.commit()
    
    def use_points(self, points):
        if self.loyalty_points >= points:
            self.loyalty_points -= points
            db.session.commit()
            return True
        return False
    
    def update_last_visit(self):
        self.last_visit = datetime.utcnow()
        db.session.commit() 