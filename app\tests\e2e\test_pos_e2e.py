import pytest
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import os

# Configuration des navigateurs pour les tests cross-browser
@pytest.fixture(params=['chrome', 'firefox', 'edge'])
def driver(request):
    """Fixture pour les tests cross-browser"""
    if request.param == 'chrome':
        driver = webdriver.Chrome()
    elif request.param == 'firefox':
        driver = webdriver.Firefox()
    elif request.param == 'edge':
        driver = webdriver.Edge()
    
    driver.implicitly_wait(10)
    yield driver
    driver.quit()

class TestPOSE2E:
    """Tests E2E pour le module POS"""
    
    def test_login_and_access_pos(self, driver, live_server):
        """Test de connexion et accès au POS"""
        driver.get(f"{live_server.url}/auth/login")
        
        # Connexion
        username_input = driver.find_element(By.NAME, "username")
        password_input = driver.find_element(By.NAME, "password")
        submit_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        
        username_input.send_keys("test_user")
        password_input.send_keys("test123")
        submit_button.click()
        
        # Vérifier la redirection vers le POS
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "product-grid"))
        )
        
        assert "Point de Vente" in driver.title
    
    def test_add_product_to_cart(self, driver, live_server):
        """Test d'ajout d'un produit au panier"""
        # Connexion préalable
        driver.get(f"{live_server.url}/auth/login")
        driver.find_element(By.NAME, "username").send_keys("test_user")
        driver.find_element(By.NAME, "password").send_keys("test123")
        driver.find_element(By.CSS_SELECTOR, "button[type='submit']").click()
        
        # Attendre le chargement des produits
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "product-grid"))
        )
        
        # Cliquer sur un produit
        product = driver.find_element(By.CLASS_NAME, "product-btn")
        product.click()
        
        # Vérifier l'ajout au panier
        cart_items = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "cartItems"))
        )
        assert len(cart_items.find_elements(By.CLASS_NAME, "cart-item")) > 0
    
    def test_complete_sale_process(self, driver, live_server):
        """Test du processus complet de vente"""
        # Connexion
        driver.get(f"{live_server.url}/auth/login")
        driver.find_element(By.NAME, "username").send_keys("test_user")
        driver.find_element(By.NAME, "password").send_keys("test123")
        driver.find_element(By.CSS_SELECTOR, "button[type='submit']").click()
        
        # Attendre le chargement des produits
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "product-grid"))
        )
        
        # Ajouter un produit au panier
        product = driver.find_element(By.CLASS_NAME, "product-btn")
        product.click()
        
        # Cliquer sur le bouton de paiement
        payment_btn = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CLASS_NAME, "cart-action-btn.payment"))
        )
        payment_btn.click()
        
        # Sélectionner le mode de paiement
        WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, "[data-method='cash']"))
        ).click()
        
        # Vérifier le message de succès
        try:
            success_message = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "alert-success"))
            )
            assert "succès" in success_message.text.lower()
        except TimeoutException:
            pytest.fail("Le message de succès n'est pas apparu")
    
    def test_stock_update_visual_feedback(self, driver, live_server):
        """Test de la mise à jour visuelle des stocks"""
        # Connexion
        driver.get(f"{live_server.url}/auth/login")
        driver.find_element(By.NAME, "username").send_keys("test_user")
        driver.find_element(By.NAME, "password").send_keys("test123")
        driver.find_element(By.CSS_SELECTOR, "button[type='submit']").click()
        
        # Attendre le chargement des produits
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "product-grid"))
        )
        
        # Noter le stock initial
        product = driver.find_element(By.CLASS_NAME, "product-btn")
        initial_stock = product.find_element(By.CLASS_NAME, "stock").text
        
        # Ajouter le produit au panier et effectuer la vente
        product.click()
        
        payment_btn = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CLASS_NAME, "cart-action-btn.payment"))
        )
        payment_btn.click()
        
        WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, "[data-method='cash']"))
        ).click()
        
        # Vérifier que le stock a été mis à jour visuellement
        WebDriverWait(driver, 10).until(
            lambda d: d.find_element(By.CLASS_NAME, "stock").text != initial_stock
        )
        
        # Vérifier l'animation de mise à jour
        stock_element = driver.find_element(By.CLASS_NAME, "stock")
        assert "stock-updated" in stock_element.get_attribute("class") 