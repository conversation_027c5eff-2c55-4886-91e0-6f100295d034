{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Supprimer le produit</h1>
        <a href="{{ url_for('reports.products') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour
        </a>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Confirmation de suppression</h6>
                </div>
                <div class="card-body">
                    <p>Êtes-vous sûr de vouloir supprimer le produit suivant ?</p>
                    
                    <table class="table">
                        <tr>
                            <th style="width: 150px">Nom</th>
                            <td>{{ product.name }}</td>
                        </tr>
                        <tr>
                            <th>Catégorie</th>
                            <td>{{ product.category.name if product.category else "Sans catégorie" }}</td>
                        </tr>
                        <tr>
                            <th>Prix</th>
                            <td>{{ product.price|format_currency }}</td>
                        </tr>
                        <tr>
                            <th>Stock actuel</th>
                            <td>{{ product.get_available_quantity() }}</td>
                        </tr>
                    </table>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Cette action est irréversible. Toutes les données associées à ce produit seront également supprimées.
                    </div>

                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Confirmer la suppression
                        </button>
                        <a href="{{ url_for('reports.products') }}" class="btn btn-secondary">Annuler</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}