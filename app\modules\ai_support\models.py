from datetime import datetime
from app.extensions import db
import enum

class SupportTicketStatus(enum.Enum):
    """Statuts des tickets de support"""
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    WAITING_AI = "waiting_ai"
    WAITING_HUMAN = "waiting_human"
    RESOLVED = "resolved"
    CLOSED = "closed"

class SupportTicketPriority(enum.Enum):
    """Priorités des tickets de support"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class SupportTicketCategory(enum.Enum):
    """Catégories des tickets de support"""
    TECHNICAL = "technical"
    BILLING = "billing"
    GENERAL = "general"
    FEATURE_REQUEST = "feature_request"
    BUG_REPORT = "bug_report"
    TRAINING = "training"
    INTEGRATION = "integration"

class MessageSender(enum.Enum):
    """Types d'expéditeurs de messages"""
    USER = "user"
    AI = "ai"
    HUMAN_AGENT = "human_agent"
    SYSTEM = "system"

class SupportTicket(db.Model):
    """Modèle pour les tickets de support"""
    __tablename__ = 'support_tickets'
    
    id = db.Column(db.Integer, primary_key=True)
    ticket_number = db.Column(db.String(20), unique=True, nullable=False)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=False)
    
    # Statut et priorité
    status = db.Column(db.Enum(SupportTicketStatus), default=SupportTicketStatus.OPEN)
    priority = db.Column(db.Enum(SupportTicketPriority), default=SupportTicketPriority.MEDIUM)
    category = db.Column(db.Enum(SupportTicketCategory), default=SupportTicketCategory.GENERAL)
    
    # Relations utilisateur
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    assigned_agent_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    
    # Métadonnées
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    resolved_at = db.Column(db.DateTime, nullable=True)
    closed_at = db.Column(db.DateTime, nullable=True)
    
    # Évaluation de satisfaction
    satisfaction_rating = db.Column(db.Integer, nullable=True)  # 1-5
    satisfaction_comment = db.Column(db.Text, nullable=True)
    
    # Flags pour l'IA
    ai_handled = db.Column(db.Boolean, default=True)
    escalated_to_human = db.Column(db.Boolean, default=False)
    escalation_reason = db.Column(db.Text, nullable=True)
    
    # Relations
    messages = db.relationship('SupportMessage', backref='ticket', lazy='dynamic', cascade='all, delete-orphan')
    user = db.relationship('User', foreign_keys=[user_id], backref='support_tickets')
    assigned_agent = db.relationship('User', foreign_keys=[assigned_agent_id], backref='assigned_tickets')
    
    def __repr__(self):
        return f'<SupportTicket {self.ticket_number}: {self.title}>'
    
    def generate_ticket_number(self):
        """Génère un numéro de ticket unique"""
        import random
        import string
        while True:
            number = 'SUP-' + ''.join(random.choices(string.digits, k=6))
            if not SupportTicket.query.filter_by(ticket_number=number).first():
                return number

class SupportMessage(db.Model):
    """Modèle pour les messages dans les conversations de support"""
    __tablename__ = 'support_messages'
    
    id = db.Column(db.Integer, primary_key=True)
    ticket_id = db.Column(db.Integer, db.ForeignKey('support_tickets.id'), nullable=False)
    
    # Contenu du message
    content = db.Column(db.Text, nullable=False)
    sender_type = db.Column(db.Enum(MessageSender), nullable=False)
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # Null pour AI/SYSTEM
    
    # Métadonnées
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_internal = db.Column(db.Boolean, default=False)  # Messages internes entre agents
    
    # Données spécifiques à l'IA
    ai_confidence = db.Column(db.Float, nullable=True)  # Confiance de l'IA dans sa réponse
    ai_model_used = db.Column(db.String(50), nullable=True)  # Modèle IA utilisé
    processing_time = db.Column(db.Float, nullable=True)  # Temps de traitement en secondes
    
    # Relations
    sender = db.relationship('User', foreign_keys=[sender_id], backref='support_messages')
    
    def __repr__(self):
        return f'<SupportMessage {self.id}: {self.sender_type.value}>'

class SupportConversation(db.Model):
    """Modèle pour les conversations de support en temps réel (chat)"""
    __tablename__ = 'support_conversations'
    
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.String(100), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Statut de la conversation
    is_active = db.Column(db.Boolean, default=True)
    started_at = db.Column(db.DateTime, default=datetime.utcnow)
    ended_at = db.Column(db.DateTime, nullable=True)
    
    # Métadonnées de la conversation
    total_messages = db.Column(db.Integer, default=0)
    ai_responses = db.Column(db.Integer, default=0)
    human_responses = db.Column(db.Integer, default=0)
    
    # Évaluation
    user_satisfaction = db.Column(db.Integer, nullable=True)  # 1-5
    feedback = db.Column(db.Text, nullable=True)
    
    # Relations
    user = db.relationship('User', backref='support_conversations')
    chat_messages = db.relationship('SupportChatMessage', backref='conversation', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<SupportConversation {self.session_id}>'

class SupportChatMessage(db.Model):
    """Modèle pour les messages de chat en temps réel"""
    __tablename__ = 'support_chat_messages'
    
    id = db.Column(db.Integer, primary_key=True)
    conversation_id = db.Column(db.Integer, db.ForeignKey('support_conversations.id'), nullable=False)
    
    # Contenu du message
    content = db.Column(db.Text, nullable=False)
    sender_type = db.Column(db.Enum(MessageSender), nullable=False)
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    
    # Métadonnées
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    read_at = db.Column(db.DateTime, nullable=True)
    
    # Données IA
    ai_confidence = db.Column(db.Float, nullable=True)
    ai_model_used = db.Column(db.String(50), nullable=True)
    processing_time = db.Column(db.Float, nullable=True)
    
    # Relations
    sender = db.relationship('User', foreign_keys=[sender_id])
    
    def __repr__(self):
        return f'<SupportChatMessage {self.id}: {self.sender_type.value}>'

class SupportKnowledgeBase(db.Model):
    """Base de connaissances pour l'IA"""
    __tablename__ = 'support_knowledge_base'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    category = db.Column(db.Enum(SupportTicketCategory), nullable=False)
    
    # Métadonnées
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Statistiques d'utilisation
    usage_count = db.Column(db.Integer, default=0)
    last_used = db.Column(db.DateTime, nullable=True)
    
    # Tags pour recherche
    tags = db.Column(db.String(500), nullable=True)  # Tags séparés par des virgules
    
    # Relations
    created_by = db.relationship('User', backref='knowledge_articles')
    
    def __repr__(self):
        return f'<SupportKnowledgeBase {self.title}>'
    
    def get_tags_list(self):
        """Retourne la liste des tags"""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',')]
        return []
    
    def set_tags_list(self, tags_list):
        """Définit les tags à partir d'une liste"""
        self.tags = ', '.join(tags_list) if tags_list else None

class SupportAnalytics(db.Model):
    """Modèle pour les analytics du support"""
    __tablename__ = 'support_analytics'
    
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.Date, nullable=False)
    
    # Statistiques des tickets
    tickets_created = db.Column(db.Integer, default=0)
    tickets_resolved = db.Column(db.Integer, default=0)
    tickets_escalated = db.Column(db.Integer, default=0)
    
    # Statistiques de l'IA
    ai_responses = db.Column(db.Integer, default=0)
    ai_success_rate = db.Column(db.Float, default=0.0)
    average_ai_confidence = db.Column(db.Float, default=0.0)
    average_response_time = db.Column(db.Float, default=0.0)
    
    # Satisfaction client
    average_satisfaction = db.Column(db.Float, default=0.0)
    total_ratings = db.Column(db.Integer, default=0)
    
    def __repr__(self):
        return f'<SupportAnalytics {self.date}>'
