import pytest
from flask import url_for
import re
from unittest.mock import patch, MagicMock
import os
import sqlite3
from datetime import datetime
from flask_login import login_user, current_user
from app.extensions import db
from app.modules.auth.models import User

def test_app_is_created(app):
    assert app is not None
    assert app.config['TESTING'] is True

def test_client_is_created(client):
    assert client is not None
    response = client.get('/')
    assert response.status_code in (200, 302)  # 200 OK ou 302 Redirect

def test_database_is_empty(app):
    with app.app_context():
        assert User.query.count() == 0 