<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}{{ site.site_name or site.owner.username }}{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS with site colors -->
    <style>
        :root {
            --primary-color: {{ site.primary_color or '#4e73df' }};
            --secondary-color: {{ site.secondary_color or '#858796' }};
        }
        
        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: bold;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            opacity: 0.9;
        }
        
        .text-primary {
            color: var(--primary-color) !important;
        }
        
        .bg-primary {
            background-color: var(--primary-color) !important;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
        }
        
        .product-card {
            transition: transform 0.2s;
            cursor: pointer;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .sticky-cart {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .sticky-cart {
                bottom: 10px;
                right: 10px;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('online_ordering_sites.index') }}">
                {% if site.logo_path %}
                    <img src="{{ url_for('static', filename='uploads/' + site.logo_path) }}" alt="Logo" height="40">
                {% else %}
                    <i class="fas fa-utensils me-2"></i>{{ site.site_name or site.owner.username }}
                {% endif %}
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('online_ordering_sites.index') }}">
                            <i class="fas fa-home me-1"></i>Accueil
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('online_ordering_sites.menu') }}">
                            <i class="fas fa-list me-1"></i>Menu
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showTrackOrderModal()">
                            <i class="fas fa-search-location me-1"></i>Suivre ma commande
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {% if session.customer_id %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>Mon Compte
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('online_ordering_sites.customer_profile') }}"><i class="fas fa-user me-2"></i>Profil</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('online_ordering_sites.customer_orders') }}"><i class="fas fa-history me-2"></i>Mes Commandes</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('online_ordering_sites.customer_favorites') }}"><i class="fas fa-heart me-2"></i>Favoris</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('online_ordering_sites.customer_logout') }}">
                                    <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('online_ordering_sites.customer_login') }}">
                                <i class="fas fa-sign-in-alt me-1"></i>Connexion
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('online_ordering_sites.customer_register') }}">
                                <i class="fas fa-user-plus me-1"></i>Inscription
                            </a>
                        </li>
                    {% endif %}
                    
                    <!-- Cart Button -->
                    <li class="nav-item">
                        <button class="btn btn-outline-primary position-relative" id="cart-btn" data-bs-toggle="modal" data-bs-target="#cartModal">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-badge" id="cart-count">0</span>
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>{{ site.site_name or site.owner.username }}</h5>
                    {% if site.site_description %}
                        <p class="text-muted">{{ site.site_description }}</p>
                    {% endif %}
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">
                        <i class="fas fa-clock me-1"></i>
                        Commande en ligne disponible 24h/24
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Cart Modal -->
    <div class="modal fade" id="cartModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-shopping-cart me-2"></i>Mon Panier
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="cart-content">
                    <!-- Cart content will be loaded here -->
                    <div id="empty-cart" class="text-center py-4">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Votre panier est vide</p>
                    </div>
                    <div id="cart-items" style="display: none;">
                        <!-- Cart items will be displayed here -->
                    </div>
                </div>
                <div class="modal-footer" id="cart-footer">
                    <div class="w-100">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="h5 mb-0">Total: <span id="cart-total">0.00€</span></span>
                            <button type="button" class="btn btn-outline-danger btn-sm" id="clear-cart-btn" onclick="clearCart()">
                                <i class="fas fa-trash"></i> Vider le panier
                            </button>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-secondary flex-grow-1" data-bs-dismiss="modal">
                                Continuer mes achats
                            </button>
                            <button type="button" class="btn btn-primary flex-grow-1" id="checkout-btn" disabled onclick="proceedToCheckout()">
                                <i class="fas fa-credit-card me-1"></i>Commander
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Track Order Modal -->
    <div class="modal fade" id="trackOrderModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-search-location me-2"></i>Suivre ma commande
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form onsubmit="trackOrder(event)">
                        <div class="mb-3">
                            <label for="order-number" class="form-label">Numéro de commande</label>
                            <input type="text" class="form-control" id="order-number"
                                   placeholder="Ex: ORD-2024-001" required>
                            <div class="form-text">
                                Vous avez reçu ce numéro par email ou SMS après votre commande
                            </div>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Suivre ma commande
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Cart functionality
        let cart = JSON.parse(localStorage.getItem('cart_{{ site.id }}') || '[]');
        
        function updateCartDisplay() {
            const cartCount = cart.reduce((sum, item) => sum + item.quantity, 0);
            const cartTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

            // Update cart count in header
            document.getElementById('cart-count').textContent = cartCount;

            // Update cart total
            document.getElementById('cart-total').textContent = cartTotal.toFixed(2) + '€';

            // Show/hide cart sections
            if (cartCount > 0) {
                document.getElementById('empty-cart').style.display = 'none';
                document.getElementById('cart-items').style.display = 'block';
                document.getElementById('checkout-btn').disabled = false;
                document.getElementById('clear-cart-btn').style.display = 'inline-block';
                renderCartItems();
            } else {
                document.getElementById('empty-cart').style.display = 'block';
                document.getElementById('cart-items').style.display = 'none';
                document.getElementById('checkout-btn').disabled = true;
                document.getElementById('clear-cart-btn').style.display = 'none';
            }
        }

        function renderCartItems() {
            const cartItemsContainer = document.getElementById('cart-items');
            cartItemsContainer.innerHTML = '';

            cart.forEach((item, index) => {
                const itemElement = document.createElement('div');
                itemElement.className = 'cart-item border-bottom pb-3 mb-3';
                itemElement.innerHTML = `
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="mb-1">${item.productName}</h6>
                            <small class="text-muted">${item.price.toFixed(2)}€ l'unité</small>
                        </div>
                        <div class="col-md-3">
                            <div class="input-group input-group-sm">
                                <button class="btn btn-outline-secondary" type="button" onclick="updateCartItemQuantity(${index}, ${item.quantity - 1})">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" class="form-control text-center" value="${item.quantity}" min="1"
                                       onchange="updateCartItemQuantity(${index}, this.value)">
                                <button class="btn btn-outline-secondary" type="button" onclick="updateCartItemQuantity(${index}, ${item.quantity + 1})">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-2 text-end">
                            <strong>${(item.price * item.quantity).toFixed(2)}€</strong>
                        </div>
                        <div class="col-md-1 text-end">
                            <button class="btn btn-outline-danger btn-sm" onclick="removeFromCart(${index})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
                cartItemsContainer.appendChild(itemElement);
            });
        }
        
        function addToCart(productId, productName, price, quantity = 1) {
            const existingItem = cart.find(item => item.productId === productId);

            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                cart.push({
                    productId: productId,
                    productName: productName,
                    price: price,
                    quantity: quantity
                });
            }

            localStorage.setItem('cart_{{ site.id }}', JSON.stringify(cart));
            updateCartDisplay();

            // Show success message
            showToast('Produit ajouté au panier', 'success');
        }

        function updateCartItemQuantity(index, newQuantity) {
            newQuantity = parseInt(newQuantity);
            if (newQuantity <= 0) {
                removeFromCart(index);
                return;
            }

            if (cart[index]) {
                cart[index].quantity = newQuantity;
                localStorage.setItem('cart_{{ site.id }}', JSON.stringify(cart));
                updateCartDisplay();
            }
        }

        function removeFromCart(index) {
            if (cart[index]) {
                cart.splice(index, 1);
                localStorage.setItem('cart_{{ site.id }}', JSON.stringify(cart));
                updateCartDisplay();
                showToast('Produit retiré du panier', 'info');
            }
        }

        function clearCart() {
            if (confirm('Êtes-vous sûr de vouloir vider votre panier ?')) {
                cart = [];
                localStorage.setItem('cart_{{ site.id }}', JSON.stringify(cart));
                updateCartDisplay();
                showToast('Panier vidé', 'info');
            }
        }

        function proceedToCheckout() {
            if (cart.length === 0) {
                showToast('Votre panier est vide', 'warning');
                return;
            }

            // Close cart modal
            const cartModal = bootstrap.Modal.getInstance(document.getElementById('cartModal'));
            if (cartModal) {
                cartModal.hide();
            }

            // Redirect to checkout
            window.location.href = '{{ url_for("online_ordering_sites.checkout") }}';
        }
        
        function showToast(message, type = 'info') {
            // Simple toast notification
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 3000);
        }
        
        // Initialize cart display
        updateCartDisplay();

        // Track order functionality
        function showTrackOrderModal() {
            const modal = new bootstrap.Modal(document.getElementById('trackOrderModal'));
            modal.show();
        }

        function trackOrder(event) {
            event.preventDefault();
            const orderNumber = document.getElementById('order-number').value.trim();

            if (!orderNumber) {
                showToast('Veuillez saisir un numéro de commande', 'warning');
                return;
            }

            // Redirect to track order page
            window.location.href = `/online-ordering/track/${orderNumber}`;
        }

        // Socket.IO pour les notifications en temps réel
        {% if session.customer_id %}
        const socket = io();

        // Connexion du client
        socket.emit('connect_customer', {
            customer_id: {{ session.customer_id }},
            site_id: {{ site.id }}
        });

        // Écouter les mises à jour de statut de commande
        socket.on('order_status_update', function(data) {
            showToast(`Commande #${data.order_number}: ${data.message}`, 'info');

            // Mettre à jour la page si on est sur le suivi de commande
            if (window.location.pathname.includes('/track/')) {
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            }
        });

        // Écouter les nouvelles notifications
        socket.on('new_notification', function(data) {
            showToast(data.title + ': ' + data.message, 'info');
        });

        // Gérer la connexion
        socket.on('connected', function(data) {
            console.log('Connecté aux notifications en temps réel');
        });

        socket.on('disconnect', function() {
            console.log('Déconnecté des notifications');
        });
        {% endif %}
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
