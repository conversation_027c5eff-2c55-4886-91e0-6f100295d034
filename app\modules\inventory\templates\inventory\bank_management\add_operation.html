{% extends "base.html" %}

{% block title %}Nouvelle Opération Bancaire{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-2">
                <h4 class="mb-0">
                    <i class="fas fa-plus"></i> Nouvelle Opération Bancaire
                </h4>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.bank_management_index') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-university"></i> Détails de l'Opération</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="operationForm">
                            {{ form.hidden_tag() }}
                            
                            <!-- Compte bancaire -->
                            <div class="mb-3">
                                {{ form.bank_account_id.label(class="form-label") }}
                                {{ form.bank_account_id(class="form-select") }}
                                {% if form.bank_account_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.bank_account_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Type d'opération -->
                            <div class="mb-3">
                                {{ form.operation_type.label(class="form-label") }}
                                {{ form.operation_type(class="form-select", id="operationType") }}
                                {% if form.operation_type.errors %}
                                    <div class="text-danger">
                                        {% for error in form.operation_type.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Montant -->
                            <div class="mb-3">
                                {{ form.amount.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.amount(class="form-control", step="0.01", min="0") }}
                                    <span class="input-group-text">€</span>
                                </div>
                                {% if form.amount.errors %}
                                    <div class="text-danger">
                                        {% for error in form.amount.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Description -->
                            <div class="mb-3">
                                {{ form.description.label(class="form-label") }}
                                {{ form.description(class="form-control") }}
                                {% if form.description.errors %}
                                    <div class="text-danger">
                                        {% for error in form.description.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Référence -->
                            <div class="mb-3">
                                {{ form.reference.label(class="form-label") }}
                                {{ form.reference(class="form-control", placeholder="Numéro de chèque, référence virement...") }}
                                {% if form.reference.errors %}
                                    <div class="text-danger">
                                        {% for error in form.reference.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Date d'opération -->
                            <div class="mb-3">
                                {{ form.operation_date.label(class="form-label") }}
                                {{ form.operation_date(class="form-control") }}
                                {% if form.operation_date.errors %}
                                    <div class="text-danger">
                                        {% for error in form.operation_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Section virement (masquée par défaut) -->
                            <div id="transferSection" style="display: none;">
                                <div class="mb-3">
                                    <label class="form-label">Compte de destination</label>
                                    <select class="form-select" name="target_account_id" id="targetAccount">
                                        <option value="">Sélectionner un compte</option>
                                        {% for account in bank_accounts %}
                                        <option value="{{ account.id }}">{{ account.name }} ({{ "%.2f"|format(account.balance) }} €)</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <!-- Section dépôt caisse (masquée par défaut) -->
                            <div id="cashDepositSection" style="display: none;">
                                <div class="mb-3">
                                    <label class="form-label">Caisse source</label>
                                    <select class="form-select" name="cash_register_id" id="cashRegister">
                                        <option value="">Sélectionner une caisse</option>
                                        {% for register in cash_registers %}
                                        <option value="{{ register.id }}">{{ register.name }} ({{ "%.2f"|format(register.current_balance) }} €)</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="mb-3">
                                {{ form.notes.label(class="form-label") }}
                                {{ form.notes(class="form-control", rows="3") }}
                                {% if form.notes.errors %}
                                    <div class="text-danger">
                                        {% for error in form.notes.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Aperçu de l'opération -->
                            <div class="alert alert-info" id="operationPreview" style="display: none;">
                                <h6><i class="fas fa-info-circle"></i> Aperçu de l'opération</h6>
                                <div id="previewContent"></div>
                            </div>

                            <!-- Boutons -->
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('inventory.bank_management_index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> Enregistrer l'opération
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const operationType = document.getElementById('operationType');
    const transferSection = document.getElementById('transferSection');
    const cashDepositSection = document.getElementById('cashDepositSection');
    const operationPreview = document.getElementById('operationPreview');
    const previewContent = document.getElementById('previewContent');
    const form = document.getElementById('operationForm');

    // Gérer l'affichage des sections selon le type d'opération
    operationType.addEventListener('change', function() {
        const type = this.value;
        
        // Masquer toutes les sections spéciales
        transferSection.style.display = 'none';
        cashDepositSection.style.display = 'none';
        
        // Afficher la section appropriée
        if (type === 'TRANSFER') {
            transferSection.style.display = 'block';
        } else if (type === 'CASH_DEPOSIT') {
            cashDepositSection.style.display = 'block';
        }
        
        updatePreview();
    });

    // Mettre à jour l'aperçu en temps réel
    form.addEventListener('input', updatePreview);
    form.addEventListener('change', updatePreview);

    function updatePreview() {
        const accountSelect = document.querySelector('select[name="bank_account_id"]');
        const typeSelect = document.getElementById('operationType');
        const amountInput = document.querySelector('input[name="amount"]');
        const descriptionInput = document.querySelector('input[name="description"]');
        
        const account = accountSelect.options[accountSelect.selectedIndex]?.text || '';
        const type = typeSelect.value;
        const amount = parseFloat(amountInput.value) || 0;
        const description = descriptionInput.value;
        
        if (account && type && amount > 0) {
            let typeText = '';
            let impactText = '';
            
            switch(type) {
                case 'DEPOSIT':
                    typeText = 'Dépôt';
                    impactText = `<span class="text-success">+${amount.toFixed(2)} €</span>`;
                    break;
                case 'WITHDRAWAL':
                    typeText = 'Retrait';
                    impactText = `<span class="text-danger">-${amount.toFixed(2)} €</span>`;
                    break;
                case 'TRANSFER':
                    typeText = 'Virement';
                    impactText = `<span class="text-danger">-${amount.toFixed(2)} €</span>`;
                    break;
                case 'CASH_DEPOSIT':
                    typeText = 'Dépôt depuis caisse';
                    impactText = `<span class="text-success">+${amount.toFixed(2)} €</span>`;
                    break;
                case 'FEE':
                    typeText = 'Frais bancaires';
                    impactText = `<span class="text-danger">-${amount.toFixed(2)} €</span>`;
                    break;
                case 'INTEREST':
                    typeText = 'Intérêts';
                    impactText = `<span class="text-success">+${amount.toFixed(2)} €</span>`;
                    break;
            }
            
            previewContent.innerHTML = `
                <div class="row">
                    <div class="col-6">
                        <strong>Compte:</strong><br>
                        ${account.split(' (')[0]}
                    </div>
                    <div class="col-6">
                        <strong>Type:</strong><br>
                        ${typeText}
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <strong>Montant:</strong><br>
                        ${impactText}
                    </div>
                    <div class="col-6">
                        <strong>Description:</strong><br>
                        ${description || 'Aucune description'}
                    </div>
                </div>
            `;
            
            operationPreview.style.display = 'block';
        } else {
            operationPreview.style.display = 'none';
        }
    }

    // Validation du formulaire
    form.addEventListener('submit', function(e) {
        const type = operationType.value;
        const amount = parseFloat(document.querySelector('input[name="amount"]').value) || 0;
        
        if (type === 'TRANSFER') {
            const targetAccount = document.getElementById('targetAccount').value;
            if (!targetAccount) {
                e.preventDefault();
                alert('Veuillez sélectionner un compte de destination pour le virement');
                return;
            }
        }
        
        if (type === 'CASH_DEPOSIT') {
            const cashRegister = document.getElementById('cashRegister').value;
            if (!cashRegister) {
                e.preventDefault();
                alert('Veuillez sélectionner une caisse source pour le dépôt');
                return;
            }
        }
        
        if (amount <= 0) {
            e.preventDefault();
            alert('Le montant doit être supérieur à 0');
            return;
        }
        
        // Confirmation pour les gros montants
        if (amount > 1000) {
            if (!confirm(`Confirmer l'opération de ${amount.toFixed(2)} € ?`)) {
                e.preventDefault();
                return;
            }
        }
    });

    // Initialiser la date à aujourd'hui
    const dateInput = document.querySelector('input[name="operation_date"]');
    if (dateInput && !dateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
    }
});
</script>
{% endblock %}
