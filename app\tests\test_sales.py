"""
Test cases for Sale model
"""
import pytest
from datetime import datetime
from app.extensions import db
from app.modules.pos.models_sale import Sale, SaleItem
from app.modules.auth.models import User
from app.modules.inventory.models_product import Product, ProductCategory

@pytest.fixture
def sample_product(app):
    """Create a sample product for testing."""
    with app.app_context():
        owner = User(username='product_owner', email='<EMAIL>')
        owner.set_password('test123')
        db.session.add(owner)
        
        category = ProductCategory(name='Test Category', owner=owner)
        db.session.add(category)
        
        product = Product(
            name='Test Product',
            description='A test product',
            price=10.99,
            stock_quantity=100,
            category=category,
            owner=owner
        )
        db.session.add(product)
        db.session.commit()
        return product

def test_create_sale(app, sample_product):
    """
    GIVEN a Sale model
    WHEN a new Sale is created
    THEN check the fields are defined correctly
    """
    with app.app_context():
        user = User(username='seller', email='<EMAIL>')
        user.set_password('test123')
        db.session.add(user)
        db.session.commit()

        # Ensure sample_product is attached to the session
        db.session.add(sample_product)
        db.session.refresh(sample_product)

        sale = Sale(user_id=user.id, owner_id=user.id)
        db.session.add(sale)
        
        sale_item = SaleItem(
            sale=sale,
            product=sample_product,
            quantity=2,
            price=sample_product.price,
            total=sample_product.price * 2
        )
        db.session.add(sale_item)
        sale.calculate_totals()
        db.session.commit()

        assert sale.user_id == user.id
        assert sale.total == sample_product.price * 2

def test_sale_stock_update(app, sample_product):
    """
    GIVEN a Sale
    WHEN a sale is completed
    THEN check the product stock is updated
    """
    with app.app_context():
        # Refresh the sample_product to ensure it's attached to the session
        db.session.add(sample_product)
        db.session.refresh(sample_product)
        initial_stock = sample_product.stock_quantity

        user = User(username='seller2', email='<EMAIL>')
        user.set_password('test123')
        db.session.add(user)
        db.session.commit()

        sale = Sale(user_id=user.id, owner_id=user.id)
        db.session.add(sale)
        
        sale_item = SaleItem(
            sale=sale,
            product=sample_product,
            quantity=5,
            price=sample_product.price,
            total=sample_product.price * 5
        )
        db.session.add(sale_item)
        sale.calculate_totals()
        
        # Update stock - use 'remove' operation to subtract stock
        sample_product.update_stock(5, operation='remove')
        db.session.commit()

        assert sample_product.stock_quantity == initial_stock - 5

def test_multiple_items_sale(app, sample_product):
    """
    GIVEN a Sale
    WHEN multiple items are added
    THEN check the total is calculated correctly
    """
    with app.app_context():
        # Refresh the sample_product to ensure it's attached to the session
        db.session.add(sample_product)
        db.session.refresh(sample_product)

        user = User(username='seller3', email='<EMAIL>')
        user.set_password('test123')
        db.session.add(user)

        # Create a second product
        category = ProductCategory.query.first()
        product2 = Product(
            name='Second Product',
            description='Another test product',
            price=20.99,
            stock_quantity=50,
            category=category,
            owner=user
        )
        db.session.add(product2)
        db.session.commit()

        sale = Sale(user_id=user.id, owner_id=user.id)
        db.session.add(sale)
        
        item1 = SaleItem(
            sale=sale,
            product=sample_product,
            quantity=2,
            price=sample_product.price,
            total=sample_product.price * 2
        )
        db.session.add(item1)
        
        item2 = SaleItem(
            sale=sale,
            product=product2,
            quantity=1,
            price=product2.price,
            total=product2.price
        )
        db.session.add(item2)
        
        sale.calculate_totals()
        db.session.commit()

        expected_total = (2 * sample_product.price) + (1 * product2.price)
        assert sale.total == expected_total

def test_sale_validation(app, sample_product):
    """
    GIVEN a Sale
    WHEN invalid data is provided
    THEN check the validation fails
    """
    with app.app_context():
        # Ensure sample_product is attached to the session
        db.session.add(sample_product)
        db.session.refresh(sample_product)

        user = User(username='seller4', email='<EMAIL>')
        user.set_password('test123')
        db.session.add(user)
        db.session.commit()

        # Test invalid quantity
        sale = Sale(user_id=user.id, owner_id=user.id)
        db.session.add(sale)
        
        sale_item = SaleItem(
            sale=sale,
            product=sample_product,
            quantity=0,  # Invalid quantity
            price=sample_product.price,
            total=0
        )
        db.session.add(sale_item)
        
        try:
            db.session.commit()
            assert False, "Should have raised an error"
        except:
            db.session.rollback()

def test_sale_date_default(app):
    """
    GIVEN a Sale model
    WHEN a new Sale is created
    THEN check the dates are set correctly
    """
    with app.app_context():
        user = User(username='seller5', email='<EMAIL>')
        user.set_password('test123')
        db.session.add(user)
        db.session.commit()

        sale = Sale(user_id=user.id, owner_id=user.id)
        db.session.add(sale)
        db.session.commit()

        assert sale.created_at is not None
        assert (datetime.utcnow() - sale.created_at).total_seconds() < 60  # Within a minute 