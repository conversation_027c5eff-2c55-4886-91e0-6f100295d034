{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    {% if user.avatar_path %}
                    <img src="{{ url_for('static', filename=user.avatar_path) }}" 
                         class="rounded-circle me-3" 
                         alt="{{ user.name }}"
                         style="width: 48px; height: 48px; object-fit: cover;">
                    {% else %}
                    <div class="bg-primary rounded-circle me-3 text-center text-white" style="width: 48px; height: 48px; line-height: 48px;">
                        {{ user.initials }}
                    </div>
                    {% endif %}
                    <div>
                        <h1 class="h3 mb-0 text-gray-800">
                            {{ user.name }}
                            {% if user.id == current_user.id %}
                            <span class="badge bg-primary ms-1">Vous</span>
                            {% endif %}
                        </h1>
                        <div class="text-muted">
                            Membre depuis {{ user.created_at.strftime('%d/%m/%Y') }}
                        </div>
                    </div>
                </div>
                <div>
                    {% if user.id != current_user.id %}
                    <button type="button" class="btn btn-outline-danger" 
                            data-bs-toggle="modal" 
                            data-bs-target="#deleteUserModal"
                            data-user-id="{{ user.id }}"
                            data-user-name="{{ user.name }}">
                        <i class="fas fa-trash"></i>
                        Supprimer
                    </button>
                    {% endif %}
                    <button type="button" class="btn btn-primary"
                            data-bs-toggle="modal"
                            data-bs-target="#userModal"
                            data-user-id="{{ user.id }}"
                            data-user-name="{{ user.name }}"
                            data-user-email="{{ user.email }}"
                            data-user-roles="{{ user.roles|map(attribute='id')|list|tojson }}"
                            data-user-is-active="{{ user.is_active|tojson }}">
                        <i class="fas fa-edit"></i>
                        Modifier
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Activity History -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history"></i>
                        Historique d'activité
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        {% for activity in activities %}
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                {% if activity.type == 'login' %}
                                <i class="fas fa-sign-in-alt text-success"></i>
                                {% elif activity.type == 'logout' %}
                                <i class="fas fa-sign-out-alt text-danger"></i>
                                {% elif activity.type == 'create' %}
                                <i class="fas fa-plus text-primary"></i>
                                {% elif activity.type == 'update' %}
                                <i class="fas fa-edit text-info"></i>
                                {% elif activity.type == 'delete' %}
                                <i class="fas fa-trash text-danger"></i>
                                {% else %}
                                <i class="fas fa-circle text-muted"></i>
                                {% endif %}
                            </div>
                            <div class="timeline-content">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <div>
                                        {% if activity.type == 'login' %}
                                        <span class="badge bg-success">Connexion</span>
                                        {% elif activity.type == 'logout' %}
                                        <span class="badge bg-danger">Déconnexion</span>
                                        {% elif activity.type == 'create' %}
                                        <span class="badge bg-primary">Création</span>
                                        {% elif activity.type == 'update' %}
                                        <span class="badge bg-info">Modification</span>
                                        {% elif activity.type == 'delete' %}
                                        <span class="badge bg-danger">Suppression</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ activity.type }}</span>
                                        {% endif %}
                                    </div>
                                    <small class="text-muted">
                                        {{ activity.created_at.strftime('%d/%m/%Y à %H:%M') }}
                                    </small>
                                </div>
                                <p class="mb-0">{{ activity.description }}</p>
                                {% if activity.details %}
                                <small class="text-muted">{{ activity.details }}</small>
                                {% endif %}
                            </div>
                        </div>
                        {% else %}
                        <p class="text-center text-muted mb-0">
                            Aucune activité
                        </p>
                        {% endfor %}
                    </div>

                    {% if activities %}
                    <!-- Pagination -->
                    <nav class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item {% if not activities.has_prev %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('admin.users.view', user_id=user.id, page=activities.prev_num) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            {% for page in activities.iter_pages() %}
                                {% if page %}
                                <li class="page-item {% if page == activities.page %}active{% endif %}">
                                    <a class="page-link" href="{{ url_for('admin.users.view', user_id=user.id, page=page) }}">
                                        {{ page }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            <li class="page-item {% if not activities.has_next %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('admin.users.view', user_id=user.id, page=activities.next_num) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- User Info Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i>
                        Informations
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Email</h6>
                        <div>
                            <a href="mailto:{{ user.email }}" class="text-primary">
                                <i class="fas fa-envelope"></i>
                                {{ user.email }}
                            </a>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6>Rôles</h6>
                        <div>
                            {% for role in user.roles %}
                            <span class="badge" style="background-color: {{ role.color }}">
                                {{ role.name }}
                            </span>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6>Statut</h6>
                        <div>
                            {% if user.is_active %}
                            <span class="badge bg-success">Actif</span>
                            {% else %}
                            <span class="badge bg-danger">Inactif</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6>Dernière connexion</h6>
                        <div>
                            {% if user.last_login_at %}
                            {{ user.last_login_at.strftime('%d/%m/%Y à %H:%M') }}
                            {% else %}
                            <span class="text-muted">Jamais</span>
                            {% endif %}
                        </div>
                    </div>

                    <div>
                        <h6>Dernière activité</h6>
                        <div>
                            {% if user.last_activity_at %}
                            {{ user.last_activity_at.strftime('%d/%m/%Y à %H:%M') }}
                            {% else %}
                            <span class="text-muted">Jamais</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activity Stats Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line"></i>
                        Statistiques
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6>Actions (7j)</h6>
                        <div class="h4 mb-0">{{ user.actions_count_7d }}</div>
                        <small class="text-muted">
                            {% if user.actions_count_7d > average_actions_7d %}
                            {{ "%.1f"|format(user.actions_count_7d / average_actions_7d) }}x plus que la moyenne
                            {% else %}
                            {{ "%.1f"|format(average_actions_7d / user.actions_count_7d) }}x moins que la moyenne
                            {% endif %}
                        </small>
                    </div>

                    <div class="mb-4">
                        <h6>Connexions (30j)</h6>
                        <div class="h4 mb-0">{{ user.logins_count_30d }}</div>
                        <small class="text-muted">
                            {{ "%.1f"|format(user.logins_count_30d / 30) }} par jour
                        </small>
                    </div>

                    <div>
                        <h6>Total actions</h6>
                        <div class="h4 mb-0">{{ user.total_actions }}</div>
                        <small class="text-muted">
                            depuis le {{ user.created_at.strftime('%d/%m/%Y') }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Modal -->
<div class="modal fade" id="userModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Modifier l'utilisateur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="userForm" method="POST" action="{{ url_for('admin.users.update', user_id=user.id) }}">
                    <div class="mb-3">
                        <label class="form-label">Nom</label>
                        <input type="text" name="name" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" name="email" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Mot de passe</label>
                        <input type="password" name="password" class="form-control">
                        <small class="text-muted">
                            Laissez vide pour conserver le mot de passe actuel
                        </small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Rôles</label>
                        <select name="roles[]" class="form-select" multiple required size="5">
                            {% for role in roles %}
                            <option value="{{ role.id }}" style="color: {{ role.color }}">
                                {{ role.name }}
                            </option>
                            {% endfor %}
                        </select>
                        <small class="text-muted">
                            Maintenez Ctrl pour sélectionner plusieurs rôles
                        </small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input type="checkbox" name="is_active" class="form-check-input" id="is-active">
                            <label class="form-check-label" for="is-active">
                                Utilisateur actif
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="submit" form="userForm" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Enregistrer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer l'utilisateur <strong id="delete-user-name"></strong> ?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    Cette action est irréversible. L'historique des actions de l'utilisateur sera conservé.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteUserForm" method="POST" action="{{ url_for('admin.users.delete', user_id=user.id) }}">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i>
                        Supprimer
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 3rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
}

.timeline-item:last-child {
    padding-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    width: 2rem;
    height: 2rem;
    text-align: center;
    line-height: 2rem;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 0 0 2px #e3e6f0;
}

.timeline-content {
    background: #fff;
    padding: 1rem;
    border-radius: 0.35rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // User Modal
    const userModal = document.getElementById('userModal');
    const userForm = document.getElementById('userForm');
    const userNameInput = userForm.querySelector('input[name="name"]');
    const userEmailInput = userForm.querySelector('input[name="email"]');
    const userRolesSelect = userForm.querySelector('select[name="roles[]"]');
    const userIsActiveInput = document.getElementById('is-active');

    userModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        userNameInput.value = button.dataset.userName;
        userEmailInput.value = button.dataset.userEmail;
        userIsActiveInput.checked = JSON.parse(button.dataset.userIsActive);
        
        const userRoles = JSON.parse(button.dataset.userRoles);
        Array.from(userRolesSelect.options).forEach(option => {
            option.selected = userRoles.includes(parseInt(option.value));
        });
    });

    // Delete User Modal
    const deleteUserModal = document.getElementById('deleteUserModal');
    deleteUserModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        document.getElementById('delete-user-name').textContent = button.dataset.userName;
    });
});
</script>
{% endblock %}
{% endblock %} 