{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Réserver la Table {{ table.number }}</h2>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <strong>Informations sur la table:</strong><br>
                        Capacité: {{ table.capacity }} personnes<br>
                        Emplacement: 
                        {% if table.location == 'interior' %}
                            Intérieur
                        {% elif table.location == 'terrace' %}
                            Terrasse
                        {% elif table.location == 'bar' %}
                            Bar
                        {% else %}
                            Salle privée
                        {% endif %}
                    </div>

                    <form method="POST">
                        {{ form.csrf_token }}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.customer_name.label(class="form-label") }}
                                    {{ form.customer_name(class="form-control") }}
                                    {% if form.customer_name.errors %}
                                        {% for error in form.customer_name.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.customer_phone.label(class="form-label") }}
                                    {{ form.customer_phone(class="form-control") }}
                                    {% if form.customer_phone.errors %}
                                        {% for error in form.customer_phone.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.number_of_guests.label(class="form-label") }}
                                    {{ form.number_of_guests(class="form-control") }}
                                    {% if form.number_of_guests.errors %}
                                        {% for error in form.number_of_guests.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.reservation_date.label(class="form-label") }}
                                    {{ form.reservation_date(class="form-control", type="datetime-local") }}
                                    {% if form.reservation_date.errors %}
                                        {% for error in form.reservation_date.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.duration_minutes.label(class="form-label") }}
                                    {{ form.duration_minutes(class="form-control") }}
                                    {% if form.duration_minutes.errors %}
                                        {% for error in form.duration_minutes.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Durée minimum: 30 minutes, maximum: 8 heures
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            {{ form.notes.label(class="form-label") }}
                            {{ form.notes(class="form-control", rows=3) }}
                            {% if form.notes.errors %}
                                {% for error in form.notes.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                {% endfor %}
                            {% endif %}
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('tables.show', id=table.id) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-calendar-check"></i> Réserver
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 