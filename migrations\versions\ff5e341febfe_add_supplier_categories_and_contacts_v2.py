"""Add supplier categories and contacts v2

Revision ID: ff5e341febfe
Revises: 49aa1f0d438f
Create Date: 2025-07-25 20:44:52.528765

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ff5e341febfe'
down_revision = '49aa1f0d438f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop existing supplier_categories table if it exists
    try:
        op.drop_table('supplier_categories')
    except:
        pass

    # Create supplier_categories table
    op.create_table('supplier_categories',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=64), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('color', sa.String(length=7), nullable=True),
    sa.Column('icon', sa.String(length=50), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('owner_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )

    op.create_table('supplier_contacts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('supplier_id', sa.Integer(), nullable=False),
    sa.Column('contact_type', sa.String(length=50), nullable=False),
    sa.Column('subject', sa.String(length=200), nullable=True),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('contact_date', sa.DateTime(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('is_important', sa.Boolean(), nullable=True),
    sa.Column('follow_up_date', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.ForeignKeyConstraint(['supplier_id'], ['suppliers.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('suppliers', schema=None) as batch_op:
        batch_op.add_column(sa.Column('category_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('website', sa.String(length=200), nullable=True))
        batch_op.add_column(sa.Column('tax_id', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('payment_terms', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('rating', sa.Integer(), nullable=True))
        batch_op.create_foreign_key('fk_suppliers_category_id', 'supplier_categories', ['category_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('suppliers', schema=None) as batch_op:
        batch_op.drop_constraint('fk_suppliers_category_id', type_='foreignkey')
        batch_op.drop_column('rating')
        batch_op.drop_column('payment_terms')
        batch_op.drop_column('tax_id')
        batch_op.drop_column('website')
        batch_op.drop_column('category_id')

    op.drop_table('supplier_contacts')
    op.drop_table('supplier_categories')
    # ### end Alembic commands ###
