{% extends "base.html" %}

{% block title %}Ajustement Rapide de Stock{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-2">
                <h4 class="mb-0">
                    <i class="fas fa-adjust"></i> Ajustement Rapide de Stock
                </h4>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- Formulaire d'ajustement -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-edit"></i> Ajustement de Stock</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="adjustmentForm">
                            {{ form.hidden_tag() }}
                            
                            <!-- Type d'article -->
                            <div class="mb-3">
                                <label class="form-label">Type d'article</label>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="item_type" id="product_type" value="product" checked>
                                    <label class="btn btn-outline-primary" for="product_type">
                                        <i class="fas fa-box"></i> Produit
                                    </label>
                                    
                                    <input type="radio" class="btn-check" name="item_type" id="ingredient_type" value="ingredient">
                                    <label class="btn btn-outline-success" for="ingredient_type">
                                        <i class="fas fa-carrot"></i> Ingrédient
                                    </label>
                                </div>
                            </div>

                            <!-- Sélection du produit -->
                            <div class="mb-3" id="productSection">
                                {{ form.product_id.label(class="form-label") }}
                                {{ form.product_id(class="form-select", id="productSelect") }}
                                {% if form.product_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.product_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Sélection de l'ingrédient -->
                            <div class="mb-3" id="ingredientSection" style="display: none;">
                                {{ form.ingredient_id.label(class="form-label") }}
                                {{ form.ingredient_id(class="form-select", id="ingredientSelect") }}
                                {% if form.ingredient_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.ingredient_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Informations actuelles -->
                            <div class="alert alert-info" id="currentStockInfo" style="display: none;">
                                <h6><i class="fas fa-info-circle"></i> Stock Actuel</h6>
                                <div id="stockDetails"></div>
                            </div>

                            <!-- Type d'ajustement -->
                            <div class="mb-3">
                                {{ form.adjustment_type.label(class="form-label") }}
                                {{ form.adjustment_type(class="form-select", id="adjustmentType") }}
                                {% if form.adjustment_type.errors %}
                                    <div class="text-danger">
                                        {% for error in form.adjustment_type.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Quantité -->
                            <div class="mb-3">
                                {{ form.quantity.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.quantity(class="form-control", step="0.01", min="0") }}
                                    <span class="input-group-text" id="unitDisplay">unité</span>
                                </div>
                                {% if form.quantity.errors %}
                                    <div class="text-danger">
                                        {% for error in form.quantity.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Raison -->
                            <div class="mb-3">
                                {{ form.reason.label(class="form-label") }}
                                {{ form.reason(class="form-control") }}
                                {% if form.reason.errors %}
                                    <div class="text-danger">
                                        {% for error in form.reason.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Notes -->
                            <div class="mb-3">
                                {{ form.notes.label(class="form-label") }}
                                {{ form.notes(class="form-control", rows="3") }}
                                {% if form.notes.errors %}
                                    <div class="text-danger">
                                        {% for error in form.notes.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Boutons -->
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> Appliquer l'ajustement
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Aperçu de l'ajustement -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-eye"></i> Aperçu de l'Ajustement</h6>
                    </div>
                    <div class="card-body">
                        <div id="adjustmentPreview">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                                <p>Sélectionnez un article pour voir l'aperçu</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ajustements récents -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-history"></i> Ajustements Récents</h6>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <!-- Les ajustements récents seront chargés ici -->
                            <div class="text-center text-muted py-3">
                                <small>Aucun ajustement récent</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const itemTypeRadios = document.querySelectorAll('input[name="item_type"]');
    const productSection = document.getElementById('productSection');
    const ingredientSection = document.getElementById('ingredientSection');
    const productSelect = document.getElementById('productSelect');
    const ingredientSelect = document.getElementById('ingredientSelect');
    const currentStockInfo = document.getElementById('currentStockInfo');
    const stockDetails = document.getElementById('stockDetails');
    const adjustmentType = document.getElementById('adjustmentType');
    const quantityInput = document.querySelector('input[name="quantity"]');
    const unitDisplay = document.getElementById('unitDisplay');
    const adjustmentPreview = document.getElementById('adjustmentPreview');

    // Gérer le changement de type d'article
    itemTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'product') {
                productSection.style.display = 'block';
                ingredientSection.style.display = 'none';
                ingredientSelect.value = '';
            } else {
                productSection.style.display = 'none';
                ingredientSection.style.display = 'block';
                productSelect.value = '';
            }
            updateStockInfo();
            updatePreview();
        });
    });

    // Gérer la sélection d'article
    productSelect.addEventListener('change', updateStockInfo);
    ingredientSelect.addEventListener('change', updateStockInfo);
    adjustmentType.addEventListener('change', updatePreview);
    quantityInput.addEventListener('input', updatePreview);

    function updateStockInfo() {
        const itemType = document.querySelector('input[name="item_type"]:checked').value;
        const itemId = itemType === 'product' ? productSelect.value : ingredientSelect.value;
        
        if (!itemId) {
            currentStockInfo.style.display = 'none';
            unitDisplay.textContent = 'unité';
            return;
        }

        // Récupérer les informations de stock
        fetch(`/inventory/api/stock-info/${itemType}/${itemId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    stockDetails.innerHTML = `
                        <div class="row">
                            <div class="col-6">
                                <strong>Article:</strong><br>
                                ${data.name}
                            </div>
                            <div class="col-6">
                                <strong>Stock actuel:</strong><br>
                                <span class="h6 ${data.stock_quantity <= data.minimum_stock ? 'text-danger' : 'text-success'}">
                                    ${data.stock_quantity} ${data.unit || ''}
                                </span>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-6">
                                <strong>Stock minimum:</strong><br>
                                ${data.minimum_stock} ${data.unit || ''}
                            </div>
                            <div class="col-6">
                                <strong>Prix unitaire:</strong><br>
                                ${data.price ? data.price.toFixed(2) + ' €' : 'N/A'}
                            </div>
                        </div>
                    `;
                    
                    unitDisplay.textContent = data.unit || 'unité';
                    currentStockInfo.style.display = 'block';
                    
                    if (data.stock_quantity <= data.minimum_stock) {
                        currentStockInfo.className = 'alert alert-warning';
                    } else {
                        currentStockInfo.className = 'alert alert-info';
                    }
                } else {
                    currentStockInfo.style.display = 'none';
                }
                updatePreview();
            })
            .catch(error => {
                console.error('Erreur:', error);
                currentStockInfo.style.display = 'none';
            });
    }

    function updatePreview() {
        const itemType = document.querySelector('input[name="item_type"]:checked').value;
        const itemId = itemType === 'product' ? productSelect.value : ingredientSelect.value;
        const adjustmentTypeValue = adjustmentType.value;
        const quantity = parseFloat(quantityInput.value) || 0;
        
        if (!itemId || !adjustmentTypeValue || quantity <= 0) {
            adjustmentPreview.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                    <p>Complétez le formulaire pour voir l'aperçu</p>
                </div>
            `;
            return;
        }

        // Récupérer les informations actuelles pour calculer le nouveau stock
        fetch(`/inventory/api/stock-info/${itemType}/${itemId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let newStock = data.stock_quantity;
                    let operation = '';
                    let color = '';
                    
                    switch(adjustmentTypeValue) {
                        case 'increase':
                            newStock += quantity;
                            operation = `+${quantity}`;
                            color = 'text-success';
                            break;
                        case 'decrease':
                            newStock -= quantity;
                            operation = `-${quantity}`;
                            color = 'text-danger';
                            break;
                        case 'set':
                            newStock = quantity;
                            operation = `= ${quantity}`;
                            color = 'text-info';
                            break;
                    }
                    
                    adjustmentPreview.innerHTML = `
                        <div class="text-center">
                            <h6>${data.name}</h6>
                            <div class="my-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Stock actuel:</span>
                                    <strong>${data.stock_quantity} ${data.unit || ''}</strong>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Ajustement:</span>
                                    <strong class="${color}">${operation} ${data.unit || ''}</strong>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Nouveau stock:</span>
                                    <strong class="h6 ${newStock <= data.minimum_stock ? 'text-danger' : 'text-success'}">
                                        ${newStock} ${data.unit || ''}
                                    </strong>
                                </div>
                            </div>
                            ${newStock <= data.minimum_stock ? 
                                '<div class="alert alert-warning alert-sm">⚠️ Stock sous le minimum</div>' : 
                                ''
                            }
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
            });
    }

    // Validation du formulaire
    document.getElementById('adjustmentForm').addEventListener('submit', function(e) {
        const itemType = document.querySelector('input[name="item_type"]:checked').value;
        const itemId = itemType === 'product' ? productSelect.value : ingredientSelect.value;
        const quantity = parseFloat(quantityInput.value) || 0;
        
        if (!itemId) {
            e.preventDefault();
            alert('Veuillez sélectionner un article');
            return;
        }
        
        if (quantity <= 0) {
            e.preventDefault();
            alert('La quantité doit être supérieure à 0');
            return;
        }
        
        if (!confirm('Confirmer cet ajustement de stock ?')) {
            e.preventDefault();
        }
    });

    // Charger les ajustements récents
    loadRecentAdjustments();

    function loadRecentAdjustments() {
        fetch('/inventory/api/recent-stock-adjustments')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.adjustments.length > 0) {
                    const container = document.querySelector('.list-group');
                    container.innerHTML = data.adjustments.map(adj => `
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between">
                                <small><strong>${adj.item_name}</strong></small>
                                <small class="text-muted">${adj.date}</small>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small>${adj.reason}</small>
                                <small class="${adj.quantity > 0 ? 'text-success' : 'text-danger'}">
                                    ${adj.quantity > 0 ? '+' : ''}${adj.quantity}
                                </small>
                            </div>
                        </div>
                    `).join('');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
            });
    }
});
</script>
{% endblock %}
