{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">
        <i class="fas fa-cog"></i> {{ title }}
    </h2>

    <form method="POST">
        {{ form.csrf_token }}
        <div class="row">
            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-cash-register"></i>
                            Paramètres généraux
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            {{ form.default_tax_rate.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.default_tax_rate(class="form-control" + (" is-invalid" if form.default_tax_rate.errors else "")) }}
                                <span class="input-group-text">%</span>
                            </div>
                            {% for error in form.default_tax_rate.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.allow_decimal_quantity(class="form-check-input") }}
                                {{ form.allow_decimal_quantity.label(class="form-check-label") }}
                            </div>
                            {% for error in form.allow_decimal_quantity.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.allow_price_change(class="form-check-input") }}
                                {{ form.allow_price_change.label(class="form-check-label") }}
                            </div>
                            {% for error in form.allow_price_change.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.allow_discount(class="form-check-input") }}
                                {{ form.allow_discount.label(class="form-check-label") }}
                            </div>
                            {% for error in form.allow_discount.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.require_customer_info(class="form-check-input") }}
                                {{ form.require_customer_info.label(class="form-check-label") }}
                            </div>
                            {% for error in form.require_customer_info.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.require_table_selection(class="form-check-input") }}
                                {{ form.require_table_selection.label(class="form-check-label") }}
                            </div>
                            {% for error in form.require_table_selection.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-print"></i>
                            Paramètres d'impression
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.enable_kitchen_print(class="form-check-input") }}
                                {{ form.enable_kitchen_print.label(class="form-check-label") }}
                            </div>
                            {% for error in form.enable_kitchen_print.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.enable_low_stock_alert(class="form-check-input") }}
                                {{ form.enable_low_stock_alert.label(class="form-check-label") }}
                            </div>
                            {% for error in form.enable_low_stock_alert.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-end">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i>
                Enregistrer
            </button>
        </div>
    </form>
</div>
{% endblock %} 