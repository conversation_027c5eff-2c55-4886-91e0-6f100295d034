# Fonctionnalités à développer pour le module Online Ordering Sites.

Remarque: utilise en principe python flask sqlalchemy pour minimiser le code javascript qui doit etre seulement pour ce qui n'est pas possible en python flask sqlalchemy pour une meilleur UX.Et sans utiliser d'API ou API externe puisque tout est dans le meme projet flask.

## 1. Gestion des sous-domaines
- Génération automatique d'un sous-domaine unique pour chaque owner (restaurant ou Business) lors de l'inscription (ex: monresto.monsaas.com) et que je peux le tester en local aussi au moment de developpement et mises à jour
- Redirection automatique vers le site de commande en ligne du restaurant ou Business

## 2. Site de commande en ligne pour chaque restaurant ou Business ou commerce
- Page d'accueil personnalisée (nom, logo, horaires, contact, etc.)
- Affichage dynamique des produits synchronisés depuis le POS (nom, image, description, prix, stock, catégorie)
- Mise à jour automatique des produits, catégories et stocks depuis le POS principal
- Affichage des catégories de produits (navigation par onglets ou menu)
- Recherche de produits

## 3. Parcours de commande client
- Ajout de produits au panier (quantité, options, commentaires)
- Affichage du panier en temps réel
- Authentification/inscription client (table dédiée : `user_customers`)
- Connexion/déconnexion client
- Gestion du profil client (adresse, téléphone, historique commandes)
- Validation de la commande (checkout)
- Choix du mode de retrait :
  - Sur place (Click & Collect)
  - Livraison à domicile

## 4. Livraison
- Saisie et validation de l'adresse de livraison (avec carte interactive Google Maps/OpenStreetMap)
- Calcul automatique de la zone de livraison et frais éventuels
- Sélection du créneau horaire de livraison
- Attribution d'un livreur (nom du livreur affiché au client)
- Suivi de la commande/livraison en temps réel (état : en préparation, en livraison, livré)

## 5. Paiement
- Paiement à la livraison (Cash on Delivery), le livreur peut traiter le paiement directement depuis POS System depuis cette page: http://127.0.0.1:5000/pos/ready_orders avec le meme processus de paiement comme les ordres depuis POS ou bien depuis Actions de cette page: http://127.0.0.1:5000/pos/sales/83
- (Optionnel) Intégration de paiements en ligne (Stripe, PayPal, etc.) à developper plus tard, ajouter pour le moment seulement un bouton de paiement par carte bancaire
- Facture/confirmation envoyée par email

## 6. Envoi de la commande à la cuisine
- Transmission automatique de la commande en ligne au module cuisine du POS (workflow identique à une commande sur place)
- Affichage de la note client (commentaire) pour la cuisine

## 7. Administration restaurateur (owner)
- Tableau de bord des commandes en ligne (en attente, en cours, terminées)
- Historique des commandes en ligne
- Gestion des horaires d'ouverture/fermeture du site de commande
- Configuration des zones de livraison, frais, minimum de commande
- Statistiques de ventes en ligne

## 8. Expérience client
- Email/SMS de confirmation de commande
- Suivi de commande (état, estimation du temps)
- Possibilité de laisser un avis après livraison
- Responsive/mobile first

## 9. Sécurité & séparation des données
- Table `user` (SaaS) : pour les restaurateurs/owners/admins du POS (existe déja dans le modules/auth)
- Table `user_customers` : pour les clients finaux qui commandent sur le site du restaurant
- Authentification, gestion des sessions, RGPD

## 10. Divers
- Multilingue (FR/EN...)
- SEO de base pour chaque sous-domaine
- Gestion des allergènes/options produits
- Impression automatique du ticket de commande en cuisine (si activé)
- Gestion des annulations/modifications de commande

## - Synchronisation temps réel entre POS et site de commande

---

**À prévoir :**

- API RESTful sécurisée pour échanges POS <-> site online
- Documentation technique et guide d'intégration pour chaque restaurant 