{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <a href="{{ url_for('inventory.add_supplier') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouveau fournisseur
            </a>
        </div>
    </div>

    <div class="row">
        {% if suppliers %}
            {% for supplier in suppliers %}
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">{{ supplier.name }}</h5>
                        <p class="card-text">{{ supplier.description }}</p>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <a href="{{ url_for('inventory.edit_supplier', id=supplier.id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i> Modifier
                        </a>
                        <a href="{{ url_for('inventory.delete_supplier', id=supplier.id) }}" 
                           class="btn btn-sm btn-outline-danger"
                           onclick="return confirmDelete('Êtes-vous sûr de vouloir supprimer ce fournisseur ?')">
                            <i class="fas fa-trash"></i> Supprimer
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucun fournisseur n'a été ajouté.
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %} 