from flask import render_template, redirect, url_for, flash, request, jsonify, current_app, send_file
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from app import db
from app.modules.settings.models_settings import Settings
from app.modules.auth.models import User, UserRole
from app.modules.tables.models_table import Table
from app.modules.cash_register.models_cash_register import CashRegister
from app.modules.settings.forms_settings import (
    BusinessSettingsForm,
    ReceiptSettingsForm,
    SaleSettingsForm,
    CashRegisterSettingsForm,
    SystemSettingsForm,
    AlertSettingsForm,
    MaintenanceSettingsForm,
    CashRegisterForm,
    CashRegisterCloseForm,
    PaymentSettingsForm,
    OnlineOrderingSettingsForm
)
from app.utils import get_date_range, format_currency, save_image
from datetime import datetime
import os

from app.modules.tables.forms_table import TableForm

from . import bp

def get_or_create_settings():
    """Get existing settings or create default ones"""
    owner_id = current_user.id
    settings = Settings.query.filter_by(owner_id=owner_id).first()
    if not settings:
        settings = Settings(
            owner_id=owner_id,
            business_name="Mon entreprise",
            currency="EUR",
            timezone="Europe/Paris",
            language="fr",
            date_format="%d/%m/%Y %H:%M",
            receipt_header="Merci de votre visite !",
            receipt_footer="À bientôt !",
            show_logo=True,
            show_tax=True,
            show_order_number=True,
            prices_include_tax=True,
            default_tax_rate=20.0,
            cash_register_name="Nom par défaut",  # Initialisez ici
            cash_register_number="Numéro par défaut",
            require_float=False,
            default_float=0.0,
            require_close=False,
            allow_delete=False,
            allow_void=False,
            require_reason=False
        )
        db.session.add(settings)
        db.session.commit()
    return settings


@bp.route('/business', methods=['GET', 'POST'])
@login_required
def business():
    """Paramètres de l'entreprise"""
    settings = get_or_create_settings()
    form = BusinessSettingsForm(obj=settings)
    
    if form.validate_on_submit():
        form.populate_obj(settings)
        
        if form.logo.data:
            logo_path = save_image(form.logo.data, 'business_logos')
            if logo_path:
                settings.logo_path = logo_path
        
        db.session.commit()
        flash('Paramètres mis à jour avec succès.', 'success')
        return redirect(url_for('settings.business'))
    
    return render_template('settings/business.html',
                         title='Paramètres de l\'entreprise',
                         form=form,
                         settings=settings)

@bp.route('/receipt', methods=['GET', 'POST'])
@login_required
def receipt():
    """Paramètres du ticket de caisse"""
    settings = get_or_create_settings()
    form = ReceiptSettingsForm(obj=settings)
    
    if form.validate_on_submit():
        form.populate_obj(settings)
        
        if form.header_image.data:
            header_path = save_image(form.header_image.data, 'receipt_headers')
            if header_path:
                settings.receipt_header_image = header_path
        
        if form.footer_image.data:
            footer_path = save_image(form.footer_image.data, 'receipt_footers')
            if footer_path:
                settings.receipt_footer_image = footer_path
        
        db.session.commit()
        flash('Paramètres du ticket mis à jour avec succès.', 'success')
        return redirect(url_for('settings.receipt'))
    
    return render_template('settings/receipt.html',
                         title='Paramètres du ticket',
                         form=form,
                         settings=settings)

@bp.route('/receipt/preview')
@login_required
def receipt_preview():
    """Aperçu du ticket de caisse"""
    settings = get_or_create_settings()
    
    # Create sample data for preview
    current_time = datetime.now()
    sample_sale = {
        'number': 'PREVIEW-001',
        'date': current_time,
        'server': 'Jean Dupont',
        'table': '12',
        'items': [
            {'name': 'Produit 1', 'quantity': 2, 'price': 10.00, 'total': 20.00},
            {'name': 'Produit 2', 'quantity': 1, 'price': 15.50, 'total': 15.50},
            {'name': 'Produit 3', 'quantity': 3, 'price': 5.00, 'total': 15.00},
        ],
        'subtotal': 50.50,
        'tax': 10.10,
        'total': 60.60,
        'payment_method': 'Espèces',
        'received': 70.00,
        'change': 9.40
    }
    
    return render_template('settings/receipt_preview.html',
                         settings=settings,
                         sale=sample_sale)

@bp.route('/receipt/print')
@login_required
def receipt_print():
    """Version imprimable du ticket de caisse"""
    settings = get_or_create_settings()
    
    # Use the same sample data as preview
    current_time = datetime.now()
    sample_sale = {
        'number': 'PREVIEW-001',
        'date': current_time,
        'server': 'Jean Dupont',
        'table': '12',
        'items': [
            {'name': 'Produit 1', 'quantity': 2, 'price': 10.00, 'total': 20.00},
            {'name': 'Produit 2', 'quantity': 1, 'price': 15.50, 'total': 15.50},
            {'name': 'Produit 3', 'quantity': 3, 'price': 5.00, 'total': 15.00},
        ],
        'subtotal': 50.50,
        'tax': 10.10,
        'total': 60.60,
        'payment_method': 'Espèces',
        'received': 70.00,
        'change': 9.40
    }
    
    return render_template('settings/receipt_print.html',
                         settings=settings,
                         sale=sample_sale)

@bp.route('/sales', methods=['GET', 'POST'])
@login_required
def sales():
    """Paramètres des ventes"""
    form = SaleSettingsForm()
    if form.validate_on_submit():
        settings = Settings.query.filter_by(owner_id=current_user.id).first()
        if not settings:
            settings = Settings(owner_id=current_user.id)
            db.session.add(settings)
        
        settings.default_tax_rate = form.default_tax_rate.data
        settings.allow_decimal_quantity = form.allow_decimal_quantity.data
        settings.allow_price_change = form.allow_price_change.data
        settings.allow_discount = form.allow_discount.data
        settings.require_customer_info = form.require_customer_info.data
        settings.require_table_selection = form.require_table_selection.data
        settings.enable_kitchen_print = form.enable_kitchen_print.data
        settings.enable_low_stock_alert = form.enable_low_stock_alert.data
        
        db.session.commit()
        flash('Les paramètres ont été enregistrés avec succès.', 'success')
        return redirect(url_for('settings.sales'))
    
    settings = Settings.query.filter_by(owner_id=current_user.id).first()
    if settings:
        form.default_tax_rate.data = settings.default_tax_rate
        form.allow_decimal_quantity.data = settings.allow_decimal_quantity
        form.allow_price_change.data = settings.allow_price_change
        form.allow_discount.data = settings.allow_discount
        form.require_customer_info.data = settings.require_customer_info
        form.require_table_selection.data = settings.require_table_selection
        form.enable_kitchen_print.data = settings.enable_kitchen_print
        form.enable_low_stock_alert.data = settings.enable_low_stock_alert

    return render_template('settings/sales.html',
                         title='Paramètres des ventes',
                         form=form)

@bp.route('/cash-register', methods=['GET', 'POST'])
@login_required
def cash_register():
    """Paramètres de la caisse"""
    form = CashRegisterSettingsForm()
    if form.validate_on_submit():
        settings = Settings.query.filter_by(owner_id=current_user.id).first()
        if not settings:
            settings = Settings(owner_id=current_user.id)
            db.session.add(settings)
        
        settings.cash_register_name = form.name.data
        settings.cash_register_number = form.number.data
        settings.require_float = form.require_float.data
        settings.default_float = form.default_float.data
        settings.require_close = form.require_close.data
        settings.allow_delete = form.allow_delete.data
        settings.allow_void = form.allow_void.data
        settings.require_reason = form.require_reason.data
        
        db.session.commit()
        flash('Les paramètres ont été enregistrés avec succès.', 'success')
        return redirect(url_for('settings.cash_register'))
    
    settings = Settings.query.filter_by(owner_id=current_user.id).first()
    if settings:
        form.name.data = settings.cash_register_name
        form.number.data = settings.cash_register_number
        form.require_float.data = settings.require_float
        form.default_float.data = settings.default_float
        form.require_close.data = settings.require_close
        form.allow_delete.data = settings.allow_delete
        form.allow_void.data = settings.allow_void
        form.require_reason.data = settings.require_reason
    
    return render_template('settings/cash_register.html',
                         title='Paramètres de la caisse',
                         form=form)

@bp.route('/cash-register/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_cash_register(id):
    """Modifier une caisse"""
    register = CashRegister.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    form = CashRegisterForm(obj=register)
    if form.validate_on_submit():
        register.name = form.name.data
        register.number = form.number.data
        register.location = form.location.data
        register.status = form.status.data
        db.session.commit()
        flash('La caisse a été modifiée avec succès.', 'success')
        return redirect(url_for('settings.cash_registers'))
    return render_template('settings/cash_register_form.html',
                         title='Modifier une caisse',
                         form=form,
                         register=register)

@bp.route('/cash-register/<int:id>/delete', methods=['POST'])
@login_required
def delete_cash_register(id):
    """Supprimer une caisse"""
    register = CashRegister.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    db.session.delete(register)
    db.session.commit()
    flash('La caisse a été supprimée avec succès.', 'success')
    return redirect(url_for('settings.cash_registers'))

@bp.route('/cash-register/<int:id>/close', methods=['GET', 'POST'])
@login_required
def close_cash_register(id):
    """Fermer une caisse"""
    register = CashRegister.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    form = CashRegisterCloseForm()
    if form.validate_on_submit():
        register.status = 'closed'
        register.closing_balance = form.closing_balance.data
        register.closing_note = form.closing_note.data
        register.closed_at = datetime.utcnow()
        register.closed_by = current_user.id
        db.session.commit()
        flash('La caisse a été fermée avec succès.', 'success')
        return redirect(url_for('settings.cash_registers'))
    return render_template('settings/cash_register_close.html',
                         title='Fermer une caisse',
                         form=form,
                         register=register)

@bp.route('/payment', methods=['GET', 'POST'])
@login_required
def payment():
    """Paramètres de paiement"""
    form = PaymentSettingsForm()
    if form.validate_on_submit():
        settings = get_or_create_settings()
        
        settings.accepted_payment_methods = form.accepted_payment_methods.data
        settings.default_payment_method = form.default_payment_method.data
        settings.allow_split_payment = form.allow_split_payment.data
        settings.allow_partial_payment = form.allow_partial_payment.data
        settings.allow_refund = form.allow_refund.data
        settings.refund_limit = form.refund_limit.data
        settings.require_approval = form.require_approval.data
        settings.approval_limit = form.approval_limit.data
        settings.require_payment_reference = form.require_payment_reference.data
        settings.round_amounts = form.round_amounts.data
        settings.card_terminal = form.card_terminal.data
        settings.card_minimum = form.card_minimum.data
        settings.card_fee = form.card_fee.data
        settings.tax_number = form.tax_number.data
        settings.tax_enabled = form.tax_enabled.data
        settings.prices_include_tax = form.prices_include_tax.data
        settings.default_tax_rate = form.default_tax_rate.data
        
        db.session.commit()
        flash('Les paramètres ont été enregistrés avec succès.', 'success')
        return redirect(url_for('settings.payment'))
    
    settings = get_or_create_settings()
    if settings:
        form.accepted_payment_methods.data = settings.accepted_payment_methods or []
        form.default_payment_method.data = settings.default_payment_method
        form.allow_split_payment.data = settings.allow_split_payment
        form.allow_partial_payment.data = settings.allow_partial_payment
        form.allow_refund.data = settings.allow_refund
        form.refund_limit.data = settings.refund_limit
        form.require_approval.data = settings.require_approval
        form.approval_limit.data = settings.approval_limit
        form.require_payment_reference.data = settings.require_payment_reference
        form.round_amounts.data = settings.round_amounts
        form.card_terminal.data = settings.card_terminal
        form.card_minimum.data = settings.card_minimum
        form.card_fee.data = settings.card_fee
        form.tax_number.data = settings.tax_number
        form.tax_enabled.data = settings.tax_enabled
        form.prices_include_tax.data = settings.prices_include_tax
        form.default_tax_rate.data = settings.default_tax_rate
    
    return render_template('settings/payment.html',
                         title='Paramètres de paiement',
                         form=form)


"""Depuis içi jusquau bas, ces fonctions n'ont pas de template.html ou liens et ont besoin de corrections"""


@bp.route('/system', methods=['GET', 'POST'])
@login_required
def system():
    """Paramètres système"""
    form = SystemSettingsForm()
    if form.validate_on_submit():
        settings = Settings.query.filter_by(owner_id=current_user.id).first()
        if not settings:
            settings = Settings(owner_id=current_user.id)
            db.session.add(settings)
        
        settings.backup_enabled = form.backup_enabled.data
        settings.backup_frequency = form.backup_frequency.data
        settings.backup_time = form.backup_time.data
        settings.backup_retention = form.backup_retention.data
        settings.log_level = form.log_level.data
        settings.session_timeout = form.session_timeout.data
        settings.password_expiry = form.password_expiry.data
        settings.password_length = form.password_length.data
        settings.password_complexity = form.password_complexity.data
        
        db.session.commit()
        flash('Les paramètres ont été enregistrés avec succès.', 'success')
        return redirect(url_for('settings.system'))
    
    settings = Settings.query.filter_by(owner_id=current_user.id).first()
    if settings:
        form.backup_enabled.data = settings.backup_enabled
        form.backup_frequency.data = settings.backup_frequency
        form.backup_time.data = settings.backup_time
        form.backup_retention.data = settings.backup_retention
        form.log_level.data = settings.log_level
        form.session_timeout.data = settings.session_timeout
        form.password_expiry.data = settings.password_expiry
        form.password_length.data = settings.password_length
        form.password_complexity.data = settings.password_complexity
    
    return render_template('settings/system.html',
                         title='Paramètres système',
                         form=form)

@bp.route('/alerts', methods=['GET', 'POST'])
@login_required
def alerts():
    """Paramètres des alertes"""
    form = AlertSettingsForm()
    if form.validate_on_submit():
        settings = Settings.query.filter_by(owner_id=current_user.id).first()
        if not settings:
            settings = Settings(owner_id=current_user.id)
            db.session.add(settings)
        
        settings.low_stock_alert = form.low_stock_alert.data
        settings.out_of_stock_alert = form.out_of_stock_alert.data
        settings.expiry_alert = form.expiry_alert.data
        settings.expiry_days = form.expiry_days.data
        settings.sales_alert = form.sales_alert.data
        settings.sales_target = form.sales_target.data
        settings.expense_alert = form.expense_alert.data
        settings.expense_limit = form.expense_limit.data
        settings.cash_alert = form.cash_alert.data
        settings.cash_limit = form.cash_limit.data
        
        db.session.commit()
        flash('Les paramètres ont été enregistrés avec succès.', 'success')
        return redirect(url_for('settings.alerts'))
    
    settings = Settings.query.filter_by(owner_id=current_user.id).first()
    if settings:
        form.low_stock_alert.data = settings.low_stock_alert
        form.out_of_stock_alert.data = settings.out_of_stock_alert
        form.expiry_alert.data = settings.expiry_alert
        form.expiry_days.data = settings.expiry_days
        form.sales_alert.data = settings.sales_alert
        form.sales_target.data = settings.sales_target
        form.expense_alert.data = settings.expense_alert
        form.expense_limit.data = settings.expense_limit
        form.cash_alert.data = settings.cash_alert
        form.cash_limit.data = settings.cash_limit
    
    return render_template('settings/alerts.html',
                         title='Paramètres des alertes',
                         form=form)

@bp.route('/online_ordering', methods=['GET', 'POST'])
@login_required
def online_ordering():
    """Paramètres de commande en ligne"""
    from app.modules.online_ordering_sites.models import OnlineOrderingSite

    form = OnlineOrderingSettingsForm()

    if form.validate_on_submit():
        settings = get_or_create_settings()

        # Mettre à jour les settings
        settings.online_ordering_enabled = form.online_ordering_enabled.data
        settings.online_ordering_subdomain = form.online_ordering_subdomain.data
        settings.online_ordering_site_name = form.online_ordering_site_name.data
        settings.online_ordering_description = form.online_ordering_description.data
        settings.online_ordering_primary_color = form.online_ordering_primary_color.data
        settings.online_ordering_secondary_color = form.online_ordering_secondary_color.data
        settings.delivery_enabled = form.delivery_enabled.data
        settings.pickup_enabled = form.pickup_enabled.data
        settings.dine_in_enabled = form.dine_in_enabled.data
        settings.drive_through_enabled = form.drive_through_enabled.data
        settings.delivery_fee = form.delivery_fee.data
        settings.minimum_order_amount = form.minimum_order_amount.data
        settings.delivery_radius = form.delivery_radius.data
        settings.notify_kitchen_online_orders = form.notify_kitchen_online_orders.data
        settings.notify_customer_order_confirmed = form.notify_customer_order_confirmed.data
        settings.notify_customer_order_ready = form.notify_customer_order_ready.data
        settings.notify_customer_out_for_delivery = form.notify_customer_out_for_delivery.data
        settings.auto_assign_deliverer = form.auto_assign_deliverer.data

        # Créer ou mettre à jour le site de commande en ligne
        site = OnlineOrderingSite.query.filter_by(owner_id=current_user.id).first()

        if not site and form.online_ordering_subdomain.data:
            # Créer un nouveau site
            site = OnlineOrderingSite(
                owner_id=current_user.id,
                subdomain=form.online_ordering_subdomain.data,
                site_name=form.online_ordering_site_name.data,
                site_description=form.online_ordering_description.data,
                primary_color=form.online_ordering_primary_color.data,
                secondary_color=form.online_ordering_secondary_color.data,
                is_active=form.online_ordering_enabled.data,
                allow_delivery=form.delivery_enabled.data,
                allow_pickup=form.pickup_enabled.data,
                allow_dine_in=form.dine_in_enabled.data,
                allow_drive_through=form.drive_through_enabled.data,
                delivery_fee=form.delivery_fee.data,
                minimum_order=form.minimum_order_amount.data,
                delivery_radius=form.delivery_radius.data
            )
            db.session.add(site)
        elif site:
            # Mettre à jour le site existant
            site.subdomain = form.online_ordering_subdomain.data
            site.site_name = form.online_ordering_site_name.data
            site.site_description = form.online_ordering_description.data
            site.primary_color = form.online_ordering_primary_color.data
            site.secondary_color = form.online_ordering_secondary_color.data
            site.is_active = form.online_ordering_enabled.data
            site.allow_delivery = form.delivery_enabled.data
            site.allow_pickup = form.pickup_enabled.data
            site.allow_dine_in = form.dine_in_enabled.data
            site.allow_drive_through = form.drive_through_enabled.data
            site.delivery_fee = form.delivery_fee.data
            site.minimum_order = form.minimum_order_amount.data
            site.delivery_radius = form.delivery_radius.data

        db.session.commit()
        flash('Les paramètres de commande en ligne ont été enregistrés avec succès.', 'success')

        if form.online_ordering_enabled.data and form.online_ordering_subdomain.data:
            flash(f'Votre site de commande est accessible à l\'adresse: http://{form.online_ordering_subdomain.data}.lvh.me:5000', 'info')

        return redirect(url_for('settings.online_ordering'))

    # Charger les données existantes
    settings = get_or_create_settings()
    site = OnlineOrderingSite.query.filter_by(owner_id=current_user.id).first()

    if settings:
        form.online_ordering_enabled.data = settings.online_ordering_enabled
        form.online_ordering_subdomain.data = settings.online_ordering_subdomain or (site.subdomain if site else current_user.username.lower().replace(' ', '_'))
        form.online_ordering_site_name.data = settings.online_ordering_site_name or (site.site_name if site else f"Restaurant {current_user.username}")
        form.online_ordering_description.data = settings.online_ordering_description or (site.site_description if site else f"Commandez en ligne chez {current_user.username}")
        form.online_ordering_primary_color.data = settings.online_ordering_primary_color or (site.primary_color if site else '#4e73df')
        form.online_ordering_secondary_color.data = settings.online_ordering_secondary_color or (site.secondary_color if site else '#858796')
        form.delivery_enabled.data = settings.delivery_enabled if settings.delivery_enabled is not None else True
        form.pickup_enabled.data = settings.pickup_enabled if settings.pickup_enabled is not None else True
        form.dine_in_enabled.data = settings.dine_in_enabled if settings.dine_in_enabled is not None else True
        form.drive_through_enabled.data = settings.drive_through_enabled if settings.drive_through_enabled is not None else False
        form.delivery_fee.data = settings.delivery_fee if settings.delivery_fee is not None else 2.50
        form.minimum_order_amount.data = settings.minimum_order_amount if settings.minimum_order_amount is not None else 15.00
        form.delivery_radius.data = settings.delivery_radius if settings.delivery_radius is not None else 10.0
        form.notify_kitchen_online_orders.data = settings.notify_kitchen_online_orders if settings.notify_kitchen_online_orders is not None else True
        form.notify_customer_order_confirmed.data = settings.notify_customer_order_confirmed if settings.notify_customer_order_confirmed is not None else True
        form.notify_customer_order_ready.data = settings.notify_customer_order_ready if settings.notify_customer_order_ready is not None else True
        form.notify_customer_out_for_delivery.data = settings.notify_customer_out_for_delivery if settings.notify_customer_out_for_delivery is not None else True
        form.auto_assign_deliverer.data = settings.auto_assign_deliverer if settings.auto_assign_deliverer is not None else False

    return render_template('settings/online_ordering.html',
                         title='Commande en ligne',
                         form=form,
                         site=site)

