from flask_sqlalchemy import SQLAlchemy
from flask_migrate import <PERSON><PERSON><PERSON>
from flask_login import <PERSON>ginManager
from flask_wtf.csrf import CSRFProtect
from flask_redis import FlaskRedis
from flask_caching import Cache
from flask_socketio import SocketIO

db = SQLAlchemy()
migrate = Migrate()
login = LoginManager()
csrf = CSRFProtect()
redis_client = FlaskRedis()
cache = Cache()
socketio = SocketIO()

def init_extensions(app):
    db.init_app(app)
    login.init_app(app)
    migrate.init_app(app, db)
    cache.init_app(app, config={
        'CACHE_TYPE': 'simple',  # Pour le développement
        'CACHE_DEFAULT_TIMEOUT': 300  # 5 minutes
    })

    # Configuration du login manager
    login.login_view = 'auth.login'
    login.login_message = 'Veuillez vous connecter pour accéder à cette page.'
    login.login_message_category = 'info' 