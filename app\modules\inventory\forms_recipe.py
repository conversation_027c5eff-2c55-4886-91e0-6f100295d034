from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, FloatField, IntegerField, SelectField, TextAreaField, SubmitField
from wtforms.validators import DataRequired, Optional, NumberRange

class RecipeForm(FlaskForm):
    description = TextAreaField('Instructions de préparation', 
                              validators=[DataRequired(message="Les instructions sont requises")])
    preparation_time = IntegerField('Temps de préparation (minutes)', 
                                  validators=[DataRequired(message="Le temps de préparation est requis"),
                                            NumberRange(min=1, message="Le temps doit être positif")])
    submit = SubmitField('Enregistrer')

class RecipeItemForm(FlaskForm):
    ingredient_id = SelectField('Ingrédient', 
                              coerce=int,
                              validators=[DataRequired(message="Veuillez sélectionner un ingrédient")])
    quantity = FloatField('Quantité', 
                         validators=[DataRequired(message="La quantité est requise"),
                                   NumberRange(min=0.01, message="La quantité doit être positive")])
    submit = SubmitField('Ajouter') 