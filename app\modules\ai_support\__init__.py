from flask import Blueprint

bp = Blueprint('ai_support', __name__, 
               template_folder='templates',
               static_folder='static',
               static_url_path='/ai_support/static')

from . import routes

def getConfidenceClass(confidence):
    if confidence is None:
        return ""
    if confidence >= 0.85:
        return "high"
    elif confidence >= 0.6:
        return "medium"
    else:
        return "low"

from flask import current_app

def init_app(app):
    @app.context_processor
    def inject_confidence_class():
        return dict(getConfidenceClass=getConfidenceClass)
