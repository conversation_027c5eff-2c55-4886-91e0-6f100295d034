{% extends "base.html" %}

{% block title %}{{ account.name }} - Détails du Compte{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-2">
                <h4 class="mb-0">
                    <i class="fas fa-university"></i> {{ account.name }}
                    {% if account.is_default %}
                        <span class="badge bg-primary">Défaut</span>
                    {% endif %}
                </h4>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.edit_bank_account', account_id=account.id) }}" 
                       class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-edit"></i> Modifier
                    </a>
                    <a href="{{ url_for('inventory.bank_management_index') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- Informations du compte -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-info-circle"></i> Informations du Compte</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Solde actuel:</strong><br>
                            <span class="h4 text-{{ 'success' if account.balance >= 0 else 'danger' }}">
                                {{ "%.2f"|format(account.balance) }} {{ account.currency }}
                            </span>
                        </div>

                        {% if account.is_overdrawn %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Découvert:</strong> {{ "%.2f"|format(account.overdraft_amount) }} €
                        </div>
                        {% endif %}

                        <div class="mb-2">
                            <strong>Solde disponible:</strong><br>
                            <span class="text-info">{{ "%.2f"|format(account.available_balance) }} {{ account.currency }}</span>
                        </div>

                        {% if account.overdraft_limit > 0 %}
                        <div class="mb-2">
                            <strong>Découvert autorisé:</strong><br>
                            <span class="text-muted">{{ "%.2f"|format(account.overdraft_limit) }} {{ account.currency }}</span>
                        </div>
                        {% endif %}

                        <hr>

                        {% if account.account_number %}
                        <div class="mb-2">
                            <strong>Numéro de compte:</strong><br>
                            <code>{{ account.account_number }}</code>
                        </div>
                        {% endif %}

                        {% if account.iban %}
                        <div class="mb-2">
                            <strong>IBAN:</strong><br>
                            <code>{{ account.iban }}</code>
                        </div>
                        {% endif %}

                        {% if account.bic %}
                        <div class="mb-2">
                            <strong>BIC:</strong><br>
                            <code>{{ account.bic }}</code>
                        </div>
                        {% endif %}

                        {% if account.bank_name %}
                        <div class="mb-2">
                            <strong>Banque:</strong><br>
                            {{ account.bank_name }}
                        </div>
                        {% endif %}

                        {% if account.description %}
                        <div class="mb-2">
                            <strong>Description:</strong><br>
                            <small class="text-muted">{{ account.description }}</small>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Actions rapides -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-bolt"></i> Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('inventory.add_bank_operation') }}?account_id={{ account.id }}" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-plus"></i> Nouvelle opération
                            </a>
                            <a href="{{ url_for('inventory.bank_transfer') }}?source={{ account.id }}" 
                               class="btn btn-outline-success btn-sm">
                                <i class="fas fa-exchange-alt"></i> Virement
                            </a>
                            <a href="{{ url_for('inventory.cash_to_bank_deposit') }}?account_id={{ account.id }}" 
                               class="btn btn-outline-info btn-sm">
                                <i class="fas fa-money-bill-wave"></i> Dépôt caisse
                            </a>
                            <a href="{{ url_for('inventory.bank_reconciliation') }}?account_id={{ account.id }}" 
                               class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-balance-scale"></i> Réconciliation
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Historique des opérations -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6><i class="fas fa-history"></i> Historique des Opérations</h6>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-secondary dropdown-toggle" 
                                        type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-filter"></i> Filtrer
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="?filter=all">Toutes</a></li>
                                    <li><a class="dropdown-item" href="?filter=credit">Crédits</a></li>
                                    <li><a class="dropdown-item" href="?filter=debit">Débits</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="?filter=deposit">Dépôts</a></li>
                                    <li><a class="dropdown-item" href="?filter=withdrawal">Retraits</a></li>
                                    <li><a class="dropdown-item" href="?filter=transfer">Virements</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if operations.items %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Type</th>
                                            <th>Montant</th>
                                            <th>Description</th>
                                            <th>Référence</th>
                                            <th>Solde après</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for operation in operations.items %}
                                        <tr>
                                            <td>
                                                <small>{{ operation.operation_date.strftime('%d/%m/%Y') }}</small><br>
                                                <small class="text-muted">{{ operation.operation_date.strftime('%H:%M') }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if operation.is_credit else 'danger' }}">
                                                    {{ operation.type_display }}
                                                </span>
                                            </td>
                                            <td class="text-{{ 'success' if operation.is_credit else 'danger' }}">
                                                {{ "+" if operation.is_credit else "-" }}{{ "%.2f"|format(operation.absolute_amount) }} €
                                            </td>
                                            <td>
                                                {{ operation.description[:40] }}
                                                {% if operation.description|length > 40 %}...{% endif %}
                                                {% if operation.notes %}
                                                    <br><small class="text-muted">{{ operation.notes[:30] }}{% if operation.notes|length > 30 %}...{% endif %}</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if operation.reference %}
                                                    <code>{{ operation.reference }}</code>
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <strong>{{ "%.2f"|format(operation.balance_after) }} €</strong>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            {% if operations.pages > 1 %}
                            <nav aria-label="Navigation des opérations">
                                <ul class="pagination pagination-sm justify-content-center">
                                    {% if operations.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('inventory.bank_account_details', account_id=account.id, page=operations.prev_num) }}">
                                            Précédent
                                        </a>
                                    </li>
                                    {% endif %}

                                    {% for page in range(1, operations.pages + 1) %}
                                        {% if page <= 3 or page > operations.pages - 3 or (page >= operations.page - 1 and page <= operations.page + 1) %}
                                        <li class="page-item {{ 'active' if page == operations.page else '' }}">
                                            <a class="page-link" href="{{ url_for('inventory.bank_account_details', account_id=account.id, page=page) }}">
                                                {{ page }}
                                            </a>
                                        </li>
                                        {% elif page == 4 and operations.page > 5 %}
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                        {% elif page == operations.pages - 3 and operations.page < operations.pages - 4 %}
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if operations.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('inventory.bank_account_details', account_id=account.id, page=operations.next_num) }}">
                                            Suivant
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                            {% endif %}
                        {% else %}
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-history fa-2x mb-2"></i>
                                <h6>Aucune opération</h6>
                                <p>Aucune opération n'a encore été enregistrée sur ce compte</p>
                                <a href="{{ url_for('inventory.add_bank_operation') }}?account_id={{ account.id }}" 
                                   class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> Première opération
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Actualisation automatique du solde
function refreshBalance() {
    fetch(`/inventory/bank-management/api/account-balance/{{ account.id }}`)
        .then(response => response.json())
        .then(data => {
            // Mettre à jour l'affichage du solde si nécessaire
            console.log('Balance updated:', data);
        })
        .catch(error => console.error('Error refreshing balance:', error));
}

// Actualiser toutes les 30 secondes
setInterval(refreshBalance, 30000);
</script>
{% endblock %}
