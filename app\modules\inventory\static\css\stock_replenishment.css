/* Styles spécifiques pour le système d'approvisionnement de stock */

/* Variables CSS pour la cohérence des couleurs */
:root {
    --replenishment-primary: #28a745;
    --replenishment-secondary: #6c757d;
    --replenishment-success: #20c997;
    --replenishment-warning: #ffc107;
    --replenishment-danger: #dc3545;
    --replenishment-info: #17a2b8;
    --replenishment-bg: #f8f9fa;
    --replenishment-border: #dee2e6;
    --replenishment-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Container principal avec background différencié */
.stock-replenishment-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    padding-bottom: 2rem;
}

/* En-tête de l'approvisionnement */
.replenishment-header {
    background: linear-gradient(135deg, var(--replenishment-primary) 0%, var(--replenishment-success) 100%);
    color: white;
    box-shadow: var(--replenishment-shadow);
    margin-bottom: 1rem;
}

.replenishment-header h2 {
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.header-actions .btn {
    border-radius: 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.header-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Cartes de statistiques */
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--replenishment-shadow);
    border: none;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(20px);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: currentColor;
}

.stat-card.bg-warning::before { background: var(--replenishment-warning); }
.stat-card.bg-danger::before { background: var(--replenishment-danger); }
.stat-card.bg-info::before { background: var(--replenishment-info); }
.stat-card.bg-success::before { background: var(--replenishment-success); }

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
    margin-bottom: 1rem;
}

.stat-card .stat-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.stat-card .stat-content p {
    color: var(--replenishment-secondary);
    margin-bottom: 0;
    font-weight: 500;
}

/* Carte de sélection de mode */
.mode-selector-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: none;
    overflow: hidden;
}

.mode-selector-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 2rem;
}

.mode-selector-card .card-header h4 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.mode-selector-card .card-body {
    padding: 2rem;
}

/* Options de mode */
.mode-option {
    background: #f8f9fa;
    border: 2px solid var(--replenishment-border);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.mode-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.mode-option:hover::before {
    left: 100%;
}

.mode-option:hover {
    border-color: var(--replenishment-primary);
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(40, 167, 69, 0.15);
}

.mode-option.selected {
    border-color: var(--replenishment-primary);
    background: rgba(40, 167, 69, 0.05);
}

.mode-option .mode-icon {
    font-size: 3rem;
    color: var(--replenishment-primary);
    margin-bottom: 1rem;
}

.mode-option h5 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1rem;
}

.mode-option p {
    color: var(--replenishment-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.mode-features {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
    text-align: left;
}

.mode-features li {
    padding: 0.5rem 0;
    color: #495057;
}

.mode-features i {
    margin-right: 0.5rem;
    width: 16px;
}

.mode-action .btn {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.mode-action .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Cartes d'actions rapides */
.quick-action-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: var(--replenishment-shadow);
    transition: all 0.3s ease;
    border: 1px solid var(--replenishment-border);
}

.quick-action-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.quick-action-card h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1rem;
}

.quick-action-card h6 i {
    color: var(--replenishment-primary);
    margin-right: 0.5rem;
}

/* Mode POS spécifique */
.pos-replenishment-container {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    min-height: 100vh;
}

.pos-replenishment-header {
    background: linear-gradient(135deg, var(--replenishment-success) 0%, var(--replenishment-primary) 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Grille d'articles pour le mode POS */
.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    padding: 1rem;
    max-height: 60vh;
    overflow-y: auto;
}

.item-card {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    box-shadow: var(--replenishment-shadow);
}

.item-card:hover {
    border-color: var(--replenishment-primary);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.15);
}

.item-card img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.item-card .item-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.item-card .item-price {
    color: var(--replenishment-primary);
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.item-card .item-stock {
    color: var(--replenishment-secondary);
    font-size: 0.8rem;
}

/* Ticket d'achat */
.purchase-ticket {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    height: fit-content;
    max-height: 80vh;
    overflow-y: auto;
}

.ticket-header {
    background: var(--replenishment-primary);
    color: white;
    padding: 1rem;
    border-radius: 10px 10px 0 0;
}

.ticket-items {
    padding: 1rem;
    max-height: 40vh;
    overflow-y: auto;
}

.ticket-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--replenishment-border);
}

.ticket-total {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0 0 10px 10px;
    border-top: 2px solid var(--replenishment-primary);
}

/* Numpad pour le mode POS */
.numpad-container {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    box-shadow: var(--replenishment-shadow);
}

.numpad-display {
    background: #2c3e50;
    color: white;
    padding: 1rem;
    text-align: right;
    font-size: 1.5rem;
    font-weight: 600;
    border-radius: 8px;
    margin-bottom: 1rem;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.numpad-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
}

.numpad-btn {
    background: #f8f9fa;
    border: 2px solid var(--replenishment-border);
    border-radius: 8px;
    padding: 1rem;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.numpad-btn:hover {
    background: var(--replenishment-primary);
    color: white;
    border-color: var(--replenishment-primary);
}

.numpad-btn.special {
    background: var(--replenishment-secondary);
    color: white;
}

.numpad-btn.special:hover {
    background: #495057;
}

/* Responsive design */
@media (max-width: 768px) {
    .mode-selector-card .card-body {
        padding: 1rem;
    }
    
    .mode-option {
        margin-bottom: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .items-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.5rem;
    }
    
    .numpad-btn {
        padding: 0.75rem;
        font-size: 1rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* États de chargement */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--replenishment-primary);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
