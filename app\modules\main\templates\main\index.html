{% extends "base.html" %}

{% block content %}
<div class="container">
    <!-- Hero Section -->
    <div class="row align-items-center py-5">
        <div class="col-lg-6">
            <h1 class="display-4 fw-bold">Gérez votre commerce en toute simplicité</h1>
            <p class="lead text-muted">
                Une solution complète de point de vente pour gérer vos ventes, votre inventaire et vos rapports en un seul endroit.
            </p>
            <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-lg px-4 me-md-2">
                    <i class="fas fa-user-plus"></i> Commencer gratuitement
                </a>
                <a href="{{ url_for('auth.login') }}" class="btn btn-outline-secondary btn-lg px-4">
                    <i class="fas fa-sign-in-alt"></i> Se connecter
                </a>
                <a href="#" class="btn btn-outline-secondary btn-lg px-4">
                    <i class="fas fa-user-secret"></i> Démo
                </a>
                <a href="{{ url_for('main.faq') }}" class="btn btn-outline-secondary btn-lg px-4">
                    <i class="fas fa-question-circle"></i> FAQ
                </a>
            </div>
        </div>
        <div class="col-lg-6">
            <img src="{{ url_for('static', filename='img/hero-image.svg') }}" class="img-fluid" alt="POS System">
        </div>
    </div>

    <!-- Features Section -->
    <div class="row py-5">
        <div class="col-12 text-center mb-5">
            <h2 class="fw-bold">Fonctionnalités principales</h2>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-cash-register fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">Point de vente</h5>
                    <p class="card-text">
                        Interface intuitive pour gérer rapidement vos ventes et encaissements.
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-boxes fa-3x text-success mb-3"></i>
                    <h5 class="card-title">Gestion des stocks</h5>
                    <p class="card-text">
                        Suivez votre inventaire en temps réel et gérez vos approvisionnements.
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-chart-bar fa-3x text-info mb-3"></i>
                    <h5 class="card-title">Rapports détaillés</h5>
                    <p class="card-text">
                        Analysez vos performances avec des rapports clairs et détaillés.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Call to Action -->
    <div class="row py-5">
        <div class="col-12 text-center">
            <div class="p-5 bg-light rounded-3">
                <h2 class="fw-bold mb-3">Prêt à commencer ?</h2>
                <p class="lead mb-4">
                    Rejoignez les milliers de commerces qui font confiance à notre solution.
                </p>
                <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-rocket"></i> Créer mon compte
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.hero-image {
    max-width: 100%;
    height: auto;
}
.card {
    transition: transform 0.2s;
}
.card:hover {
    transform: translateY(-5px);
}
</style>
{% endblock %} 