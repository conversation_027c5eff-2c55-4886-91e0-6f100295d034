{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <a href="{{ url_for('inventory.add_ingredient_category') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouvelle catégorie
            </a>
        </div>
    </div>

    <div class="row">
        {% for category in categories %}
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                {% if category.image_path %}
                <img src="{{ url_for('static', filename=category.image_path) }}" class="card-img-top" alt="{{ category.name }}">
                {% endif %}
                <div class="card-body">
                    <h5 class="card-title">{{ category.name }}</h5>
                    {% if category.description %}
                    <p class="card-text">{{ category.description }}</p>
                    {% endif %}
                    <p class="card-text">
                        <small class="text-muted">
                            {{ category.ingredients.count() }} ingrédient(s)
                        </small>
                    </p>
                </div>
                <div class="card-footer bg-transparent border-0">
                    <a href="{{ url_for('inventory.edit_ingredient_category', id=category.id) }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-edit"></i> Modifier
                    </a>
                    <a href="{{ url_for('inventory.delete_ingredient_category', id=category.id) }}" 
                       class="btn btn-sm btn-outline-danger"
                       onclick="return confirmDelete('Êtes-vous sûr de vouloir supprimer cette catégorie ?')">
                        <i class="fas fa-trash"></i> Supprimer
                    </a>
                </div>
            </div>
        </div>
        {% else %}
        <div class="col">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Aucune catégorie d'ingrédients n'a été créée.
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %} 