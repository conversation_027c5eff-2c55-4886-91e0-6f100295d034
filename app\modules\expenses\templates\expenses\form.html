{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-file-invoice-dollar"></i>
            {{ title }}
        </h1>
        <a href="{{ url_for('expenses.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i>
            Retour à la liste
        </a>
    </div>

    <!-- Form Card -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                {{ form.csrf_token }}

                <div class="row">
                    <!-- Informations principales -->
                    <div class="col-md-8">
                        <h5 class="mb-4">Informations principales</h5>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.category_id.label(class="form-label") }}
                                {{ form.category_id(class="form-select" + (" is-invalid" if form.category_id.errors else "")) }}
                                {% for error in form.category_id.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="col-md-6">
                                {{ form.date.label(class="form-label") }}
                                {{ form.date(class="form-control" + (" is-invalid" if form.date.errors else ""), type="date") }}
                                {% for error in form.date.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.amount.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.amount(class="form-control" + (" is-invalid" if form.amount.errors else ""), type="number", step="0.01") }}
                                    <span class="input-group-text">€</span>
                                </div>
                                {% for error in form.amount.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="col-md-6">
                                {{ form.payment_method.label(class="form-label") }}
                                {{ form.payment_method(class="form-select" + (" is-invalid" if form.payment_method.errors else "")) }}
                                {% for error in form.payment_method.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows=3) }}
                            {% for error in form.description.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.reference.label(class="form-label") }}
                            {{ form.reference(class="form-control" + (" is-invalid" if form.reference.errors else "")) }}
                            {% for error in form.reference.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                            <div class="form-text">Numéro de facture, référence du paiement, etc.</div>
                        </div>
                    </div>

                    <!-- Options supplémentaires -->
                    <div class="col-md-4">
                        <h5 class="mb-4">Options supplémentaires</h5>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.is_recurring(class="form-check-input") }}
                                {{ form.is_recurring.label(class="form-check-label") }}
                            </div>
                        </div>

                        <div id="recurringOptions" class="mb-3" style="display: none;">
                            <div class="mb-3">
                                {{ form.recurring_interval.label(class="form-label") }}
                                {{ form.recurring_interval(class="form-select" + (" is-invalid" if form.recurring_interval.errors else "")) }}
                                {% for error in form.recurring_interval.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="mb-3">
                                {{ form.recurring_day.label(class="form-label") }}
                                {{ form.recurring_day(class="form-control" + (" is-invalid" if form.recurring_day.errors else ""), type="number", min="1", max="31") }}
                                {% for error in form.recurring_day.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                                <div class="form-text">Jour du mois où la dépense sera répétée</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.image.label(class="form-label") }}
                            {{ form.image(class="form-control" + (" is-invalid" if form.image.errors else "")) }}
                            {% for error in form.image.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                            <div class="form-text">Formats acceptés: JPG, PNG, JPEG, PDF</div>
                        </div>

                        {% if expense and expense.image_path %}
                        <div class="mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Justificatif actuel</h6>
                                    {% if expense.image_path.endswith('.pdf') %}
                                    <p class="mb-2">
                                        <i class="fas fa-file-pdf text-danger"></i>
                                        Document PDF
                                    </p>
                                    {% else %}
                                    <img src="{{ url_for('static', filename=expense.image_path) }}" 
                                         class="img-fluid mb-2" 
                                         alt="Justificatif">
                                    {% endif %}
                                    <div class="text-end">
                                        <a href="{{ url_for('static', filename=expense.image_path) }}" 
                                           class="btn btn-sm btn-primary"
                                           target="_blank">
                                            <i class="fas fa-external-link-alt"></i>
                                            Voir
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <hr>

                <div class="text-end">
                    <a href="{{ url_for('expenses.index') }}" class="btn btn-secondary">Annuler</a>
                    {{ form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const isRecurringCheckbox = document.getElementById('is_recurring');
    const recurringOptions = document.getElementById('recurringOptions');

    function toggleRecurringOptions() {
        recurringOptions.style.display = isRecurringCheckbox.checked ? 'block' : 'none';
    }

    isRecurringCheckbox.addEventListener('change', toggleRecurringOptions);
    toggleRecurringOptions();
});
</script>
{% endblock %} 