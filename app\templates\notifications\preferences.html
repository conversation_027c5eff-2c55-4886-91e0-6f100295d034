{% extends "base.html" %}

{% block title %}Préférences de Notifications{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-bell"></i> Préférences de Notifications</h4>
                </div>
                <div class="card-body">
                    <form id="preferencesForm">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Méthodes de notification</h5>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="email_enabled" name="email_enabled" 
                                           {% if preferences.email_enabled %}checked{% endif %}>
                                    <label class="form-check-label" for="email_enabled">
                                        <i class="fas fa-envelope"></i> Notifications par email
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="browser_enabled" name="browser_enabled" 
                                           {% if preferences.browser_enabled %}checked{% endif %}>
                                    <label class="form-check-label" for="browser_enabled">
                                        <i class="fas fa-desktop"></i> Notifications navigateur
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="sound_enabled" name="sound_enabled" 
                                           {% if preferences.sound_enabled %}checked{% endif %}>
                                    <label class="form-check-label" for="sound_enabled">
                                        <i class="fas fa-volume-up"></i> Sons de notification
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5>Types de notifications</h5>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="order_updates" name="order_updates" 
                                           {% if preferences.order_updates %}checked{% endif %}>
                                    <label class="form-check-label" for="order_updates">
                                        <i class="fas fa-shopping-cart"></i> Mises à jour des commandes
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="payment_updates" name="payment_updates" 
                                           {% if preferences.payment_updates %}checked{% endif %}>
                                    <label class="form-check-label" for="payment_updates">
                                        <i class="fas fa-credit-card"></i> Mises à jour des paiements
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="promotional" name="promotional" 
                                           {% if preferences.promotional %}checked{% endif %}>
                                    <label class="form-check-label" for="promotional">
                                        <i class="fas fa-tag"></i> Offres promotionnelles
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" onclick="history.back()">
                                <i class="fas fa-arrow-left"></i> Retour
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Zone de notifications -->
<div id="notification-area" class="position-fixed" style="top: 20px; right: 20px; z-index: 1050;"></div>

<script>
document.getElementById('preferencesForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('{{ url_for("notifications.update_notification_preferences") }}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Préférences mises à jour avec succès', 'success');
        } else {
            showNotification('Erreur: ' + data.error, 'error');
        }
    })
    .catch(error => {
        showNotification('Erreur de connexion', 'error');
    });
});

function showNotification(message, type = 'info') {
    const notificationArea = document.getElementById('notification-area');
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show`;
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    notificationArea.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
