# SYSTÈME D'APPROVISIONNEMENT DE STOCK - DOCUMENTATION COMPLÈTE

## 📋 APERÇU GÉNÉRAL

Ce document décrit le système complet d'approvisionnement de stock permettant aux utilisateurs d'ajouter des quantités d'ingrédients et de produits sans recettes via deux modes d'interface : **Mode Formulaire** et **Mode POS**.

## 🎯 OBJECTIFS PRINCIPAUX

1. **Faciliter l'approvisionnement rapide** des stocks via une interface similaire au POS
2. **Gérer les achats auprès des fournisseurs** avec traçabilité complète
3. **Intégrer les paiements** avec le système de caisse et gestion bancaire existant
4. **Maintenir la cohérence des stocks** en temps réel
5. **Offrir flexibilité** entre saisie rapide (Mode POS) et saisie détaillée (Mode Formulaire)

## 🏗️ ARCHITECTURE DU SYSTÈME

### 📁 Structure des Fichiers
```
app/modules/inventory/
├── models/
│   ├── models_purchase_order.py      # Nouveau - Commandes d'achat
│   ├── models_supplier_invoice.py    # Nouveau - Factures fournisseurs
│   └── models_bank_account.py        # Nouveau - Gestion bancaire
├── routes/
│   ├── routes_stock_replenishment.py # Nouveau - Routes approvisionnement
│   └── routes_bank_management.py     # Nouveau - Routes gestion bancaire
├── templates/
│   ├── stock_replenishment/
│   │   ├── index.html                # Page principale avec sélecteur de mode
│   │   ├── form_mode.html            # Mode formulaire
│   │   ├── pos_mode.html             # Mode POS
│   │   └── payment_modal.html        # Modale de paiement
│   └── bank_management/
│       └── index.html                # Gestion compte bancaire
├── static/
│   ├── css/
│   │   └── stock_replenishment.css   # Styles spécifiques
│   └── js/
│       └── stock_replenishment.js    # Logique JavaScript
└── forms/
    ├── forms_stock_replenishment.py  # Formulaires approvisionnement
    └── forms_bank_management.py      # Formulaires gestion bancaire
```

## 🔧 FONCTIONNALITÉS DÉTAILLÉES

### 1. PAGE PRINCIPALE D'APPROVISIONNEMENT

**Route:** `/inventory/stock-replenishment`

#### Composants de l'interface :
- **Sélecteur de mode** : Basculer entre Mode Formulaire (défaut) et Mode POS
- **Background différencié** : Couleur distincte du POS pour éviter la confusion
- **Navigation cohérente** avec le système existant

### 2. MODE FORMULAIRE

#### Caractéristiques :
- **Interface traditionnelle** avec champs de saisie
- **Validation complète** des données
- **Saisie détaillée** pour chaque article
- **Gestion des lots** et dates d'expiration

#### Champs du formulaire :
- Sélection fournisseur (optionnel)
- Catégorie de fournisseur (optionnel)
- Article (produit sans recette ou ingrédient)
- Quantité
- Prix unitaire
- Date de livraison
- Notes

### 3. MODE POS POUR APPROVISIONNEMENT

#### Interface similaire au POS existant :

##### A. Barre de recherche
- **Recherche en temps réel** des produits sans recettes et ingrédients
- **Filtrage par nom** et code-barres

##### B. Sélecteurs de fournisseurs
- **Catégories de fournisseurs** (optionnel) - Basé sur `/inventory/supplier-categories`
- **Fournisseurs** (optionnel) - Basé sur `/inventory/suppliers`
- **Affichage dynamique** des fournisseurs selon la catégorie sélectionnée

##### C. Boutons de catégories
- **"Produits sans recettes"** : Affiche uniquement les catégories de produits sans recettes
- **"Ingrédients"** : Affiche uniquement les catégories d'ingrédients  
- **"Tous"** : Affiche toutes les catégories (produits + ingrédients) sauf produits avec recettes

##### D. Grille des articles
- **Affichage en grille** similaire au POS
- **Informations par article** :
  - Image
  - Nom
  - Prix d'achat
  - Quantité disponible en stock (mise à jour en temps réel)
- **Filtrage par catégorie** sélectionnée

##### E. Numpad
- **Interface identique** au POS existant
- **Saisie des quantités** à acheter/approvisionner
- **Validation** et effacement

##### F. Ticket d'achat (côté droit)
- **Liste des articles** sélectionnés avec :
  - Nom de l'article
  - Quantité
  - Prix unitaire
  - Sous-total
- **Informations d'en-tête** :
  - Nom du fournisseur (si sélectionné) ou "Autres"
  - Nom de l'utilisateur
  - Numéro de facture auto-généré
  - Date et heure
- **Total à payer** en bas

### 4. SYSTÈME DE PAIEMENT FOURNISSEURS

#### Modale de paiement avec options :

##### A. Paiement Cash depuis la Caisse
- **Type** : `cash_caisse` (distinct du cash POS)
- **Action** : Retire le montant du "Solde Total" de la caisse
- **Enregistrement** : Nouvelle entrée dans "Mouvements" > "Sorties"
- **Nouveau tableau** : Spécifique aux achats de produits/ingrédients

##### B. Paiements Bancaires
- **Chèque depuis compte banque** : `cheque_compte_banque`
- **Virement depuis compte banque** : `virement_depuis_compte_banque`  
- **Sortie cash banque** : `sortie_cash_banque`
- **Gestion** : Via nouvelle page de gestion bancaire

##### C. Paiement Différé
- **Option** : "Payer plus tard"
- **Stockage** : Facture reste dans la page du fournisseur
- **Statut** : "Non payée" jusqu'au paiement
- **Paiement groupé** : Possibilité de payer plusieurs factures d'un coup

### 5. GESTION DES STOCKS

#### Mise à jour automatique :
- **Ajout au stock** lors de la réception (contrairement au POS qui soustrait)
- **Calcul en temps réel** : Stock actuel + quantité achetée
- **Synchronisation** : Mise à jour partout où le stock est affiché
- **Traçabilité** : Enregistrement des mouvements de stock

#### Gestion des statuts :
- **Marchandise reçue et payée** : Stock mis à jour immédiatement
- **Marchandise reçue non payée** : Stock mis à jour, facture en attente
- **Bon de commande** : Option pour commandes non encore reçues

## 📊 MODÈLES DE DONNÉES

### 1. PurchaseOrder (Commandes d'achat)
```python
class PurchaseOrder(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    reference = db.Column(db.String(20), unique=True)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'))
    status = db.Column(db.Enum(PurchaseOrderStatus))  # PENDING, RECEIVED, PAID
    order_date = db.Column(db.DateTime)
    delivery_date = db.Column(db.DateTime)
    total_amount = db.Column(db.Float)
    notes = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'))
```

### 2. PurchaseOrderItem (Articles de commande)
```python
class PurchaseOrderItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    purchase_order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'))
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'))
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'))
    quantity = db.Column(db.Float)
    unit_price = db.Column(db.Float)
    total_price = db.Column(db.Float)
```

### 3. SupplierInvoice (Factures fournisseurs)
```python
class SupplierInvoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    reference = db.Column(db.String(50))
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'))
    purchase_order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'))
    amount = db.Column(db.Float)
    payment_status = db.Column(db.Enum(PaymentStatus))  # PENDING, PAID
    payment_method = db.Column(db.String(50))
    payment_date = db.Column(db.DateTime)
    due_date = db.Column(db.DateTime)
```

### 4. BankAccount (Comptes bancaires)
```python
class BankAccount(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100))
    account_number = db.Column(db.String(50))
    bank_name = db.Column(db.String(100))
    balance = db.Column(db.Float, default=0)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'))
```

## 🔄 FLUX DE TRAVAIL

### Scénario 1 : Approvisionnement avec paiement immédiat
1. Utilisateur sélectionne Mode POS
2. Sélectionne fournisseur (optionnel)
3. Ajoute articles via numpad + clic
4. Valide le ticket d'achat
5. Choisit méthode de paiement
6. Stock mis à jour immédiatement
7. Paiement enregistré dans la caisse/banque

### Scénario 2 : Approvisionnement avec paiement différé
1. Même processus jusqu'à l'étape 4
2. Sélectionne "Payer plus tard"
3. Stock mis à jour immédiatement
4. Facture stockée chez le fournisseur
5. Paiement ultérieur depuis la page fournisseur

### Scénario 3 : Bon de commande
1. Même processus jusqu'à l'étape 4
2. Marque comme "Bon de commande"
3. Stock NON mis à jour
4. Commande visible dans "Pending marchandise"
5. Réception ultérieure met à jour le stock

## 🎨 INTERFACE UTILISATEUR

### Couleurs et thème :
- **Background principal** : Différent du POS (ex: #f8f9fa au lieu de #ffffff)
- **Couleur d'accent** : Vert pour l'approvisionnement vs Bleu pour les ventes
- **Icônes** : Thème "achat/approvisionnement" (camion, boîtes, etc.)

### Responsive design :
- **Adaptation mobile** pour saisie sur tablette
- **Grille flexible** pour différentes tailles d'écran
- **Numpad optimisé** pour écrans tactiles

## 🔐 SÉCURITÉ ET PERMISSIONS

### Contrôles d'accès :
- **Permission spécifique** : `can_manage_stock_replenishment`
- **Validation des données** côté serveur
- **Audit trail** pour tous les mouvements
- **Limitation par utilisateur** selon les rôles

## 📈 INTÉGRATIONS

### Avec modules existants :
- **Inventory** : Mise à jour des stocks
- **Cash Register** : Enregistrement des sorties de caisse
- **Suppliers** : Gestion des fournisseurs et factures
- **Reports** : Rapports d'approvisionnement
- **Notifications** : Alertes de stock bas

## 🚀 PHASES DE DÉVELOPPEMENT

### Phase 1 : Infrastructure de base
- Modèles de données
- Routes principales
- Interface Mode Formulaire

### Phase 2 : Mode POS
- Interface POS adaptée
- Numpad et grille d'articles
- Système de ticket

### Phase 3 : Système de paiement
- Modale de paiement
- Intégration caisse
- Gestion bancaire

### Phase 4 : Fonctionnalités avancées
- Paiement différé
- Bons de commande
- Rapports et analytics

## 📝 NOTES TECHNIQUES

### Considérations importantes :
- **Performance** : Optimisation des requêtes pour grandes listes d'articles
- **Concurrence** : Gestion des mises à jour simultanées de stock
- **Sauvegarde** : Backup automatique des données critiques
- **Logs** : Traçabilité complète des opérations

### Technologies utilisées :
- **Backend** : Flask, SQLAlchemy
- **Frontend** : HTML5, CSS3, JavaScript ES6
- **Base de données** : SQLite/PostgreSQL
- **UI Framework** : Bootstrap 5

---

*Ce document sera mis à jour au fur et à mesure du développement des fonctionnalités.*
