{% extends 'base.html' %}

{% block title %}Paramètres de la Caisse{% endblock %}

{% block content %}
<style>
    .form-control, .form-check-input {
        border: 2px solid #007bff; /* Couleur de la bordure */
        border-radius: 0.5rem; /* Arrondi des coins */
        transition: border-color 0.3s ease; /* Transition pour l'effet de survol */
    }

    .form-control:focus, .form-check-input:focus {
        border-color: #0056b3; /* Couleur de la bordure au focus */
        box-shadow: 0 0 5px rgba(0, 123, 255, 0.5); /* Ombre au focus */
    }

    .form-check-label {
        margin-left: 0.5rem; /* Espacement à gauche pour les labels */
    }

    .form-check {
        margin-bottom: 1rem; /* Espacement entre les check-boxes */
    }
</style>

<div class="container">
    <h1 class="mb-4">Paramètres de la Caisse</h1>
    <form method="POST" action="{{ url_for('settings.cash_register') }}">
        {{ form.hidden_tag() }}
        
        <div class="mb-3">
            <label for="name" class="form-label">Nom de la caisse:</label>
            {{ form.name(class="form-control", id="name", placeholder="Entrez le nom de la caisse") }}
        </div>

        <div class="mb-3">
            <label for="number" class="form-label">Numéro de la caisse:</label>
            {{ form.number(class="form-control", id="number", placeholder="Entrez le numéro de la caisse") }}
        </div>

        <div class="mb-3">
            <div class="form-check">
                {{ form.require_float(class="form-check-input", id="require_float") }}
                <label class="form-check-label" for="require_float">Exiger un montant initial</label>
            </div>
        </div>

        <div class="mb-3">
            <label for="default_float" class="form-label">Montant initial par défaut:</label>
            {{ form.default_float(class="form-control", id="default_float", placeholder="Entrez le montant initial par défaut") }}
        </div>

        <div class="mb-3">
            <div class="form-check">
                {{ form.require_close(class="form-check-input", id="require_close") }}
                <label class="form-check-label" for="require_close">Exiger la fermeture</label>
            </div>
        </div>

        <div class="mb-3">
            <div class="form-check">
                {{ form.allow_delete(class="form-check-input", id="allow_delete") }}
                <label class="form-check-label" for="allow_delete">Autoriser la suppression</label>
            </div>
        </div>

        <div class="mb-3">
            <div class="form-check">
                {{ form.allow_void(class="form-check-input", id="allow_void") }}
                <label class="form-check-label" for="allow_void">Autoriser l'annulation</label>
            </div>
        </div>

        <div class="mb-3">
            <div class="form-check">
                {{ form.require_reason(class="form-check-input", id="require_reason") }}
                <label class="form-check-label" for="require_reason">Exiger une raison</label>
            </div>
        </div>

        <button type="submit" class="btn btn-primary">Enregistrer</button>
        <a href="{{ url_for('cash_register.index') }}" class="btn btn-secondary">Annuler</a>
    </form>
</div>
{% endblock %} 