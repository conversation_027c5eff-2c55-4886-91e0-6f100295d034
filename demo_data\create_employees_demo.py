#!/usr/bin/env python3
"""
Script pour créer des données de démonstration pour le module employés
"""

import sys
import os
from datetime import date, datetime, timedelta
from decimal import Decimal

# Ajouter le répertoire parent au path pour importer l'app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configurer l'environnement
os.environ['FLASK_APP'] = 'run.py'
os.environ['FLASK_ENV'] = 'development'

from app import create_app, db
from config import Config
from app.modules.employees.models import (
    Employee, EmployeeProfile, WorkSchedule, Attendance, Payroll, Performance,
    EmployeeStatus, ContractType, PaymentFrequency, AttendanceStatus, ShiftType
)
from app.modules.auth.models import User

def create_demo_employees():
    """Créer des employés de démonstration"""

    app = create_app()
    app.config.from_object(Config)
    
    with app.app_context():
        # Récupérer l'utilisateur owner (premier utilisateur avec role OWNER)
        owner = User.query.filter_by(role='OWNER').first()
        if not owner:
            print("Aucun utilisateur OWNER trouvé. Veuillez d'abord créer un utilisateur OWNER.")
            return
        
        print(f"Création d'employés pour l'owner: {owner.username}")
        
        # Données des employés de démonstration
        employees_data = [
            {
                'employee_id': 'EMP001',
                'first_name': 'Marie',
                'last_name': 'Dubois',
                'email': '<EMAIL>',
                'phone': '01.23.45.67.89',
                'position': 'Manager',
                'department': 'Direction',
                'base_salary': Decimal('3500.00'),
                'hourly_rate': Decimal('20.00'),
                'contract_type': ContractType.FULL_TIME,
                'hire_date': date(2023, 1, 15)
            },
            {
                'employee_id': 'EMP002',
                'first_name': 'Pierre',
                'last_name': 'Martin',
                'email': '<EMAIL>',
                'phone': '01.23.45.67.90',
                'position': 'Chef Cuisinier',
                'department': 'Cuisine',
                'base_salary': Decimal('2800.00'),
                'hourly_rate': Decimal('16.50'),
                'contract_type': ContractType.FULL_TIME,
                'hire_date': date(2023, 3, 1)
            },
            {
                'employee_id': 'EMP003',
                'first_name': 'Sophie',
                'last_name': 'Bernard',
                'email': '<EMAIL>',
                'phone': '01.23.45.67.91',
                'position': 'Serveuse',
                'department': 'Service',
                'base_salary': Decimal('1800.00'),
                'hourly_rate': Decimal('12.00'),
                'contract_type': ContractType.PART_TIME,
                'hire_date': date(2023, 6, 10)
            },
            {
                'employee_id': 'EMP004',
                'first_name': 'Thomas',
                'last_name': 'Leroy',
                'email': '<EMAIL>',
                'phone': '01.23.45.67.92',
                'position': 'Caissier',
                'department': 'Service',
                'base_salary': Decimal('2000.00'),
                'hourly_rate': Decimal('13.50'),
                'contract_type': ContractType.FULL_TIME,
                'hire_date': date(2023, 8, 20)
            },
            {
                'employee_id': 'EMP005',
                'first_name': 'Julie',
                'last_name': 'Moreau',
                'email': '<EMAIL>',
                'phone': '01.23.45.67.93',
                'position': 'Commis de cuisine',
                'department': 'Cuisine',
                'base_salary': Decimal('1600.00'),
                'hourly_rate': Decimal('11.00'),
                'contract_type': ContractType.TEMPORARY,
                'hire_date': date(2024, 1, 5)
            }
        ]
        
        created_employees = []
        
        for emp_data in employees_data:
            # Vérifier si l'employé existe déjà
            existing = Employee.query.filter_by(
                employee_id=emp_data['employee_id'],
                owner_id=owner.id
            ).first()
            
            if existing:
                print(f"Employé {emp_data['employee_id']} existe déjà, ignoré.")
                created_employees.append(existing)
                continue
            
            # Créer l'employé
            employee = Employee(
                employee_id=emp_data['employee_id'],
                first_name=emp_data['first_name'],
                last_name=emp_data['last_name'],
                email=emp_data['email'],
                phone=emp_data['phone'],
                position=emp_data['position'],
                department=emp_data['department'],
                base_salary=emp_data['base_salary'],
                hourly_rate=emp_data['hourly_rate'],
                contract_type=emp_data['contract_type'],
                payment_frequency=PaymentFrequency.MONTHLY,
                hire_date=emp_data['hire_date'],
                status=EmployeeStatus.ACTIVE,
                owner_id=owner.id
            )
            
            db.session.add(employee)
            db.session.flush()  # Pour obtenir l'ID
            
            # Créer le profil de l'employé
            profile = EmployeeProfile(
                employee_id=employee.id,
                emergency_contact_name=f"Contact {emp_data['first_name']}",
                emergency_contact_phone="***********.32",
                emergency_contact_relationship="Famille",
                skills="Expérience en restauration, Service client",
                languages="Français, Anglais"
            )
            
            db.session.add(profile)
            created_employees.append(employee)
            
            print(f"Employé créé: {employee.full_name} ({employee.employee_id})")
        
        # Créer quelques plannings
        print("\nCréation des plannings...")
        for employee in created_employees:
            # Planning du lundi au vendredi
            for day in range(5):  # 0-4 = Lundi à Vendredi
                schedule = WorkSchedule(
                    employee_id=employee.id,
                    owner_id=owner.id,
                    start_date=date.today() - timedelta(days=30),
                    end_date=date.today() + timedelta(days=30),
                    day_of_week=day,
                    shift_type=ShiftType.FULL_DAY,
                    start_time=datetime.strptime('09:00', '%H:%M').time(),
                    end_time=datetime.strptime('17:00', '%H:%M').time(),
                    break_duration=60,
                    is_recurring=True,
                    created_by_id=owner.id
                )
                db.session.add(schedule)
        
        # Créer quelques présences pour les 7 derniers jours
        print("Création des présences...")
        for i in range(7):
            attendance_date = date.today() - timedelta(days=i)
            
            for employee in created_employees[:3]:  # Seulement pour les 3 premiers
                attendance = Attendance(
                    employee_id=employee.id,
                    owner_id=owner.id,
                    date=attendance_date,
                    status=AttendanceStatus.PRESENT,
                    clock_in_time=datetime.combine(attendance_date, datetime.strptime('09:00', '%H:%M').time()),
                    clock_out_time=datetime.combine(attendance_date, datetime.strptime('17:00', '%H:%M').time()),
                    break_duration=60,
                    scheduled_hours=Decimal('8.0'),
                    hours_worked=Decimal('7.0')  # 8h - 1h de pause
                )
                db.session.add(attendance)
        
        # Créer quelques évaluations de performance
        print("Création des évaluations...")
        for employee in created_employees[:2]:  # Seulement pour les 2 premiers
            performance = Performance(
                employee_id=employee.id,
                owner_id=owner.id,
                evaluator_id=owner.id,
                evaluation_period_start=date(2024, 1, 1),
                evaluation_period_end=date(2024, 6, 30),
                evaluation_date=date(2024, 7, 1),
                punctuality_score=4,
                quality_of_work_score=5,
                teamwork_score=4,
                communication_score=4,
                initiative_score=3,
                customer_service_score=5,
                overall_score=Decimal('4.2'),
                strengths="Excellent service client, très ponctuel",
                areas_for_improvement="Pourrait prendre plus d'initiatives",
                evaluator_comments="Employé très fiable et apprécié des clients",
                is_finalized=True
            )
            db.session.add(performance)
        
        # Sauvegarder toutes les données
        db.session.commit()
        
        print(f"\n✅ Données de démonstration créées avec succès !")
        print(f"   - {len(created_employees)} employés")
        print(f"   - {len(created_employees) * 5} plannings")
        print(f"   - {3 * 7} présences")
        print(f"   - 2 évaluations de performance")
        print(f"\nVous pouvez maintenant tester le module RH à l'adresse: http://127.0.0.1:5000/employees")

if __name__ == '__main__':
    create_demo_employees()
