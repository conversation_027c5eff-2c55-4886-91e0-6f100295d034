{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row">
        <!-- Colonne principale -->
        <div class="col-md-8">
            <!-- <PERSON><PERSON> de la recette -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-book-open"></i>
                        Fiche Technique - {{ product.name }}
                    </h5>
                    <span class="badge {% if product.get_stock_status() == 'in_stock' %}bg-success{% elif product.get_stock_status() == 'low_stock' %}bg-warning{% else %}bg-danger{% endif %}">
                        {{ product.get_available_quantity() }} unités disponibles
                    </span>
                </div>
                
                <div class="card-body">
                    <!-- Informations de base -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Temps de préparation</h6>
                            <p class="mb-0">{{ recipe.preparation_time or 0 }} minutes</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Coût de production</h6>
                            <p class="mb-0">{{ "%.2f"|format(recipe.calculate_cost()) }} €</p>
                        </div>
                    </div>

                    <!-- Instructions -->
                    <form method="POST" class="mb-4">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.instructions.label(class="form-label") }}
                            {{ form.instructions(class="form-control" + (" is-invalid" if form.instructions.errors else ""), rows=4) }}
                            {% for error in form.instructions.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.preparation_time.label(class="form-label") }}
                            {{ form.preparation_time(class="form-control" + (" is-invalid" if form.preparation_time.errors else ""), type="number") }}
                            {% for error in form.preparation_time.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer les modifications
                        </button>
                    </form>

                    <hr>

                    <!-- Liste des ingrédients -->
                    <h5 class="mb-3">Ingrédients</h5>
                    
                    <!-- Tableau des ingrédients -->
                    <div class="table-responsive mb-3">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Ingrédient</th>
                                    <th>Quantité</th>
                                    <th>Stock disponible</th>
                                    <th>Coût</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in recipe.items %}
                                <tr>
                                    <td>{{ item.ingredient.name }}</td>
                                    <td>{{ item.quantity }} {{ item.ingredient.unit }}</td>
                                    <td>
                                        {% set stock_status = 'success' if item.ingredient.stock_quantity >= item.quantity else 'danger' %}
                                        <span class="badge bg-{{ stock_status }}">
                                            {{ item.ingredient.stock_quantity }} {{ item.ingredient.unit }}
                                        </span>
                                    </td>
                                    <td>{{ "%.2f"|format(item.calculate_cost()) }} €</td>
                                    <td>
                                        <button type="button" 
                                                class="btn btn-outline-danger btn-sm"
                                                onclick="removeIngredient('{{ item.id }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="5" class="text-center">
                                        <p class="text-muted mb-0">Aucun ingrédient dans la recette</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Bouton pour ajouter un ingrédient -->
                    <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addIngredientModal">
                        <i class="fas fa-plus"></i> Ajouter un ingrédient
                    </button>
                </div>
            </div>
        </div>

        <!-- Colonne latérale -->
        <div class="col-md-4">
            <!-- Carte des informations produit -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i>
                        Informations produit
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Prix de vente</h6>
                        <p class="h4">{{ "%.2f"|format(product.price) }} €</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Coût de production</h6>
                        <p class="h4">{{ "%.2f"|format(recipe.calculate_cost()) }} €</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Marge brute</h6>
                        {% set margin = ((product.price - recipe.calculate_cost()) / product.price * 100) if product.price > 0 else 0 %}
                        <p class="h4">{{ "%.1f"|format(margin) }}%</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Production possible</h6>
                        <p class="h4">{{ recipe.get_possible_quantity() }} unités</p>
                    </div>
                </div>
            </div>

            <!-- Carte des alertes -->
            {% set missing_ingredients = recipe.get_ingredients_needed() %}
            {% if missing_ingredients %}
            <div class="card mb-4 border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        Ingrédients manquants
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        {% for item in missing_ingredients %}
                        {% if item.quantity_needed > item.available %}
                        <li class="mb-2">
                            <strong>{{ item.ingredient.name }}:</strong><br>
                            Manquant: {{ item.quantity_needed - item.available }} {{ item.unit }}
                        </li>
                        {% endif %}
                        {% endfor %}
                    </ul>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal pour ajouter un ingrédient -->
<div class="modal fade" id="addIngredientModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ajouter un ingrédient</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addIngredientForm" method="POST" action="{{ url_for('inventory.add_recipe_item', product_id=product.id) }}">
                    {{ ingredient_form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ ingredient_form.ingredient_id.label(class="form-label") }}
                        {{ ingredient_form.ingredient_id(class="form-select") }}
                    </div>
                    
                    <div class="mb-3">
                        {{ ingredient_form.quantity.label(class="form-label") }}
                        {{ ingredient_form.quantity(class="form-control", type="number", step="0.01") }}
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="submit" form="addIngredientForm" class="btn btn-primary">Ajouter</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function removeIngredient(itemId) {
    if (confirm('Êtes-vous sûr de vouloir retirer cet ingrédient ?')) {
        fetch(`{{ url_for('inventory.recipe', product_id=product.id) }}/items/${itemId}/remove`, {
            method: 'POST'
        }).then(response => {
            if (response.ok) {
                location.reload();
            }
        });
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Mise à jour automatique des coûts et quantités
    setInterval(function() {
        fetch(`{{ url_for('inventory.check_stock', product_id=product.id) }}`)
            .then(response => response.json())
            .then(data => {
                // Mettre à jour les informations
                document.querySelector('.available-quantity').textContent = data.available_quantity;
                document.querySelector('.production-cost').textContent = data.cost.toFixed(2);
                document.querySelector('.profit-margin').textContent = data.margin.toFixed(1);
            });
    }, 30000); // Mise à jour toutes les 30 secondes
});
</script>
{% endblock %} 