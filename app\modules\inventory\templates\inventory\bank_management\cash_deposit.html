{% extends "base.html" %}

{% block title %}Dépôt depuis <PERSON>{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-2">
                <h4 class="mb-0">
                    <i class="fas fa-money-bill-wave"></i> Dépôt depuis Caisse
                </h4>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.bank_management_index') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-exchange-alt"></i> Transfert Caisse → Banque</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="cashDepositForm">
                            {{ form.hidden_tag() }}
                            
                            <!-- Caisse source -->
                            <div class="mb-3">
                                {{ form.cash_register_id.label(class="form-label") }}
                                {{ form.cash_register_id(class="form-select", id="cashRegisterSelect") }}
                                {% if form.cash_register_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.cash_register_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div id="cashBalanceInfo" class="mt-2" style="display: none;">
                                    <small class="text-muted">Solde disponible: <span id="cashBalance">0.00</span> €</small>
                                </div>
                            </div>

                            <!-- Compte bancaire destination -->
                            <div class="mb-3">
                                {{ form.bank_account_id.label(class="form-label") }}
                                {{ form.bank_account_id(class="form-select", id="bankAccountSelect") }}
                                {% if form.bank_account_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.bank_account_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div id="bankBalanceInfo" class="mt-2" style="display: none;">
                                    <small class="text-muted">Solde actuel: <span id="bankBalance">0.00</span> €</small>
                                </div>
                            </div>

                            <!-- Montant -->
                            <div class="mb-3">
                                {{ form.amount.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.amount(class="form-control", step="0.01", min="0.01", id="amountInput") }}
                                    <span class="input-group-text">€</span>
                                </div>
                                {% if form.amount.errors %}
                                    <div class="text-danger">
                                        {% for error in form.amount.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div id="amountWarning" class="mt-2" style="display: none;">
                                    <small class="text-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Montant supérieur au solde disponible
                                    </small>
                                </div>
                            </div>

                            <!-- Description -->
                            <div class="mb-3">
                                {{ form.description.label(class="form-label") }}
                                {{ form.description(class="form-control") }}
                                {% if form.description.errors %}
                                    <div class="text-danger">
                                        {% for error in form.description.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Référence -->
                            <div class="mb-3">
                                {{ form.reference.label(class="form-label") }}
                                {{ form.reference(class="form-control", placeholder="Numéro de bordereau, référence...") }}
                                {% if form.reference.errors %}
                                    <div class="text-danger">
                                        {% for error in form.reference.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Notes -->
                            <div class="mb-3">
                                {{ form.notes.label(class="form-label") }}
                                {{ form.notes(class="form-control", rows="3") }}
                                {% if form.notes.errors %}
                                    <div class="text-danger">
                                        {% for error in form.notes.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Aperçu du transfert -->
                            <div class="alert alert-info" id="transferPreview" style="display: none;">
                                <h6><i class="fas fa-info-circle"></i> Aperçu du Transfert</h6>
                                <div id="previewContent"></div>
                            </div>

                            <!-- Boutons -->
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('inventory.bank_management_index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-success" id="submitBtn" disabled>
                                    <i class="fas fa-exchange-alt"></i> Effectuer le dépôt
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Informations complémentaires -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-info-circle"></i> Informations</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6>Dépôt de caisse</h6>
                            <p class="small mb-2">
                                Cette opération transfère de l'argent liquide de votre caisse vers un compte bancaire.
                            </p>
                            <ul class="small mb-0">
                                <li>Le montant sera débité de la caisse</li>
                                <li>Le montant sera crédité sur le compte bancaire</li>
                                <li>Les deux opérations sont liées</li>
                            </ul>
                        </div>

                        <div class="mt-3">
                            <h6>Caisses disponibles</h6>
                            {% if cash_registers_with_totals %}
                                {% for register in cash_registers_with_totals %}
                                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                    <span>{{ register.name }}</span>
                                    <span class="text-success">{{ "%.2f"|format(register.total_balance) }} €</span>
                                </div>
                                {% endfor %}
                            {% else %}
                                <p class="text-muted small">Aucune caisse ouverte</p>
                            {% endif %}
                        </div>

                        <div class="mt-3">
                            <h6>Comptes bancaires</h6>
                            {% if bank_accounts %}
                                {% for account in bank_accounts %}
                                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                    <span>{{ account.name }}</span>
                                    <span class="text-{{ 'success' if account.balance >= 0 else 'danger' }}">
                                        {{ "%.2f"|format(account.balance) }} €
                                    </span>
                                </div>
                                {% endfor %}
                            {% else %}
                                <p class="text-muted small">Aucun compte bancaire</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const cashRegisterSelect = document.getElementById('cashRegisterSelect');
    const bankAccountSelect = document.getElementById('bankAccountSelect');
    const amountInput = document.getElementById('amountInput');
    const submitBtn = document.getElementById('submitBtn');
    const transferPreview = document.getElementById('transferPreview');
    const previewContent = document.getElementById('previewContent');
    const cashBalanceInfo = document.getElementById('cashBalanceInfo');
    const bankBalanceInfo = document.getElementById('bankBalanceInfo');
    const cashBalance = document.getElementById('cashBalance');
    const bankBalance = document.getElementById('bankBalance');
    const amountWarning = document.getElementById('amountWarning');

    // Données des comptes (passées depuis le template)
    const cashRegisters = {{ cash_registers_json | safe }};
    const bankAccounts = {{ bank_accounts_json | safe }};

    // Mettre à jour les informations de solde
    function updateBalanceInfo() {
        const selectedCash = cashRegisterSelect.value;
        const selectedBank = bankAccountSelect.value;

        if (selectedCash && selectedCash !== '0') {
            const cashData = cashRegisters.find(c => c.id == selectedCash);
            if (cashData) {
                cashBalance.textContent = cashData.total_balance.toFixed(2);
                cashBalanceInfo.style.display = 'block';
            }
        } else {
            cashBalanceInfo.style.display = 'none';
        }

        if (selectedBank && selectedBank !== '0') {
            const bankData = bankAccounts.find(b => b.id == selectedBank);
            if (bankData) {
                bankBalance.textContent = bankData.balance.toFixed(2);
                bankBalanceInfo.style.display = 'block';
            }
        } else {
            bankBalanceInfo.style.display = 'none';
        }

        updatePreview();
    }

    // Mettre à jour l'aperçu
    function updatePreview() {
        const selectedCash = cashRegisterSelect.value;
        const selectedBank = bankAccountSelect.value;
        const amount = parseFloat(amountInput.value) || 0;

        if (selectedCash && selectedCash !== '0' && selectedBank && selectedBank !== '0' && amount > 0) {
            const cashData = cashRegisters.find(c => c.id == selectedCash);
            const bankData = bankAccounts.find(b => b.id == selectedBank);

            if (cashData && bankData) {
                const newCashBalance = cashData.total_balance - amount;
                const newBankBalance = bankData.balance + amount;

                previewContent.innerHTML = `
                    <div class="row">
                        <div class="col-6">
                            <strong>Caisse:</strong><br>
                            ${cashData.name}<br>
                            <span class="text-danger">-${amount.toFixed(2)} €</span><br>
                            <small>Nouveau solde: ${newCashBalance.toFixed(2)} €</small>
                        </div>
                        <div class="col-6">
                            <strong>Compte bancaire:</strong><br>
                            ${bankData.name}<br>
                            <span class="text-success">+${amount.toFixed(2)} €</span><br>
                            <small>Nouveau solde: ${newBankBalance.toFixed(2)} €</small>
                        </div>
                    </div>
                `;

                transferPreview.style.display = 'block';

                // Vérifier si le montant est disponible (utiliser total_balance)
                if (amount > cashData.total_balance) {
                    amountWarning.style.display = 'block';
                    submitBtn.disabled = true;
                } else {
                    amountWarning.style.display = 'none';
                    submitBtn.disabled = false;
                }
            }
        } else {
            transferPreview.style.display = 'none';
            submitBtn.disabled = true;
        }
    }

    // Événements
    cashRegisterSelect.addEventListener('change', updateBalanceInfo);
    bankAccountSelect.addEventListener('change', updateBalanceInfo);
    amountInput.addEventListener('input', updatePreview);

    // Validation du formulaire
    document.getElementById('cashDepositForm').addEventListener('submit', function(e) {
        const amount = parseFloat(amountInput.value) || 0;
        const selectedCash = cashRegisterSelect.value;

        if (selectedCash && selectedCash !== '0') {
            const cashData = cashRegisters.find(c => c.id == selectedCash);
            if (cashData && amount > cashData.total_balance) {
                e.preventDefault();
                alert('Le montant dépasse le solde total disponible en caisse');
                return;
            }
        }

        if (amount > 1000) {
            if (!confirm(`Confirmer le dépôt de ${amount.toFixed(2)} € ?`)) {
                e.preventDefault();
                return;
            }
        }
    });

    // Initialisation
    updateBalanceInfo();
});
</script>
{% endblock %}
