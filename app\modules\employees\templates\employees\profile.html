{% extends "employees/base_hr.html" %}

{% block title %}Profil - {{ employee.full_name }}{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-user me-2"></i>Profil de {{ employee.full_name }}
    </h1>
    <div>
        <a href="{{ url_for('employees.edit', id=employee.id) }}" class="btn btn-outline-primary me-2">
            <i class="fas fa-edit me-1"></i>Modifier
        </a>
        <a href="{{ url_for('employees.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour
        </a>
    </div>
</div>

<div class="row">
    <!-- Informations personnelles -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-id-card me-2"></i>Informations Personnelles
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-lg bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-user fa-2x"></i>
                    </div>
                    <h5 class="mb-1">{{ employee.full_name }}</h5>
                    <p class="text-muted mb-0">{{ employee.position }}</p>
                </div>
                
                <hr>
                
                <div class="row g-2">
                    <div class="col-sm-6">
                        <strong>ID Employé:</strong>
                    </div>
                    <div class="col-sm-6">
                        {{ employee.employee_id }}
                    </div>
                    
                    <div class="col-sm-6">
                        <strong>Email:</strong>
                    </div>
                    <div class="col-sm-6">
                        <a href="mailto:{{ employee.email }}">{{ employee.email }}</a>
                    </div>
                    
                    <div class="col-sm-6">
                        <strong>Téléphone:</strong>
                    </div>
                    <div class="col-sm-6">
                        {% if employee.phone %}
                            <a href="tel:{{ employee.phone }}">{{ employee.phone }}</a>
                        {% else %}
                            <span class="text-muted">Non renseigné</span>
                        {% endif %}
                    </div>
                    
                    <div class="col-sm-6">
                        <strong>Département:</strong>
                    </div>
                    <div class="col-sm-6">
                        {{ employee.department or 'Non assigné' }}
                    </div>
                    
                    <div class="col-sm-6">
                        <strong>Statut:</strong>
                    </div>
                    <div class="col-sm-6">
                        {% if employee.status == 'ACTIVE' %}
                            <span class="badge bg-success">Actif</span>
                        {% elif employee.status == 'INACTIVE' %}
                            <span class="badge bg-secondary">Inactif</span>
                        {% elif employee.status == 'SUSPENDED' %}
                            <span class="badge bg-warning">Suspendu</span>
                        {% elif employee.status == 'TERMINATED' %}
                            <span class="badge bg-danger">Licencié</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Informations professionnelles -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-briefcase me-2"></i>Informations Professionnelles
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Date d'embauche</label>
                            <p class="mb-0">{{ employee.hire_date.strftime('%d/%m/%Y') }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Type de contrat</label>
                            <p class="mb-0">{{ employee.contract_type.replace('_', ' ').title() if employee.contract_type else 'Non défini' }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Salaire de base</label>
                            <p class="mb-0">
                                {% if employee.base_salary %}
                                    {{ "%.2f"|format(employee.base_salary) }}€
                                    <small class="text-muted">({{ employee.payment_frequency.replace('_', ' ').lower() if employee.payment_frequency else 'mensuel' }})</small>
                                {% else %}
                                    <span class="text-muted">Non défini</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Taux horaire</label>
                            <p class="mb-0">
                                {% if employee.hourly_rate %}
                                    {{ "%.2f"|format(employee.hourly_rate) }}€/h
                                {% else %}
                                    <span class="text-muted">Non défini</span>
                                {% endif %}
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Ancienneté</label>
                            <p class="mb-0">
                                {% if seniority_info %}
                                    {{ seniority_info }}
                                {% else %}
                                    <span class="text-muted">Non définie</span>
                                {% endif %}
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Adresse</label>
                            <p class="mb-0">
                                {% if employee.address %}
                                    {{ employee.address }}
                                {% else %}
                                    <span class="text-muted">Non renseignée</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Actions Rapides
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('employees.new_attendance', id=employee.id) }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-clock me-2"></i>Nouvelle Présence
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('employees.new_schedule', id=employee.id) }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-calendar me-2"></i>Nouveau Planning
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('employees.new_payroll', id=employee.id) }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-money-bill me-2"></i>Nouvelle Paie
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('employees.new_performance', id=employee.id) }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-star me-2"></i>Nouvelle Évaluation
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
}
</style>
{% endblock %}
