{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Personnalisation</h1>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Theme Card -->
        <div class="col-xl-6 col-md-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thème & Apparence</h6>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('advanced.save_theme') }}" method="POST">
                        <div class="mb-3">
                            <label for="primaryColor" class="form-label">Couleur principale</label>
                            <input type="color" class="form-control form-control-color" id="primaryColor" name="primary_color" value="#4e73df">
                        </div>
                        <div class="mb-3">
                            <label for="secondaryColor" class="form-label">Couleur secondaire</label>
                            <input type="color" class="form-control form-control-color" id="secondaryColor" name="secondary_color" value="#858796">
                        </div>
                        <div class="mb-3">
                            <label for="theme" class="form-label">Thème</label>
                            <select class="form-select" id="theme" name="theme">
                                <option value="light">Clair</option>
                                <option value="dark">Sombre</option>
                                <option value="auto">Auto (selon système)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="font" class="form-label">Police</label>
                            <select class="form-select" id="font" name="font">
                                <option value="nunito">Nunito</option>
                                <option value="roboto">Roboto</option>
                                <option value="open-sans">Open Sans</option>
                                <option value="lato">Lato</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">Enregistrer le thème</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Layout Card -->
        <div class="col-xl-6 col-md-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Mise en page</h6>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('advanced.save_layout') }}" method="POST">
                        <div class="mb-3">
                            <label for="sidebarPosition" class="form-label">Position de la barre latérale</label>
                            <select class="form-select" id="sidebarPosition" name="sidebar_position">
                                <option value="left">Gauche</option>
                                <option value="right">Droite</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="containerWidth" class="form-label">Largeur du contenu</label>
                            <select class="form-select" id="containerWidth" name="container_width">
                                <option value="fluid">Fluide</option>
                                <option value="fixed">Fixe</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="stickyHeader" name="sticky_header">
                                <label class="form-check-label" for="stickyHeader">
                                    En-tête fixe
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="stickyFooter" name="sticky_footer">
                                <label class="form-check-label" for="stickyFooter">
                                    Pied de page fixe
                                </label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Enregistrer la mise en page</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Branding Card -->
        <div class="col-xl-6 col-md-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Image de marque</h6>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('advanced.save_branding') }}" method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="logo" class="form-label">Logo</label>
                            <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                            <div class="form-text">Format recommandé : PNG transparent, 200x50px</div>
                        </div>
                        <div class="mb-3">
                            <label for="favicon" class="form-label">Favicon</label>
                            <input type="file" class="form-control" id="favicon" name="favicon" accept="image/x-icon,image/png">
                            <div class="form-text">Format recommandé : ICO ou PNG, 32x32px</div>
                        </div>
                        <div class="mb-3">
                            <label for="appName" class="form-label">Nom de l'application</label>
                            <input type="text" class="form-control" id="appName" name="app_name" placeholder="Mon Application">
                        </div>
                        <button type="submit" class="btn btn-primary">Enregistrer l'image de marque</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Email Templates Card -->
        <div class="col-xl-6 col-md-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Modèles d'emails</h6>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('advanced.save_email_templates') }}" method="POST">
                        <div class="mb-3">
                            <label for="emailTemplate" class="form-label">Modèle</label>
                            <select class="form-select" id="emailTemplate" name="template">
                                <option value="welcome">Email de bienvenue</option>
                                <option value="order">Confirmation de commande</option>
                                <option value="invoice">Facture</option>
                                <option value="reset_password">Réinitialisation mot de passe</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="emailSubject" class="form-label">Sujet</label>
                            <input type="text" class="form-control" id="emailSubject" name="subject">
                        </div>
                        <div class="mb-3">
                            <label for="emailContent" class="form-label">Contenu</label>
                            <textarea class="form-control" id="emailContent" name="content" rows="10"></textarea>
                            <div class="form-text">Variables disponibles : {name}, {email}, {date}, etc.</div>
                        </div>
                        <button type="submit" class="btn btn-primary">Enregistrer le modèle</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Card -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Aperçu</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h4>Bureau</h4>
                            <div id="desktopPreview" class="border p-3 mb-4" style="min-height: 300px;">
                                <!-- Preview content -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h4>Mobile</h4>
                            <div id="mobilePreview" class="border p-3" style="max-width: 375px; min-height: 300px;">
                                <!-- Preview content -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Preview updates
    function updatePreview() {
        const primaryColor = document.getElementById('primaryColor').value;
        const secondaryColor = document.getElementById('secondaryColor').value;
        const font = document.getElementById('font').value;
        
        const desktopPreview = document.getElementById('desktopPreview');
        const mobilePreview = document.getElementById('mobilePreview');
        
        // Update preview styles
        [desktopPreview, mobilePreview].forEach(preview => {
            preview.style.fontFamily = font;
            preview.style.setProperty('--primary-color', primaryColor);
            preview.style.setProperty('--secondary-color', secondaryColor);
        });
    }
    
    // Add event listeners
    document.getElementById('primaryColor').addEventListener('input', updatePreview);
    document.getElementById('secondaryColor').addEventListener('input', updatePreview);
    document.getElementById('font').addEventListener('change', updatePreview);
    
    // Initial preview
    updatePreview();
    
    // Email template handling
    const emailTemplate = document.getElementById('emailTemplate');
    const emailSubject = document.getElementById('emailSubject');
    const emailContent = document.getElementById('emailContent');
    
    emailTemplate.addEventListener('change', function() {
        // Fetch template content
        fetch(`/advanced/email-template/${this.value}`)
            .then(response => response.json())
            .then(data => {
                emailSubject.value = data.subject;
                emailContent.value = data.content;
            })
            .catch(error => console.error('Error:', error));
    });
});
</script>
{% endblock %} 