{% extends "ai_support/base.html" %}

{% block support_content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ url_for('ai_support.index') }}">Support AI</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{{ url_for('ai_support.tickets') }}">Mes tickets</a>
                </li>
                <li class="breadcrumb-item active">{{ ticket.ticket_number }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Informations du ticket -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">{{ ticket.title }}</h4>
                        <div class="d-flex align-items-center gap-3">
                            <span class="ticket-status status-{{ ticket.status.value }}">
                                {{ ticket.status.value.replace('_', ' ').title() }}
                            </span>
                            <span class="priority-{{ ticket.priority.value }}">
                                <i class="fas fa-flag"></i> {{ ticket.priority.value.title() }}
                            </span>
                            <span class="badge bg-secondary">
                                {{ ticket.category.value.replace('_', ' ').title() }}
                            </span>
                        </div>
                    </div>
                    <div class="text-end">
                        <h5 class="mb-0">{{ ticket.ticket_number }}</h5>
                        <small class="text-muted">{{ ticket.created_at.strftime('%d/%m/%Y %H:%M') }}</small>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>Description initiale :</h6>
                    <p class="text-muted">{{ ticket.description|nl2br|safe }}</p>
                </div>
                
                {% if ticket.escalated_to_human %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Ticket escaladé vers un agent humain</strong>
                    {% if ticket.escalation_reason %}
                    <br>Raison : {{ ticket.escalation_reason }}
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Messages -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-comments"></i> Conversation
                    <span class="badge bg-primary ms-2">{{ messages|length }} messages</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="messages-container" style="max-height: 500px; overflow-y: auto;">
                    {% for message in messages %}
                    <div class="message-item mb-3 {% if message.sender_type.value == 'user' %}user-message{% elif message.sender_type.value == 'ai' %}ai-message{% else %}system-message{% endif %}">
                        <div class="d-flex align-items-start">
                            <div class="message-avatar me-3">
                                {% if message.sender_type.value == 'user' %}
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-user"></i>
                                </div>
                                {% elif message.sender_type.value == 'ai' %}
                                <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-robot"></i>
                                </div>
                                {% else %}
                                <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-cog"></i>
                                </div>
                                {% endif %}
                            </div>
                            <div class="message-content flex-grow-1">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <strong>
                                        {% if message.sender_type.value == 'user' %}
                                            {{ message.sender.username if message.sender else 'Utilisateur' }}
                                        {% elif message.sender_type.value == 'ai' %}
                                            Assistant IA
                                        {% elif message.sender_type.value == 'human_agent' %}
                                            {{ message.sender.username if message.sender else 'Agent' }}
                                        {% else %}
                                            Système
                                        {% endif %}
                                    </strong>
                                    <small class="text-muted">{{ message.created_at.strftime('%d/%m/%Y %H:%M') }}</small>
                                </div>
                                <div class="message-text">
                                    {{ message.content|nl2br|safe }}
                                </div>
                                {% if message.sender_type.value == 'ai' and message.ai_confidence %}
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-chart-line"></i> 
                                        Confiance: {{ (message.ai_confidence * 100)|round }}%
                                        {% if message.ai_model_used %}
                                        | Modèle: {{ message.ai_model_used }}
                                        {% endif %}
                                        {% if message.processing_time %}
                                        | Temps: {{ "%.2f"|format(message.processing_time) }}s
                                        {% endif %}
                                    </small>
                                </div>
                                {% endif %}
                                {% if message.is_internal %}
                                <div class="mt-1">
                                    <span class="badge bg-warning">Message interne</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <!-- Ajouter un message -->
        {% if ticket.status.value not in ['closed'] %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Ajouter un message</h6>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('ai_support.add_message', id=ticket.id) }}">
                    {{ message_form.hidden_tag() }}
                    <div class="mb-3">
                        {{ message_form.content(class="form-control", rows="4", placeholder="Tapez votre message...") }}
                        {% if message_form.content.errors %}
                            <div class="text-danger">
                                {% for error in message_form.content.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    {% if current_user.is_admin %}
                    <div class="mb-3">
                        <div class="form-check">
                            {{ message_form.is_internal(class="form-check-input") }}
                            {{ message_form.is_internal.label(class="form-check-label") }}
                        </div>
                    </div>
                    {% endif %}
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-info" onclick="askAI()">
                            <i class="fas fa-robot"></i> Demander à l'IA
                        </button>
                        {{ message_form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-lg-4">
        <!-- Informations du ticket -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Informations</h6>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-5">Numéro :</dt>
                    <dd class="col-sm-7">{{ ticket.ticket_number }}</dd>
                    
                    <dt class="col-sm-5">Statut :</dt>
                    <dd class="col-sm-7">
                        <span class="ticket-status status-{{ ticket.status.value }}">
                            {{ ticket.status.value.replace('_', ' ').title() }}
                        </span>
                    </dd>
                    
                    <dt class="col-sm-5">Priorité :</dt>
                    <dd class="col-sm-7">
                        <span class="priority-{{ ticket.priority.value }}">
                            <i class="fas fa-flag"></i> {{ ticket.priority.value.title() }}
                        </span>
                    </dd>
                    
                    <dt class="col-sm-5">Catégorie :</dt>
                    <dd class="col-sm-7">{{ ticket.category.value.replace('_', ' ').title() }}</dd>
                    
                    <dt class="col-sm-5">Créé le :</dt>
                    <dd class="col-sm-7">{{ ticket.created_at.strftime('%d/%m/%Y %H:%M') }}</dd>
                    
                    <dt class="col-sm-5">Mis à jour :</dt>
                    <dd class="col-sm-7">{{ ticket.updated_at.strftime('%d/%m/%Y %H:%M') }}</dd>
                    
                    {% if ticket.resolved_at %}
                    <dt class="col-sm-5">Résolu le :</dt>
                    <dd class="col-sm-7">{{ ticket.resolved_at.strftime('%d/%m/%Y %H:%M') }}</dd>
                    {% endif %}
                    
                    {% if ticket.assigned_agent %}
                    <dt class="col-sm-5">Agent :</dt>
                    <dd class="col-sm-7">{{ ticket.assigned_agent.username }}</dd>
                    {% endif %}
                </dl>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if ticket.status.value not in ['resolved', 'closed'] %}
                    <button class="btn btn-outline-warning" onclick="escalateTicket()">
                        <i class="fas fa-user"></i> Escalader vers un agent
                    </button>
                    {% endif %}
                    
                    {% if ticket.status.value == 'resolved' %}
                    <button class="btn btn-success" onclick="closeTicket()">
                        <i class="fas fa-check"></i> Fermer le ticket
                    </button>
                    <button class="btn btn-outline-warning" onclick="reopenTicket()">
                        <i class="fas fa-redo"></i> Rouvrir le ticket
                    </button>
                    {% endif %}
                    
                    <a href="{{ url_for('ai_support.chat') }}" class="btn btn-outline-primary">
                        <i class="fas fa-comments"></i> Chat en direct
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Évaluation -->
        {% if ticket.status.value in ['resolved', 'closed'] and not ticket.satisfaction_rating %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Évaluer le support</h6>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('ai_support.rate_ticket', id=ticket.id) }}">
                    {{ satisfaction_form.hidden_tag() }}
                    <div class="mb-3">
                        {{ satisfaction_form.rating.label(class="form-label") }}
                        {{ satisfaction_form.rating(class="form-select") }}
                    </div>
                    <div class="mb-3">
                        {{ satisfaction_form.comment(class="form-control", rows="3") }}
                    </div>
                    {{ satisfaction_form.submit(class="btn btn-primary w-100") }}
                </form>
            </div>
        </div>
        {% elif ticket.satisfaction_rating %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Votre évaluation</h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="mb-2">
                        {% for i in range(1, 6) %}
                        <i class="fas fa-star {% if i <= ticket.satisfaction_rating %}text-warning{% else %}text-muted{% endif %}"></i>
                        {% endfor %}
                    </div>
                    <p class="text-muted">{{ ticket.satisfaction_rating }}/5</p>
                    {% if ticket.satisfaction_comment %}
                    <p class="small">{{ ticket.satisfaction_comment }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ super() }}
<script>
function escalateTicket() {
    $('#escalationModal').modal('show');
}

function askAI() {
    const content = $('#content').val();
    if (!content.trim()) {
        alert('Veuillez d\'abord taper votre question.');
        return;
    }
    
    // Simuler une demande à l'IA
    alert('Fonctionnalité en cours de développement. L\'IA analysera votre message et proposera une réponse.');
}

function closeTicket() {
    if (confirm('Êtes-vous sûr de vouloir fermer ce ticket ?')) {
        // Ici, envoyer une requête pour fermer le ticket
        alert('Fonctionnalité à implémenter');
    }
}

function reopenTicket() {
    if (confirm('Êtes-vous sûr de vouloir rouvrir ce ticket ?')) {
        // Ici, envoyer une requête pour rouvrir le ticket
        alert('Fonctionnalité à implémenter');
    }
}

// Auto-scroll vers le dernier message
$(document).ready(function() {
    const messagesContainer = $('.messages-container');
    if (messagesContainer.length) {
        messagesContainer.scrollTop(messagesContainer[0].scrollHeight);
    }
});
</script>
{% endblock %}
