{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">{{ title }}</h2>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.csrf_token }}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.name.label(class="form-label") }}
                                    {{ form.name(class="form-control") }}
                                    {% if form.name.errors %}
                                        {% for error in form.name.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.promotion_type.label(class="form-label") }}
                                    {{ form.promotion_type(class="form-select") }}
                                    {% if form.promotion_type.errors %}
                                        {% for error in form.promotion_type.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control", rows=3) }}
                            {% if form.description.errors %}
                                {% for error in form.description.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                {% endfor %}
                            {% endif %}
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.value.label(class="form-label") }}
                                    {{ form.value(class="form-control") }}
                                    {% if form.value.errors %}
                                        {% for error in form.value.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 buy-x-get-y-fields" style="display: none;">
                                <div class="form-group">
                                    {{ form.buy_x.label(class="form-label") }}
                                    {{ form.buy_x(class="form-control") }}
                                    {% if form.buy_x.errors %}
                                        {% for error in form.buy_x.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4 buy-x-get-y-fields" style="display: none;">
                                <div class="form-group">
                                    {{ form.get_y.label(class="form-label") }}
                                    {{ form.get_y(class="form-control") }}
                                    {% if form.get_y.errors %}
                                        {% for error in form.get_y.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.start_date.label(class="form-label") }}
                                    {{ form.start_date(class="form-control", type="datetime-local") }}
                                    {% if form.start_date.errors %}
                                        {% for error in form.start_date.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.end_date.label(class="form-label") }}
                                    {{ form.end_date(class="form-control", type="datetime-local") }}
                                    {% if form.end_date.errors %}
                                        {% for error in form.end_date.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.min_purchase.label(class="form-label") }}
                                    {{ form.min_purchase(class="form-control") }}
                                    {% if form.min_purchase.errors %}
                                        {% for error in form.min_purchase.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.max_discount.label(class="form-label") }}
                                    {{ form.max_discount(class="form-control") }}
                                    {% if form.max_discount.errors %}
                                        {% for error in form.max_discount.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.usage_limit.label(class="form-label") }}
                                    {{ form.usage_limit(class="form-control") }}
                                    {% if form.usage_limit.errors %}
                                        {% for error in form.usage_limit.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    {{ form.is_active(class="form-check-input") }}
                                    {{ form.is_active.label(class="form-check-label") }}
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            {{ form.products.label(class="form-label") }}
                            {{ form.products(class="form-select", multiple=true, size=5) }}
                            {% if form.products.errors %}
                                {% for error in form.products.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                {% endfor %}
                            {% endif %}
                            <small class="form-text text-muted">
                                Maintenez Ctrl (Windows) ou Cmd (Mac) pour sélectionner plusieurs produits
                            </small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('promotions.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const promotionTypeSelect = document.getElementById('promotion_type');
    const buyXGetYFields = document.querySelectorAll('.buy-x-get-y-fields');
    
    function toggleBuyXGetYFields() {
        const isBuyXGetY = promotionTypeSelect.value === 'buy_x_get_y';
        buyXGetYFields.forEach(field => {
            field.style.display = isBuyXGetY ? 'block' : 'none';
        });
    }
    
    promotionTypeSelect.addEventListener('change', toggleBuyXGetYFields);
    toggleBuyXGetYFields();
});
</script>
{% endblock %} 