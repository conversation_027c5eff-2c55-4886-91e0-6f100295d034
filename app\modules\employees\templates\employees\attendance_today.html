{% extends "employees/base_hr.html" %}

{% block title %}Présences du Jour{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-calendar-day me-2"></i>Présences du Jour
        <small class="text-muted">{{ today.strftime('%d/%m/%Y') }}</small>
    </h1>
    <a href="{{ url_for('employees.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>Retour
    </a>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ attendances|selectattr('status', 'equalto', 'PRESENT')|list|length }}</h4>
                        <p class="mb-0">Présents</p>
                    </div>
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ attendances|selectattr('status', 'equalto', 'ABSENT')|list|length }}</h4>
                        <p class="mb-0">Absents</p>
                    </div>
                    <i class="fas fa-times-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ attendances|selectattr('status', 'equalto', 'LATE')|list|length }}</h4>
                        <p class="mb-0">En retard</p>
                    </div>
                    <i class="fas fa-clock fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ attendances|length }}</h4>
                        <p class="mb-0">Total</p>
                    </div>
                    <i class="fas fa-users fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>Liste des Présences
        </h5>
    </div>
    <div class="card-body p-0">
        {% if attendances %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Employé</th>
                        <th>Statut</th>
                        <th>Arrivée</th>
                        <th>Départ</th>
                        <th>Heures travaillées</th>
                        <th>Notes</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for attendance in attendances %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                    {{ attendance.employee.first_name[0] }}{{ attendance.employee.last_name[0] }}
                                </div>
                                <div>
                                    <strong>{{ attendance.employee.full_name }}</strong><br>
                                    <small class="text-muted">{{ attendance.employee.position }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if attendance.status == 'PRESENT' %}
                                <span class="badge bg-success">Présent</span>
                            {% elif attendance.status == 'ABSENT' %}
                                <span class="badge bg-danger">Absent</span>
                            {% elif attendance.status == 'LATE' %}
                                <span class="badge bg-warning">En retard</span>
                            {% elif attendance.status == 'SICK_LEAVE' %}
                                <span class="badge bg-info">Congé maladie</span>
                            {% elif attendance.status == 'VACATION' %}
                                <span class="badge bg-primary">Congé</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance.clock_in_time %}
                                {{ attendance.clock_in_time.strftime('%H:%M') }}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance.clock_out_time %}
                                {{ attendance.clock_out_time.strftime('%H:%M') }}
                            {% else %}
                                <span class="text-muted">En cours</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance.hours_worked %}
                                <strong>{{ "%.2f"|format(attendance.hours_worked) }}h</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance.notes %}
                                <span class="text-truncate" style="max-width: 150px;" title="{{ attendance.notes }}">
                                    {{ attendance.notes }}
                                </span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <a href="{{ url_for('employees.edit_attendance', attendance_id=attendance.id) }}" 
                               class="btn btn-sm btn-outline-warning" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-day fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucune présence enregistrée aujourd'hui</h5>
            <p class="text-muted">Les présences apparaîtront ici une fois enregistrées.</p>
        </div>
        {% endif %}
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
    font-weight: bold;
}
</style>
{% endblock %}
