{% extends "employees/base_hr.html" %}

{% block title %}{{ title }}{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-star me-2"></i>{{ title }}
        <small class="text-muted">{{ employee.full_name }}</small>
    </h1>
    <a href="{{ url_for('employees.detail', id=employee.id) }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>Retour
    </a>
</div>

<div class="row">
    <div class="col-lg-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clipboard-check me-2"></i>Évaluation de Performance
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <!-- Informations générales -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Informations Générales</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    {{ form.evaluation_date.label(class="form-label") }}
                                    {{ form.evaluation_date(class="form-control" + (" is-invalid" if form.evaluation_date.errors else "")) }}
                                    {% if form.evaluation_date.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.evaluation_date.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    {{ form.evaluation_period_start.label(class="form-label") }}
                                    {{ form.evaluation_period_start(class="form-control" + (" is-invalid" if form.evaluation_period_start.errors else "")) }}
                                    {% if form.evaluation_period_start.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.evaluation_period_start.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    {{ form.evaluation_period_end.label(class="form-label") }}
                                    {{ form.evaluation_period_end(class="form-control" + (" is-invalid" if form.evaluation_period_end.errors else "")) }}
                                    {% if form.evaluation_period_end.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.evaluation_period_end.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Critères d'évaluation -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Critères d'Évaluation (1-5)</h6>
                            <small class="text-muted">1 = Insuffisant, 2 = Acceptable, 3 = Bon, 4 = Très bon, 5 = Excellent</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.punctuality.label(class="form-label") }}
                                    <div class="d-flex align-items-center">
                                        {{ form.punctuality(class="form-range me-3" + (" is-invalid" if form.punctuality.errors else ""), min="1", max="5", step="1") }}
                                        <span id="punctuality-value" class="badge bg-primary">3</span>
                                    </div>
                                    {% if form.punctuality.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.punctuality.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    {{ form.quality_of_work.label(class="form-label") }}
                                    <div class="d-flex align-items-center">
                                        {{ form.quality_of_work(class="form-range me-3" + (" is-invalid" if form.quality_of_work.errors else ""), min="1", max="5", step="1") }}
                                        <span id="quality_of_work-value" class="badge bg-primary">3</span>
                                    </div>
                                    {% if form.quality_of_work.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.quality_of_work.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.teamwork.label(class="form-label") }}
                                    <div class="d-flex align-items-center">
                                        {{ form.teamwork(class="form-range me-3" + (" is-invalid" if form.teamwork.errors else ""), min="1", max="5", step="1") }}
                                        <span id="teamwork-value" class="badge bg-primary">3</span>
                                    </div>
                                    {% if form.teamwork.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.teamwork.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    {{ form.communication.label(class="form-label") }}
                                    <div class="d-flex align-items-center">
                                        {{ form.communication(class="form-range me-3" + (" is-invalid" if form.communication.errors else ""), min="1", max="5", step="1") }}
                                        <span id="communication-value" class="badge bg-primary">3</span>
                                    </div>
                                    {% if form.communication.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.communication.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.initiative.label(class="form-label") }}
                                    <div class="d-flex align-items-center">
                                        {{ form.initiative(class="form-range me-3" + (" is-invalid" if form.initiative.errors else ""), min="1", max="5", step="1") }}
                                        <span id="initiative-value" class="badge bg-primary">3</span>
                                    </div>
                                    {% if form.initiative.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.initiative.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    {{ form.customer_service.label(class="form-label") }}
                                    <div class="d-flex align-items-center">
                                        {{ form.customer_service(class="form-range me-3" + (" is-invalid" if form.customer_service.errors else ""), min="1", max="5", step="1") }}
                                        <span id="customer_service-value" class="badge bg-primary">3</span>
                                    </div>
                                    {% if form.customer_service.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.customer_service.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.overall_score.label(class="form-label") }}
                                    <div class="d-flex align-items-center">
                                        {{ form.overall_score(class="form-range me-3" + (" is-invalid" if form.overall_score.errors else ""), min="1", max="5", step="0.1", readonly=true) }}
                                        <span id="overall_score-value" class="badge bg-success fs-6">3.0</span>
                                    </div>
                                    <small class="form-text text-muted">Calculé automatiquement</small>
                                    {% if form.overall_score.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.overall_score.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Commentaires -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Commentaires et Observations</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                {{ form.strengths.label(class="form-label") }}
                                {{ form.strengths(class="form-control" + (" is-invalid" if form.strengths.errors else ""), rows="3") }}
                                {% if form.strengths.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.strengths.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                {{ form.areas_for_improvement.label(class="form-label") }}
                                {{ form.areas_for_improvement(class="form-control" + (" is-invalid" if form.areas_for_improvement.errors else ""), rows="3") }}
                                {% if form.areas_for_improvement.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.areas_for_improvement.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                {{ form.goals.label(class="form-label") }}
                                {{ form.goals(class="form-control" + (" is-invalid" if form.goals.errors else ""), rows="3") }}
                                {% if form.goals.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.goals.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                {{ form.comments.label(class="form-label") }}
                                {{ form.comments(class="form-control" + (" is-invalid" if form.comments.errors else ""), rows="4") }}
                                {% if form.comments.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.comments.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Statut -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Statut de l'Évaluation</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        {{ form.is_finalized(class="form-check-input" + (" is-invalid" if form.is_finalized.errors else "")) }}
                                        {{ form.is_finalized.label(class="form-check-label") }}
                                        {% if form.is_finalized.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.is_finalized.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        {{ form.employee_acknowledged(class="form-check-input" + (" is-invalid" if form.employee_acknowledged.errors else "")) }}
                                        {{ form.employee_acknowledged.label(class="form-check-label") }}
                                        {% if form.employee_acknowledged.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.employee_acknowledged.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('employees.detail', id=employee.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mettre à jour les valeurs affichées pour les sliders
    const sliders = ['punctuality', 'quality_of_work', 'teamwork', 'communication', 'initiative', 'customer_service'];
    
    sliders.forEach(function(sliderId) {
        const slider = document.getElementById(sliderId);
        const valueSpan = document.getElementById(sliderId + '-value');
        
        if (slider && valueSpan) {
            // Mettre à jour la valeur affichée
            function updateValue() {
                valueSpan.textContent = slider.value;
                calculateOverallScore();
            }
            
            slider.addEventListener('input', updateValue);
            updateValue(); // Initialiser
        }
    });
    
    // Calculer le score global
    function calculateOverallScore() {
        const scores = sliders.map(function(sliderId) {
            const slider = document.getElementById(sliderId);
            return slider ? parseFloat(slider.value) : 0;
        });
        
        const average = scores.reduce((a, b) => a + b, 0) / scores.length;
        const overallSlider = document.getElementById('overall_score');
        const overallValue = document.getElementById('overall_score-value');
        
        if (overallSlider && overallValue) {
            overallSlider.value = average.toFixed(1);
            overallValue.textContent = average.toFixed(1);
            
            // Changer la couleur selon le score
            overallValue.className = 'badge fs-6 ' + getScoreColor(average);
        }
    }
    
    function getScoreColor(score) {
        if (score >= 4.5) return 'bg-success';
        if (score >= 3.5) return 'bg-primary';
        if (score >= 2.5) return 'bg-warning';
        return 'bg-danger';
    }
    
    // Calculer au chargement
    calculateOverallScore();
});
</script>
{% endblock %}
