// Cette fonction n'est plus nécessaire car elle est exposée globalement dans pos.js
// function processPayment(method) {
//     if (window.POS && typeof window.POS.processPayment === 'function') {
//         window.POS.processPayment(method);
//     } else {
//         console.error('POS.processPayment is not defined');
//     }
// }

// Initialiser les événements quand le document est chargé
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM chargé, initialisation des gestionnaires d'événements...");
    
    // Attacher les événements aux boutons de paiement dans la modale
    document.querySelectorAll('.payment-method').forEach(btn => {
        btn.addEventListener('click', function() {
            // Convertir la méthode en majuscules pour correspondre à l'énumération PaymentMethod
            const method = this.dataset.method.toUpperCase();
            console.log(`Méthode de paiement sélectionnée: ${method}`);
            if (typeof window.processPayment === 'function') {
                window.processPayment(method);
            } else {
                console.error('window.processPayment is not defined');
            }
        });
    });
    
    // Attacher l'événement au bouton principal de paiement en utilisant son ID
    const mainPaymentBtn = document.getElementById('mainPaymentButton');
    if (mainPaymentBtn) {
        mainPaymentBtn.addEventListener('click', function() {
            console.log('Bouton de paiement principal cliqué');
            if (typeof window.processPayment === 'function') {
                window.processPayment(); // Appel sans méthode pour ouvrir la modale
            } else {
                console.error('window.processPayment is not defined');
            }
        });
    } else {
        console.error('Bouton de paiement principal non trouvé');
    }
    
    console.log('Payment event handlers initialized');
});