{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Détails du produit</h1>
        <a href="{{ url_for('reports.products') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour
        </a>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informations générales</h6>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th style="width: 150px">Nom</th>
                            <td>{{ product.name }}</td>
                        </tr>
                        <tr>
                            <th>Catégorie</th>
                            <td>{{ product.category.name if product.category else "Sans catégorie" }}</td>
                        </tr>
                        <tr>
                            <th>Prix</th>
                            <td>{{ product.price|format_currency }}</td>
                        </tr>
                        <tr>
                            <th>Description</th>
                            <td>{{ product.description or "Aucune description" }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Stock</h6>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th style="width: 150px">Stock actuel</th>
                            <td>{{ product.get_available_quantity() }}</td>
                        </tr>
                        <tr>
                            <th>Stock minimum</th>
                            <td>{{ product.minimum_stock }}</td>
                        </tr>
                        <tr>
                            <th>Statut</th>
                            <td>
                                {% if product.get_available_quantity() == 0 %}
                                <span class="badge bg-danger">Rupture</span>
                                {% elif product.get_available_quantity() <= product.minimum_stock %}
                                <span class="badge bg-warning">Stock faible</span>
                                {% else %}
                                <span class="badge bg-success">En stock</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-12">
            <a href="{{ url_for('reports.edit_product', id=product.id) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Modifier
            </a>
            <a href="{{ url_for('reports.delete_product', id=product.id) }}" class="btn btn-danger">
                <i class="fas fa-trash"></i> Supprimer
            </a>
        </div>
    </div>
</div>
{% endblock %}