{% extends "base.html" %}

{% block title %}Virement Bancaire{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-2">
                <h4 class="mb-0">
                    <i class="fas fa-exchange-alt"></i> Virement Bancaire
                </h4>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.bank_management_index') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-exchange-alt"></i> Nouveau Virement</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="transferForm">
                            {{ form.hidden_tag() }}
                            
                            <!-- Compte source -->
                            <div class="mb-3">
                                {{ form.source_account_id.label(class="form-label") }}
                                {{ form.source_account_id(class="form-select", id="sourceAccountSelect") }}
                                {% if form.source_account_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.source_account_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div id="sourceBalanceInfo" class="mt-2" style="display: none;">
                                    <small class="text-muted">Solde disponible: <span id="sourceBalance">0.00</span> €</small>
                                </div>
                            </div>

                            <!-- Compte destination -->
                            <div class="mb-3">
                                {{ form.target_account_id.label(class="form-label") }}
                                {{ form.target_account_id(class="form-select", id="targetAccountSelect") }}
                                {% if form.target_account_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.target_account_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div id="targetBalanceInfo" class="mt-2" style="display: none;">
                                    <small class="text-muted">Solde actuel: <span id="targetBalance">0.00</span> €</small>
                                </div>
                            </div>

                            <!-- Montant -->
                            <div class="mb-3">
                                {{ form.amount.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.amount(class="form-control", step="0.01", min="0.01", id="amountInput") }}
                                    <span class="input-group-text">€</span>
                                </div>
                                {% if form.amount.errors %}
                                    <div class="text-danger">
                                        {% for error in form.amount.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div id="amountWarning" class="mt-2" style="display: none;">
                                    <small class="text-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Montant supérieur au solde disponible
                                    </small>
                                </div>
                            </div>

                            <!-- Description -->
                            <div class="mb-3">
                                {{ form.description.label(class="form-label") }}
                                {{ form.description(class="form-control") }}
                                {% if form.description.errors %}
                                    <div class="text-danger">
                                        {% for error in form.description.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Référence -->
                            <div class="mb-3">
                                {{ form.reference.label(class="form-label") }}
                                {{ form.reference(class="form-control", placeholder="Numéro de virement, référence...") }}
                                {% if form.reference.errors %}
                                    <div class="text-danger">
                                        {% for error in form.reference.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Notes -->
                            <div class="mb-3">
                                {{ form.notes.label(class="form-label") }}
                                {{ form.notes(class="form-control", rows="3") }}
                                {% if form.notes.errors %}
                                    <div class="text-danger">
                                        {% for error in form.notes.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Aperçu du virement -->
                            <div class="alert alert-info" id="transferPreview" style="display: none;">
                                <h6><i class="fas fa-info-circle"></i> Aperçu du Virement</h6>
                                <div id="previewContent"></div>
                            </div>

                            <!-- Boutons -->
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('inventory.bank_management_index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-success" id="submitBtn" disabled>
                                    <i class="fas fa-exchange-alt"></i> Effectuer le virement
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Informations complémentaires -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-info-circle"></i> Informations</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6>Virement entre comptes</h6>
                            <p class="small mb-2">
                                Cette opération transfère de l'argent d'un compte bancaire vers un autre.
                            </p>
                            <ul class="small mb-0">
                                <li>Le montant sera débité du compte source</li>
                                <li>Le montant sera crédité sur le compte destination</li>
                                <li>Les deux opérations sont liées</li>
                                <li>L'opération est immédiate</li>
                            </ul>
                        </div>

                        <div class="mt-3">
                            <h6>Comptes disponibles</h6>
                            {% if bank_accounts %}
                                {% for account in bank_accounts %}
                                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                    <span>{{ account.name }}</span>
                                    <span class="text-{{ 'success' if account.balance >= 0 else 'danger' }}">
                                        {{ "%.2f"|format(account.balance) }} €
                                    </span>
                                </div>
                                {% endfor %}
                            {% else %}
                                <p class="text-muted small">Aucun compte bancaire</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Virements récents -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-history"></i> Virements Récents</h6>
                    </div>
                    <div class="card-body">
                        <div id="recentTransfers">
                            <p class="text-muted small">Chargement...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sourceAccountSelect = document.getElementById('sourceAccountSelect');
    const targetAccountSelect = document.getElementById('targetAccountSelect');
    const amountInput = document.getElementById('amountInput');
    const submitBtn = document.getElementById('submitBtn');
    const transferPreview = document.getElementById('transferPreview');
    const previewContent = document.getElementById('previewContent');
    const sourceBalanceInfo = document.getElementById('sourceBalanceInfo');
    const targetBalanceInfo = document.getElementById('targetBalanceInfo');
    const sourceBalance = document.getElementById('sourceBalance');
    const targetBalance = document.getElementById('targetBalance');
    const amountWarning = document.getElementById('amountWarning');

    // Données des comptes (passées depuis le template)
    const bankAccounts = {{ bank_accounts_json | safe }};

    // Mettre à jour les informations de solde
    function updateBalanceInfo() {
        const selectedSource = sourceAccountSelect.value;
        const selectedTarget = targetAccountSelect.value;

        if (selectedSource && selectedSource !== '0') {
            const sourceData = bankAccounts.find(a => a.id == selectedSource);
            if (sourceData) {
                sourceBalance.textContent = sourceData.balance.toFixed(2);
                sourceBalanceInfo.style.display = 'block';
            }
        } else {
            sourceBalanceInfo.style.display = 'none';
        }

        if (selectedTarget && selectedTarget !== '0') {
            const targetData = bankAccounts.find(a => a.id == selectedTarget);
            if (targetData) {
                targetBalance.textContent = targetData.balance.toFixed(2);
                targetBalanceInfo.style.display = 'block';
            }
        } else {
            targetBalanceInfo.style.display = 'none';
        }

        updatePreview();
    }

    // Mettre à jour l'aperçu
    function updatePreview() {
        const selectedSource = sourceAccountSelect.value;
        const selectedTarget = targetAccountSelect.value;
        const amount = parseFloat(amountInput.value) || 0;

        if (selectedSource && selectedSource !== '0' && selectedTarget && selectedTarget !== '0' && amount > 0) {
            // Vérifier que les comptes sont différents
            if (selectedSource === selectedTarget) {
                transferPreview.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Vous ne pouvez pas effectuer un virement vers le même compte
                    </div>
                `;
                transferPreview.style.display = 'block';
                submitBtn.disabled = true;
                return;
            }

            const sourceData = bankAccounts.find(a => a.id == selectedSource);
            const targetData = bankAccounts.find(a => a.id == selectedTarget);

            if (sourceData && targetData) {
                const newSourceBalance = sourceData.balance - amount;
                const newTargetBalance = targetData.balance + amount;

                previewContent.innerHTML = `
                    <div class="row">
                        <div class="col-6">
                            <strong>Compte source:</strong><br>
                            ${sourceData.name}<br>
                            <span class="text-danger">-${amount.toFixed(2)} €</span><br>
                            <small>Nouveau solde: ${newSourceBalance.toFixed(2)} €</small>
                        </div>
                        <div class="col-6">
                            <strong>Compte destination:</strong><br>
                            ${targetData.name}<br>
                            <span class="text-success">+${amount.toFixed(2)} €</span><br>
                            <small>Nouveau solde: ${newTargetBalance.toFixed(2)} €</small>
                        </div>
                    </div>
                `;

                transferPreview.className = 'alert alert-info';
                transferPreview.style.display = 'block';

                // Vérifier si le montant est disponible
                if (amount > sourceData.balance) {
                    amountWarning.style.display = 'block';
                    submitBtn.disabled = true;
                } else {
                    amountWarning.style.display = 'none';
                    submitBtn.disabled = false;
                }
            }
        } else {
            transferPreview.style.display = 'none';
            submitBtn.disabled = true;
        }
    }

    // Événements
    sourceAccountSelect.addEventListener('change', updateBalanceInfo);
    targetAccountSelect.addEventListener('change', updateBalanceInfo);
    amountInput.addEventListener('input', updatePreview);

    // Validation du formulaire
    document.getElementById('transferForm').addEventListener('submit', function(e) {
        const amount = parseFloat(amountInput.value) || 0;
        const selectedSource = sourceAccountSelect.value;
        const selectedTarget = targetAccountSelect.value;

        if (selectedSource === selectedTarget) {
            e.preventDefault();
            alert('Vous ne pouvez pas effectuer un virement vers le même compte');
            return;
        }

        if (selectedSource && selectedSource !== '0') {
            const sourceData = bankAccounts.find(a => a.id == selectedSource);
            if (sourceData && amount > sourceData.balance) {
                e.preventDefault();
                alert('Le montant dépasse le solde disponible');
                return;
            }
        }

        if (amount > 1000) {
            if (!confirm(`Confirmer le virement de ${amount.toFixed(2)} € ?`)) {
                e.preventDefault();
                return;
            }
        }
    });

    // Charger les virements récents
    loadRecentTransfers();

    function loadRecentTransfers() {
        fetch('/inventory/api/recent-transfers')
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('recentTransfers');
                if (data.success && data.transfers.length > 0) {
                    container.innerHTML = data.transfers.map(transfer => `
                        <div class="border-bottom py-2">
                            <div class="d-flex justify-content-between">
                                <small><strong>${transfer.description}</strong></small>
                                <small class="text-muted">${transfer.date}</small>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">${transfer.source} → ${transfer.target}</small>
                                <small class="text-primary">${transfer.amount.toFixed(2)} €</small>
                            </div>
                        </div>
                    `).join('');
                } else {
                    container.innerHTML = '<p class="text-muted small">Aucun virement récent</p>';
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                document.getElementById('recentTransfers').innerHTML = '<p class="text-danger small">Erreur lors du chargement</p>';
            });
    }

    // Initialisation
    updateBalanceInfo();
});
</script>
{% endblock %}
