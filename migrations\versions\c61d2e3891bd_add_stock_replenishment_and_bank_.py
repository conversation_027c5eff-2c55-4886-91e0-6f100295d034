"""Add stock replenishment and bank management system

Revision ID: c61d2e3891bd
Revises: ff5e341febfe
Create Date: 2025-08-12 09:40:27.044103

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c61d2e3891bd'
down_revision = 'ff5e341febfe'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('cash_operations', schema=None) as batch_op:
        batch_op.alter_column('type',
               existing_type=sa.VARCHAR(length=12),
               type_=sa.Enum('OPENING', 'CLOSING', 'CASH_IN', 'CASH_OUT', 'SALE', 'BANK_DEPOSIT', 'SUPPLIER_PAYMENT', name='cashregisteroperationtype'),
               existing_nullable=False)

    with op.batch_alter_table('suppliers', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'supplier_categories', ['category_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('suppliers', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('cash_operations', schema=None) as batch_op:
        batch_op.alter_column('type',
               existing_type=sa.Enum('OPENING', 'CLOSING', 'CASH_IN', 'CASH_OUT', 'SALE', 'BANK_DEPOSIT', 'SUPPLIER_PAYMENT', name='cashregisteroperationtype'),
               type_=sa.VARCHAR(length=12),
               existing_nullable=False)

    # ### end Alembic commands ###
