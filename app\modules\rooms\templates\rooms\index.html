{% extends "base.html" %}

{% block title %}Gestion des Salles{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-door-open"></i> Gestion des Salles</h2>
        <a href="{{ url_for('rooms.new') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Nouvelle Salle
        </a>
    </div>

    {% if rooms %}
    <div class="row">
        {% for room in rooms %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100 {% if room.is_default %}border-primary{% endif %}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        {{ room.name }}
                        {% if room.is_default %}
                        <span class="badge bg-primary ms-2">Par défaut</span>
                        {% endif %}
                    </h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('rooms.show', id=room.id) }}">
                                <i class="fas fa-eye"></i> Voir le plan
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('rooms.edit', id=room.id) }}">
                                <i class="fas fa-edit"></i> Modifier
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ url_for('rooms.delete', id=room.id) }}" 
                                      onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette salle ?')">
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-trash"></i> Supprimer
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    {% if room.description %}
                    <p class="card-text text-muted">{{ room.description }}</p>
                    {% endif %}
                    
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="border rounded p-2">
                                <div class="h5 mb-0 text-primary">{{ room.table_count }}</div>
                                <small class="text-muted">Tables</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border rounded p-2">
                                <div class="h5 mb-0 text-success">{{ room.available_tables_count }}</div>
                                <small class="text-muted">Libres</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border rounded p-2">
                                <div class="h5 mb-0 text-warning">{{ room.occupied_tables_count }}</div>
                                <small class="text-muted">Occupées</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-ruler-combined"></i> {{ room.width }}x{{ room.height }}px
                        </small>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('rooms.show', id=room.id) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-map"></i> Plan de salle
                        </a>
                        <a href="{{ url_for('tables.index') }}?room_id={{ room.id }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-table"></i> Gérer tables
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-door-open fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">Aucune salle configurée</h4>
        <p class="text-muted">Créez votre première salle pour organiser vos tables.</p>
        <a href="{{ url_for('rooms.new') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Créer une salle
        </a>
    </div>
    {% endif %}
</div>

<style>
.card {
    transition: transform 0.2s;
}
.card:hover {
    transform: translateY(-2px);
}
.border-primary {
    border-width: 2px !important;
}
</style>
{% endblock %}
