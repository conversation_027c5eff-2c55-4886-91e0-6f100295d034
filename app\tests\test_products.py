import pytest
from app.modules.auth.models import User
from app.modules.inventory.models_product import Product, ProductCategory
from app.extensions import db

def test_create_product(app):
    """
    GIVEN a Product model
    WHEN a new Product is created
    THEN check the name, price, and stock fields are defined correctly
    """
    with app.app_context():
        # Create required relationships first
        owner = User(username='test_user', email='<EMAIL>')
        owner.set_password('test123')
        db.session.add(owner)
        
        category = ProductCategory(name='Test Category', owner=owner)
        db.session.add(category)
        db.session.commit()

        product = Product(
            name='Test Product',
            description='A test product',
            price=10.99,
            stock_quantity=100,
            category=category,
            owner=owner
        )
        db.session.add(product)
        db.session.commit()

        assert product.name == 'Test Product'
        assert product.price == 10.99
        assert product.stock_quantity == 100
        assert product.category == category
        assert product.owner == owner

def test_update_product(app):
    """
    GIVEN an existing Product
    WHEN the product is updated
    THEN check the changes are saved correctly
    """
    with app.app_context():
        # Create required relationships first
        owner = User(username='test_user2', email='<EMAIL>')
        owner.set_password('test123')
        db.session.add(owner)
        
        category = ProductCategory(name='Test Category 2', owner=owner)
        db.session.add(category)
        db.session.commit()

        product = Product(
            name='Original Name',
            description='Original description',
            price=10.99,
            stock_quantity=100,
            category=category,
            owner=owner
        )
        db.session.add(product)
        db.session.commit()

        # Update the product
        product.name = 'Updated Name'
        product.price = 15.99
        db.session.commit()

        assert product.name == 'Updated Name'
        assert product.price == 15.99

def test_delete_product(app):
    """
    GIVEN an existing Product
    WHEN the product is deleted
    THEN check it no longer exists in the database
    """
    with app.app_context():
        # Create required relationships first
        owner = User(username='test_user3', email='<EMAIL>')
        owner.set_password('test123')
        db.session.add(owner)
        
        category = ProductCategory(name='Test Category 3', owner=owner)
        db.session.add(category)
        db.session.commit()

        product = Product(
            name='To Delete',
            description='This will be deleted',
            price=10.99,
            stock_quantity=100,
            category=category,
            owner=owner
        )
        db.session.add(product)
        db.session.commit()

        product_id = product.id
        db.session.delete(product)
        db.session.commit()

        assert Product.query.get(product_id) is None

def test_product_category_relationship(app):
    """
    GIVEN Products and Categories
    WHEN they are associated
    THEN check the relationship works correctly
    """
    with app.app_context():
        owner = User(username='test_user4', email='<EMAIL>')
        owner.set_password('test123')
        db.session.add(owner)
        
        category = ProductCategory(name='Test Category 4', owner=owner)
        db.session.add(category)
        db.session.commit()

        product = Product(
            name='Categorized Product',
            description='A product in a category',
            price=10.99,
            stock_quantity=100,
            category=category,
            owner=owner
        )
        db.session.add(product)
        db.session.commit()

        assert product in category.products
        assert product.category == category

def test_product_stock_management(app):
    """
    GIVEN a Product
    WHEN stock is adjusted
    THEN check the stock level is updated correctly
    """
    with app.app_context():
        owner = User(username='test_user5', email='<EMAIL>')
        owner.set_password('test123')
        db.session.add(owner)
        
        category = ProductCategory(name='Test Category 5', owner=owner)
        db.session.add(category)
        db.session.commit()

        product = Product(
            name='Stock Test',
            description='Testing stock management',
            price=10.99,
            stock_quantity=100,
            category=category,
            owner=owner
        )
        db.session.add(product)
        db.session.commit()

        # Test stock adjustments
        product.stock_quantity += 50
        db.session.commit()
        assert product.stock_quantity == 150

        product.stock_quantity -= 30
        db.session.commit()
        assert product.stock_quantity == 120

def test_product_price_validation(app):
    """
    GIVEN a Product
    WHEN created with invalid price
    THEN check it raises an error
    """
    with app.app_context():
        owner = User(username='test_user6', email='<EMAIL>')
        owner.set_password('test123')
        db.session.add(owner)
        
        category = ProductCategory(name='Test Category 6', owner=owner)
        db.session.add(category)
        db.session.commit()

        # Test negative price
        try:
            product = Product(
                name='Invalid Price',
                description='Testing price validation',
                price=-10.99,
                stock_quantity=100,
                category=category,
                owner=owner
            )
            db.session.add(product)
            db.session.commit()
            assert False, "Should have raised an error for negative price"
        except:
            db.session.rollback()
            assert True 