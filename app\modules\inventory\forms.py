from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON><PERSON>ield, FileAllowed
from wtforms import <PERSON><PERSON>ield, TextAreaField, FloatField, BooleanField, SelectField, SubmitField, IntegerField
from wtforms.validators import DataRequired, Length, Optional, NumberRange

class ProductCategoryForm(FlaskForm):
    name = StringField('Nom de la catégorie', validators=[
        DataRequired(),
        Length(min=2, max=64)
    ])
    description = TextAreaField('Description')
    color = StringField('Couleur', default='#6c757d')
    image = FileField('Image', validators=[
        FileAllowed(['jpg', 'png', 'jpeg', 'gif'], 'Images uniquement!')
    ])
    submit = SubmitField('Enregistrer')

class ProductForm(FlaskForm):
    name = StringField('Nom', validators=[DataRequired(message="Le nom est requis")])
    description = TextAreaField('Description')
    category_id = SelectField('Catégorie', coerce=int, 
                            validators=[DataRequired(message="La catégorie est requise")])
    price = FloatField('Prix de vente', 
                      validators=[DataRequired(message="Le prix est requis"),
                                NumberRange(min=0, message="Le prix doit être positif")])
    cost_price = FloatField('Prix de revient', 
                          validators=[Optional(),
                                    NumberRange(min=0, message="Le prix de revient doit être positif")])
    stock_quantity = FloatField('Quantité en stock',
                              validators=[Optional(),
                                        NumberRange(min=0, message="La quantité doit être positive")])
    minimum_stock = FloatField('Stock minimum',
                             validators=[Optional(),
                                       NumberRange(min=0, message="Le stock minimum doit être positif")])
    unit = StringField('Unité', default='unité')
    has_recipe = BooleanField('Basé sur une recette')
    image = FileField('Image', validators=[
        FileAllowed(['jpg', 'png', 'jpeg', 'gif'], 'Images uniquement!')
    ])
    submit = SubmitField('Enregistrer')
    
    def validate(self, extra_validators=None):
        if not super().validate(extra_validators=extra_validators):
            return False
            
        if self.has_recipe.data:
            # Si c'est un produit basé sur une recette, on met la quantité en stock à 0
            self.stock_quantity.data = 0
        else:
            # Si ce n'est pas un produit basé sur une recette, on doit avoir une quantité en stock
            if self.stock_quantity.data is None:
                self.stock_quantity.errors.append(
                    'La quantité en stock est requise pour un produit non basé sur une recette'
                )
                return False
        
        return True

class RecipeForm(FlaskForm):
    instructions = TextAreaField('Instructions de préparation', validators=[Optional()])
    preparation_time = IntegerField('Temps de préparation (minutes)', validators=[Optional(), NumberRange(min=0)])
    cooking_time = IntegerField('Temps de cuisson (minutes)', validators=[Optional(), NumberRange(min=0)])
    yield_quantity = IntegerField('Nombre de portions', validators=[Optional(), NumberRange(min=1)])
    submit = SubmitField('Enregistrer')

class RecipeItemForm(FlaskForm):
    ingredient_id = SelectField('Ingrédient', coerce=int, validators=[DataRequired()])
    quantity = FloatField('Quantité', validators=[DataRequired(), NumberRange(min=0.01)])
    submit = SubmitField('Ajouter')

class IngredientCategoryForm(FlaskForm):
    name = StringField('Nom', validators=[DataRequired(), Length(min=2, max=64)])
    description = TextAreaField('Description')
    image = FileField('Image', validators=[
        FileAllowed(['jpg', 'png', 'jpeg', 'gif'], 'Images uniquement!')
    ])
    submit = SubmitField('Enregistrer')

class IngredientForm(FlaskForm):
    name = StringField('Nom', validators=[DataRequired(), Length(min=2, max=64)])
    description = TextAreaField('Description')
    category_id = SelectField('Catégorie', coerce=int, validators=[Optional()])
    unit = SelectField('Unité de mesure', choices=[
        ('kg', 'Kilogramme'),
        ('g', 'Gramme'),
        ('l', 'Litre'),
        ('ml', 'Millilitre'),
        ('unité', 'Unité')
    ], validators=[DataRequired()])
    stock_quantity = FloatField('Quantité en stock', validators=[NumberRange(min=0)], default=0)
    minimum_stock = FloatField('Stock minimum', validators=[NumberRange(min=0)], default=0)
    price_per_unit = FloatField('Prix par unité', validators=[NumberRange(min=0)], default=0)
    expiry_date = StringField('Date d\'expiration', validators=[Optional()])
    image = FileField('Image', validators=[FileAllowed(['jpg', 'png', 'jpeg', 'gif'], 'Images uniquement!')])
    submit = SubmitField('Enregistrer')

class IngredientStockForm(FlaskForm):
    quantity = FloatField('Quantité', validators=[DataRequired(), NumberRange(min=0.01)])
    operation = SelectField('Opération', choices=[
        ('add', 'Ajouter au stock'),
        ('subtract', 'Retirer du stock'),
        ('set', 'Définir le stock')
    ], validators=[DataRequired()])
    submit = SubmitField('Mettre à jour le stock')

class SupplierCategoryForm(FlaskForm):
    name = StringField('Nom de la catégorie', validators=[
        DataRequired(),
        Length(min=2, max=64)
    ])
    description = TextAreaField('Description')
    color = StringField('Couleur', default='#6c757d')
    icon = StringField('Icône', default='fas fa-truck')
    submit = SubmitField('Enregistrer')

class SupplierForm(FlaskForm):
    name = StringField('Nom', validators=[DataRequired(), Length(min=2, max=100)])
    category_id = SelectField('Catégorie', coerce=int, validators=[Optional()])
    contact_name = StringField('Nom du contact', validators=[Optional(), Length(max=100)])
    email = StringField('Email', validators=[Optional(), Length(max=100)])
    phone = StringField('Téléphone', validators=[Optional(), Length(max=20)])
    website = StringField('Site web', validators=[Optional(), Length(max=200)])
    tax_id = StringField('N° TVA/SIRET', validators=[Optional(), Length(max=50)])
    payment_terms = StringField('Conditions de paiement', validators=[Optional(), Length(max=100)])
    address = TextAreaField('Adresse', validators=[Optional()])
    notes = TextAreaField('Notes', validators=[Optional()])
    rating = SelectField('Note', choices=[
        (0, 'Non noté'),
        (1, '1 étoile'),
        (2, '2 étoiles'),
        (3, '3 étoiles'),
        (4, '4 étoiles'),
        (5, '5 étoiles')
    ], coerce=int, default=0)
    is_active = BooleanField('Actif', default=True)
    submit = SubmitField('Enregistrer')

    def __init__(self, *args, **kwargs):
        super(SupplierForm, self).__init__(*args, **kwargs)
        # Charger les catégories disponibles
        from flask_login import current_user
        if current_user.is_authenticated:
            from app.modules.inventory.models_supplier_category import SupplierCategory
            categories = SupplierCategory.query.filter_by(
                owner_id=current_user.get_owner_id,
                is_active=True
            ).all()
            self.category_id.choices = [(0, 'Sans catégorie')] + [(cat.id, cat.name) for cat in categories]

class SupplierContactForm(FlaskForm):
    contact_type = SelectField('Type de contact', choices=[
        ('email', 'Email'),
        ('phone', 'Téléphone'),
        ('meeting', 'Réunion'),
        ('note', 'Note'),
        ('order', 'Commande'),
        ('complaint', 'Réclamation'),
        ('quote', 'Devis')
    ], validators=[DataRequired()])
    subject = StringField('Sujet', validators=[Optional(), Length(max=200)])
    content = TextAreaField('Contenu', validators=[DataRequired()])
    contact_date = StringField('Date de contact', validators=[Optional()])
    is_important = BooleanField('Important')
    follow_up_date = StringField('Date de suivi', validators=[Optional()])
    status = SelectField('Statut', choices=[
        ('completed', 'Terminé'),
        ('pending', 'En attente'),
        ('cancelled', 'Annulé')
    ], default='completed')
    submit = SubmitField('Enregistrer')