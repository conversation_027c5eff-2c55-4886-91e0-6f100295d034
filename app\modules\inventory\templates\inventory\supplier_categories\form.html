{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">{{ title }}</h2>
                </div>
                <div class="card-body">
                    <form method="POST" novalidate>
                        {{ form.csrf_token }}
                        
                        <div class="mb-3">
                            {{ form.name.label(class="form-label") }}
                            {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                            {% for error in form.name.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows=3) }}
                            {% for error in form.description.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.color.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.color(class="form-control", type="color") }}
                                        <span class="input-group-text">
                                            <i class="fas fa-palette"></i>
                                        </span>
                                    </div>
                                    {% for error in form.color.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.icon.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.icon(class="form-control", placeholder="fas fa-truck") }}
                                        <span class="input-group-text">
                                            <i id="iconPreview" class="{{ form.icon.data or 'fas fa-truck' }}"></i>
                                        </span>
                                    </div>
                                    <small class="form-text text-muted">
                                        Utilisez les classes FontAwesome (ex: fas fa-truck, fas fa-industry)
                                    </small>
                                    {% for error in form.icon.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>

                        <!-- Aperçu de la catégorie -->
                        <div class="mb-4">
                            <label class="form-label">Aperçu</label>
                            <div class="card" style="max-width: 300px;">
                                <div class="card-header" id="previewHeader" style="background-color: {{ form.color.data or '#6c757d' }}; color: white;">
                                    <h6 class="mb-0">
                                        <i id="previewIcon" class="{{ form.icon.data or 'fas fa-truck' }}"></i>
                                        <span id="previewName">{{ form.name.data or 'Nom de la catégorie' }}</span>
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text" id="previewDescription">
                                        {{ form.description.data or 'Description de la catégorie' }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('inventory.supplier_categories') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameField = document.getElementById('name');
    const descriptionField = document.getElementById('description');
    const colorField = document.getElementById('color');
    const iconField = document.getElementById('icon');
    
    const previewName = document.getElementById('previewName');
    const previewDescription = document.getElementById('previewDescription');
    const previewHeader = document.getElementById('previewHeader');
    const previewIcon = document.getElementById('previewIcon');
    const iconPreview = document.getElementById('iconPreview');
    
    // Mise à jour en temps réel de l'aperçu
    nameField.addEventListener('input', function() {
        previewName.textContent = this.value || 'Nom de la catégorie';
    });
    
    descriptionField.addEventListener('input', function() {
        previewDescription.textContent = this.value || 'Description de la catégorie';
    });
    
    colorField.addEventListener('input', function() {
        previewHeader.style.backgroundColor = this.value;
    });
    
    iconField.addEventListener('input', function() {
        const iconClass = this.value || 'fas fa-truck';
        previewIcon.className = iconClass;
        iconPreview.className = iconClass;
    });
});
</script>
{% endblock %}
