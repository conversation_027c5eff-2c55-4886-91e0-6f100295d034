import os
import uuid
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
from flask import current_app
from werkzeug.utils import secure_filename

# Définir les extensions autorisées globalement
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

def allowed_file(filename, allowed_extensions=None):
    """
    Vérifie si le fichier a une extension autorisée
    :param filename: Nom du fichier à vérifier
    :param allowed_extensions: Set d'extensions autorisées. Si None, utilise ALLOWED_EXTENSIONS
    :return: Boolean indiquant si le fichier est autorisé
    """
    if allowed_extensions is None:
        allowed_extensions = ALLOWED_EXTENSIONS
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions

def save_file(file, folder, allowed_extensions=None):
    """
    Sauvegarde un fichier dans le dossier spécifié
    :param file: Fichier à sauvegarder
    :param folder: Dossier de destination
    :param allowed_extensions: Extensions autorisées (optionnel)
    :return: Chemin relatif du fichier sauvegardé ou None
    """
    if file and allowed_file(file.filename, allowed_extensions):
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], folder, unique_filename)
        
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        file.save(file_path)
        return os.path.join(folder, unique_filename)
    return None

def save_image(image_file, folder, size=(800, 800)):
    """
    Sauvegarde et redimensionne une image dans le dossier static
    :param image_file: Fichier image à sauvegarder
    :param folder: Dossier de destination (dans static/uploads)
    :param size: Taille maximale de l'image (largeur, hauteur)
    :return: Chemin relatif de l'image sauvegardée ou None
    """
    if not PIL_AVAILABLE:
        return save_file(image_file, folder)
        
    if image_file and allowed_file(image_file.filename):
        filename = secure_filename(image_file.filename)
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        
        # Créer le chemin dans static/uploads/folder
        upload_folder = os.path.join('app', 'static', 'uploads', folder)
        os.makedirs(upload_folder, exist_ok=True)
        
        image_path = os.path.join(upload_folder, unique_filename)
        
        try:
            image = Image.open(image_file)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Redimensionner l'image principale
            image.thumbnail(size)
            image.save(image_path, quality=85, optimize=True)
            
            # Créer une miniature (200x200)
            thumbnail = image.copy()
            thumbnail.thumbnail((200, 200))
            thumb_path = os.path.join(upload_folder, f"thumb_{unique_filename}")
            thumbnail.save(thumb_path, quality=85, optimize=True)
            
            # Retourner le chemin relatif pour la base de données (depuis static)
            return f"uploads/{folder}/{unique_filename}"
        except Exception as e:
            current_app.logger.error(f"Erreur lors du traitement de l'image: {str(e)}")
            # Supprimer les fichiers en cas d'erreur
            if os.path.exists(image_path):
                os.remove(image_path)
            if os.path.exists(thumb_path):
                os.remove(thumb_path)
            return None
            
    return None

def delete_file(file_path):
    """
    Supprime un fichier
    :param file_path: Chemin relatif du fichier à supprimer
    :return: True si le fichier a été supprimé, False sinon
    """
    if file_path:
        full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], file_path)
        if os.path.exists(full_path):
            os.remove(full_path)
            return True
    return False

def get_file_url(file_path):
    """
    Retourne l'URL d'un fichier
    :param file_path: Chemin relatif du fichier
    :return: URL du fichier ou None
    """
    if file_path:
        return os.path.join('/uploads', file_path)
    return None

def get_file_size(file_path):
    """
    Retourne la taille d'un fichier en octets
    :param file_path: Chemin relatif du fichier
    :return: Taille du fichier en octets ou 0
    """
    if file_path:
        full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], file_path)
        if os.path.exists(full_path):
            return os.path.getsize(full_path)
    return 0

def save_product_image(file, product_id):
    """
    Sauvegarde et redimensionne l'image d'un produit
    :param file: Fichier image à sauvegarder
    :param product_id: ID du produit
    :return: Chemin relatif de l'image sauvegardée ou None
    """
    if not file or not allowed_file(file.filename):
        return None
        
    # Créer le dossier uploads/products s'il n'existe pas
    upload_folder = os.path.join('app', 'static', 'uploads', 'products')
    os.makedirs(upload_folder, exist_ok=True)
    
    # Générer un nom de fichier unique
    filename = secure_filename(f"product_{product_id}_{uuid.uuid4().hex[:8]}_{file.filename}")
    filepath = os.path.join(upload_folder, filename)
    
    try:
        # Ouvrir et redimensionner l'image
        image = Image.open(file)
        if image.mode != 'RGB':
            image = image.convert('RGB')
            
        # Redimensionner l'image principale (max 800x800)
        image.thumbnail((800, 800))
        image.save(filepath, quality=85, optimize=True)
        
        # Créer une miniature (200x200)
        thumbnail = image.copy()
        thumbnail.thumbnail((200, 200))
        thumb_path = os.path.join(upload_folder, f"thumb_{filename}")
        thumbnail.save(thumb_path, quality=85, optimize=True)
        
        # Retourner le chemin relatif pour la base de données
        return f"uploads/products/{filename}"
    except Exception as e:
        current_app.logger.error(f"Erreur lors du traitement de l'image: {str(e)}")
        # Supprimer les fichiers en cas d'erreur
        if os.path.exists(filepath):
            os.remove(filepath)
        if os.path.exists(thumb_path):
            os.remove(thumb_path)
        return None

def delete_product_image(image_path):
    """
    Supprime l'image d'un produit et sa miniature
    :param image_path: Chemin relatif de l'image à supprimer
    """
    if not image_path:
        return
        
    try:
        full_path = os.path.join(current_app.root_path, 'static', image_path)
        if os.path.exists(full_path):
            os.remove(full_path)
            
        filename = os.path.basename(image_path)
        thumb_path = os.path.join(os.path.dirname(full_path), f"thumb_{filename}")
        if os.path.exists(thumb_path):
            os.remove(thumb_path)
    except Exception as e:
        current_app.logger.error(f"Erreur lors de la suppression de l'image: {str(e)}") 