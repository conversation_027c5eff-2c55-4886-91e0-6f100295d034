from flask_wtf import FlaskForm
from wtforms import <PERSON>loat<PERSON>ield, TextAreaField, SelectField, StringField, BooleanField
from wtforms.validators import DataRequired, NumberRange
from flask_wtf.file import FileField, FileAllowed

class OpenRegisterForm(FlaskForm):
    initial_amount = FloatField('Montant Initial', validators=[
        DataRequired(message="Le montant initial est requis")
    ])
    use_last_amount = BooleanField('Utiliser le dernier montant de fermeture ?')
    reason = TextAreaField('Motif du changement de montant', 
        description="Obligatoire si le montant diffère du dernier montant de fermeture")

class CloseRegisterForm(FlaskForm):
    final_amount = FloatField('Montant Final', validators=[
        DataRequired(),
        NumberRange(min=0, message="Le montant doit être positif")
    ])
    note = TextAreaField('Note')

class CashInForm(FlaskForm):
    amount = FloatField('Montant', validators=[
        DataRequired(),
        NumberRange(min=0, message="Le montant doit être positif")
    ])
    source = SelectField('Source', choices=[
        ('cash', 'Espèces'),
        ('check', 'Chèque'),
        ('transfer', 'Virement'),
        ('other', 'Autre')
    ])
    note = TextAreaField('Note', validators=[DataRequired()])

class CashOutForm(FlaskForm):
    amount = FloatField('Montant', validators=[
        DataRequired(),
        NumberRange(min=0, message="Le montant doit être positif")
    ])
    reason = SelectField('Motif', choices=[
        ('supplier_payment', 'Paiement Fournisseur'),
        ('expense', 'Dépense'),
        ('other', 'Autre')
    ])
    note = TextAreaField('Note', validators=[DataRequired()])

class BankDepositForm(FlaskForm):
    amount = FloatField('Montant', validators=[
        DataRequired(),
        NumberRange(min=0, message="Le montant doit être positif")
    ])
    note = TextAreaField('Note')
    receipt = FileField('Justificatif', validators=[
        FileAllowed(['jpg', 'jpeg', 'png', 'pdf'], 'Images ou PDF uniquement')
    ]) 

class ResetRegisterForm(FlaskForm):
    """Formulaire vide pour le CSRF token"""
    pass 

class CashRegisterSettingsForm(FlaskForm):
    minimum_amount_required = BooleanField('Exiger un montant minimum')
    minimum_amount = FloatField('Montant minimum', validators=[
        NumberRange(min=0, message="Le montant minimum doit être positif")
    ])
    use_last_closing_amount = BooleanField('Utiliser le dernier montant de fermeture') 