{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">{{ title }}</h2>
                </div>
                <div class="card-body">
                    <form method="POST" novalidate>
                        {{ form.csrf_token }}
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    {{ form.name.label(class="form-label") }}
                                    {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                    {% for error in form.name.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    {{ form.category_id.label(class="form-label") }}
                                    {{ form.category_id(class="form-control" + (" is-invalid" if form.category_id.errors else "")) }}
                                    {% for error in form.category_id.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                    <small class="form-text text-muted">
                                        <a href="{{ url_for('inventory.supplier_categories') }}" target="_blank">
                                            <i class="fas fa-external-link-alt"></i> Gérer les catégories
                                        </a>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.contact_name.label(class="form-label") }}
                            {{ form.contact_name(class="form-control" + (" is-invalid" if form.contact_name.errors else "")) }}
                            {% for error in form.contact_name.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.email.label(class="form-label") }}
                                    {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                    {% for error in form.email.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.phone.label(class="form-label") }}
                                    {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                                    {% for error in form.phone.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.website.label(class="form-label") }}
                                    {{ form.website(class="form-control" + (" is-invalid" if form.website.errors else ""), placeholder="https://") }}
                                    {% for error in form.website.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.tax_id.label(class="form-label") }}
                                    {{ form.tax_id(class="form-control" + (" is-invalid" if form.tax_id.errors else "")) }}
                                    {% for error in form.tax_id.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.payment_terms.label(class="form-label") }}
                                    {{ form.payment_terms(class="form-control" + (" is-invalid" if form.payment_terms.errors else ""), placeholder="Ex: 30 jours net") }}
                                    {% for error in form.payment_terms.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.rating.label(class="form-label") }}
                                    {{ form.rating(class="form-control" + (" is-invalid" if form.rating.errors else "")) }}
                                    {% for error in form.rating.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.address.label(class="form-label") }}
                            {{ form.address(class="form-control" + (" is-invalid" if form.address.errors else ""), rows=3) }}
                            {% for error in form.address.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.notes.label(class="form-label") }}
                            {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows=3) }}
                            {% for error in form.notes.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_active(class="form-check-input") }}
                                {{ form.is_active.label(class="form-check-label") }}
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('inventory.suppliers') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 