from app.utils.helpers import format_datetime, format_currency

def datetime_filter(value, date_format=None, time_format=None):
    """Format a datetime object for display in templates"""
    if value is None:
        return ''
    return format_datetime(value, date_format, time_format)

def format_currency_filter(value):
    """Format a number as currency"""
    if value is None:
        return ''
    return format_currency(value)