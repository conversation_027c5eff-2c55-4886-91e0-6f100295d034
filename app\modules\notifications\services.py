"""Services pour le système de notifications"""

from datetime import datetime
from app import db
from app.modules.notifications.models import (
    Notification, NotificationType, NotificationStatus, NotificationPreference
)

class NotificationService:
    """Service pour gérer les notifications"""
    
    def __init__(self):
        pass
    
    def create_notification(self, type, title, message, recipient_type, recipient_id, 
                          order_id=None, data=None):
        """Créer une nouvelle notification"""
        try:
            # Convertir le type en enum si c'est une string
            if isinstance(type, str):
                type = NotificationType(type.lower())
            
            notification = Notification(
                type=type,
                title=title,
                message=message,
                recipient_type=recipient_type,
                recipient_id=recipient_id,
                order_id=order_id,
                data=data or {},
                status=NotificationStatus.PENDING
            )
            
            db.session.add(notification)
            db.session.commit()
            
            # Envoyer immédiatement la notification
            self.send_notification(notification)
            
            return notification
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    def send_notification(self, notification):
        """Envoyer une notification"""
        try:
            # Vérifier les préférences de l'utilisateur
            preferences = NotificationPreference.query.filter_by(
                user_id=notification.recipient_id,
                user_type=notification.recipient_type
            ).first()

            # Si pas de préférences, créer des préférences par défaut
            if not preferences:
                preferences = NotificationPreference(
                    user_id=notification.recipient_id,
                    user_type=notification.recipient_type
                )
                db.session.add(preferences)
                db.session.commit()

            # Vérifier si les notifications sont activées
            if not preferences.browser_enabled:
                notification.status = NotificationStatus.FAILED
                db.session.commit()
                return False

            # Marquer comme envoyée
            notification.mark_as_sent()

            # Envoyer via SocketIO si disponible
            try:
                from app.extensions import socketio
                room = f"user_{notification.recipient_type}_{notification.recipient_id}"

                socketio.emit('new_notification', {
                    'id': notification.id,
                    'type': notification.type.value,
                    'title': notification.title,
                    'message': notification.message,
                    'created_at': notification.created_at.isoformat(),
                    'data': notification.data
                }, room=room)

            except Exception as e:
                print(f"Erreur lors de l'envoi SocketIO: {e}")

            return True

        except Exception as e:
            notification.status = NotificationStatus.FAILED
            db.session.commit()
            raise e
    
    def notify_order_received(self, order):
        """Notifier qu'une nouvelle commande a été reçue"""
        return self.create_notification(
            type=NotificationType.ORDER_RECEIVED,
            title=f"Nouvelle commande #{order.id}",
            message=f"Une nouvelle commande d'un montant de {order.total_amount}€ a été reçue.",
            recipient_type='restaurant',
            recipient_id=order.owner_id,
            order_id=order.id,
            data={
                'order_id': order.id,
                'customer_name': order.customer_name,
                'total_amount': float(order.total_amount),
                'delivery_type': order.delivery_type
            }
        )
    
    def notify_order_confirmed(self, order):
        """Notifier que la commande a été confirmée"""
        # Notifier le client
        customer_notification = self.create_notification(
            type=NotificationType.ORDER_CONFIRMED,
            title=f"Commande #{order.id} confirmée",
            message="Votre commande a été confirmée et est en cours de préparation.",
            recipient_type='customer',
            recipient_id=order.customer_id if order.customer_id else 0,
            order_id=order.id,
            data={
                'order_id': order.id,
                'estimated_time': order.estimated_preparation_time
            }
        )
        
        return customer_notification
    
    def notify_order_ready(self, order):
        """Notifier que la commande est prête"""
        notifications = []
        
        # Notifier le client
        customer_notification = self.create_notification(
            type=NotificationType.ORDER_READY,
            title=f"Commande #{order.id} prête",
            message="Votre commande est prête pour le retrait ou la livraison.",
            recipient_type='customer',
            recipient_id=order.customer_id if order.customer_id else 0,
            order_id=order.id
        )
        notifications.append(customer_notification)
        
        # Si c'est une livraison, notifier le livreur
        if order.delivery_type == 'delivery' and order.delivery_person_id:
            delivery_notification = self.create_notification(
                type=NotificationType.ORDER_READY,
                title=f"Commande #{order.id} prête pour livraison",
                message=f"La commande est prête pour livraison à {order.delivery_address}.",
                recipient_type='delivery',
                recipient_id=order.delivery_person_id,
                order_id=order.id,
                data={
                    'delivery_address': order.delivery_address,
                    'customer_phone': order.customer_phone
                }
            )
            notifications.append(delivery_notification)
        
        return notifications
    
    def notify_order_out_for_delivery(self, order):
        """Notifier que la commande est en livraison"""
        return self.create_notification(
            type=NotificationType.ORDER_OUT_FOR_DELIVERY,
            title=f"Commande #{order.id} en livraison",
            message="Votre commande est en cours de livraison.",
            recipient_type='customer',
            recipient_id=order.customer_id if order.customer_id else 0,
            order_id=order.id,
            data={
                'delivery_person': order.delivery_person.name if order.delivery_person else None,
                'estimated_delivery_time': order.estimated_delivery_time
            }
        )
    
    def notify_order_delivered(self, order):
        """Notifier que la commande a été livrée"""
        notifications = []
        
        # Notifier le client
        customer_notification = self.create_notification(
            type=NotificationType.ORDER_DELIVERED,
            title=f"Commande #{order.id} livrée",
            message="Votre commande a été livrée avec succès. Merci pour votre commande !",
            recipient_type='customer',
            recipient_id=order.customer_id if order.customer_id else 0,
            order_id=order.id
        )
        notifications.append(customer_notification)
        
        # Notifier le restaurant
        restaurant_notification = self.create_notification(
            type=NotificationType.ORDER_DELIVERED,
            title=f"Commande #{order.id} livrée",
            message=f"La commande #{order.id} a été livrée avec succès.",
            recipient_type='restaurant',
            recipient_id=order.owner_id,
            order_id=order.id
        )
        notifications.append(restaurant_notification)
        
        return notifications
    
    def notify_order_cancelled(self, order, reason=None):
        """Notifier qu'une commande a été annulée"""
        message = f"La commande #{order.id} a été annulée."
        if reason:
            message += f" Raison: {reason}"
        
        return self.create_notification(
            type=NotificationType.ORDER_CANCELLED,
            title=f"Commande #{order.id} annulée",
            message=message,
            recipient_type='customer',
            recipient_id=order.customer_id if order.customer_id else 0,
            order_id=order.id,
            data={'reason': reason}
        )
    
    def get_unread_count(self, user_id, user_type):
        """Obtenir le nombre de notifications non lues"""
        return Notification.query.filter_by(
            recipient_id=user_id,
            recipient_type=user_type,
            status=NotificationStatus.SENT
        ).count()
    
    def get_recent_notifications(self, user_id, user_type, limit=10):
        """Obtenir les notifications récentes"""
        return Notification.query.filter_by(
            recipient_id=user_id,
            recipient_type=user_type
        ).order_by(Notification.created_at.desc()).limit(limit).all()
