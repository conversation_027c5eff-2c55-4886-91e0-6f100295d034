{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ title }}</h1>

    <!-- Cartes de statistiques -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Valeur totale du stock</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ "%.2f"|format(total_value) }} €</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-euro-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Produits en rupture</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ out_of_stock_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Stock faible</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ low_stock_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Stock optimal</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ optimal_stock_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques -->
    <div class="row">
        <!-- Graphique de la valeur du stock par catégorie -->
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Valeur du stock par catégorie</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie">
                        <canvas id="stockValueChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graphique des produits par niveau de stock -->
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Répartition des niveaux de stock</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie">
                        <canvas id="stockLevelChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableaux -->
    <div class="row">
        <!-- Produits en rupture de stock -->
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">Produits en rupture de stock</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Catégorie</th>
                                    <th>Stock minimum</th>
                                    <th>Stock actuel</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in out_of_stock_products %}
                                <tr>
                                    <td>{{ product.name }}</td>
                                    <td>{{ product.category.name }}</td>
                                    <td>{{ product.minimum_stock }}</td>
                                    <td class="text-danger">{{ product.get_available_quantity() }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Produits en stock faible -->
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">Produits en stock faible</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Catégorie</th>
                                    <th>Stock minimum</th>
                                    <th>Stock actuel</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in low_stock_products %}
                                <tr>
                                    <td>{{ product.name }}</td>
                                    <td>{{ product.category.name }}</td>
                                    <td>{{ product.minimum_stock }}</td>
                                    <td class="text-warning">{{ product.get_available_quantity() }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Graphique de la valeur du stock par catégorie
var ctx = document.getElementById("stockValueChart");
var stockValueChart = new Chart(ctx, {
    type: 'pie',
    data: {
        labels: {{ category_labels|tojson }},
        datasets: [{
            data: {{ category_values|tojson }},
            backgroundColor: {{ category_colors|tojson }},
        }]
    },
    options: {
        maintainAspectRatio: false,
        tooltips: {
            callbacks: {
                label: function(tooltipItem, data) {
                    var value = data.datasets[0].data[tooltipItem.index];
                    return data.labels[tooltipItem.index] + ': ' + value.toFixed(2) + ' €';
                }
            }
        }
    }
});

// Graphique des niveaux de stock
var ctx2 = document.getElementById("stockLevelChart");
var stockLevelChart = new Chart(ctx2, {
    type: 'doughnut',
    data: {
        labels: ['Stock optimal', 'Stock faible', 'Rupture de stock'],
        datasets: [{
            data: [
                {{ optimal_stock_count }},
                {{ low_stock_count }},
                {{ out_of_stock_count }}
            ],
            backgroundColor: [
                'rgba(28, 200, 138, 0.8)',  // Vert
                'rgba(246, 194, 62, 0.8)',  // Jaune
                'rgba(231, 74, 59, 0.8)'    // Rouge
            ],
        }]
    },
    options: {
        maintainAspectRatio: false,
        tooltips: {
            callbacks: {
                label: function(tooltipItem, data) {
                    var value = data.datasets[0].data[tooltipItem.index];
                    var total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                    var percentage = ((value / total) * 100).toFixed(1);
                    return data.labels[tooltipItem.index] + ': ' + value + ' (' + percentage + '%)';
                }
            }
        }
    }
});
</script>
{% endblock %} 