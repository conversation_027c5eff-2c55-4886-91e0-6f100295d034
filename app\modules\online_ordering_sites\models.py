from app.extensions import db
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import enum

class OnlineOrderingSite(db.Model):
    """Modèle pour les sites de commande en ligne de chaque restaurant"""
    __tablename__ = 'online_ordering_sites'
    
    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    subdomain = db.Column(db.String(50), unique=True, nullable=False)
    is_active = db.Column(db.<PERSON>olean, default=True)
    
    # Personnalisation
    site_name = db.Column(db.String(100))
    site_description = db.Column(db.Text)
    primary_color = db.Column(db.String(7), default='#4e73df')
    secondary_color = db.Column(db.String(7), default='#858796')
    logo_path = db.Column(db.String(255))
    banner_path = db.Column(db.String(255))
    
    # Configuration
    allow_online_ordering = db.Column(db.Boolean, default=True)
    allow_delivery = db.Column(db.Boolean, default=True)
    allow_pickup = db.Column(db.Boolean, default=True)
    allow_dine_in = db.Column(db.Boolean, default=True)
    allow_drive_through = db.Column(db.Boolean, default=False)
    
    # Horaires de livraison (JSON)
    delivery_hours = db.Column(db.JSON)
    pickup_hours = db.Column(db.JSON)
    
    # Frais et limites
    delivery_fee = db.Column(db.Float, default=0.0)
    minimum_order = db.Column(db.Float, default=0.0)
    delivery_radius = db.Column(db.Float, default=10.0)  # en km
    
    # Métadonnées
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    owner = db.relationship('User', backref='online_ordering_site')
    orders = db.relationship('OnlineOrder', backref='site', lazy='dynamic')
    
    def __repr__(self):
        return f'<OnlineOrderingSite {self.subdomain}>'

class CustomerUser(UserMixin, db.Model):
    """Modèle pour les clients qui commandent sur les sites de restaurants"""
    __tablename__ = 'customer_users'
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    
    # Informations personnelles
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    
    # Adresse principale
    address = db.Column(db.Text, nullable=False)
    city = db.Column(db.String(50), nullable=False)
    country = db.Column(db.String(50), nullable=False)
    postal_code = db.Column(db.String(10))
    latitude = db.Column(db.Float)
    longitude = db.Column(db.Float)
    
    # Préférences
    preferred_language = db.Column(db.String(2), default='fr')
    email_notifications = db.Column(db.Boolean, default=True)
    sms_notifications = db.Column(db.Boolean, default=True)
    
    # Métadonnées
    is_active = db.Column(db.Boolean, default=True)
    email_verified = db.Column(db.Boolean, default=False)
    phone_verified = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # Relations
    orders = db.relationship('OnlineOrder', backref='customer', lazy='dynamic')
    favorite_products = db.relationship('Product', secondary='customer_favorite_products', backref='favorited_by')
    delivery_addresses = db.relationship('DeliveryAddress', backref='customer', lazy='dynamic')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    def __repr__(self):
        return f'<CustomerUser {self.email}>'

class DeliveryAddress(db.Model):
    """Adresses de livraison supplémentaires pour les clients"""
    __tablename__ = 'delivery_addresses'
    
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer_users.id'), nullable=False)
    
    name = db.Column(db.String(50))  # ex: "Maison", "Bureau", etc.
    address = db.Column(db.Text, nullable=False)
    city = db.Column(db.String(50), nullable=False)
    postal_code = db.Column(db.String(10))
    latitude = db.Column(db.Float)
    longitude = db.Column(db.Float)
    instructions = db.Column(db.Text)  # Instructions de livraison
    
    is_default = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<DeliveryAddress {self.name}>'

# Table d'association pour les produits favoris
customer_favorite_products = db.Table('customer_favorite_products',
    db.Column('customer_id', db.Integer, db.ForeignKey('customer_users.id'), primary_key=True),
    db.Column('product_id', db.Integer, db.ForeignKey('products.id'), primary_key=True)
)

class OrderType(enum.Enum):
    """Types de commande"""
    DELIVERY = 'delivery'
    PICKUP = 'pickup'
    DINE_IN = 'dine_in'
    DRIVE_THROUGH = 'drive_through'

class OnlineOrderStatus(enum.Enum):
    """Statuts des commandes en ligne"""
    PENDING = 'pending'
    CONFIRMED = 'confirmed'
    PREPARING = 'preparing'
    READY = 'ready'
    OUT_FOR_DELIVERY = 'out_for_delivery'
    DELIVERED = 'delivered'
    COMPLETED = 'completed'
    CANCELLED = 'cancelled'

class PaymentStatus(enum.Enum):
    """Statuts de paiement"""
    PENDING = 'pending'
    PAID = 'paid'
    FAILED = 'failed'
    REFUNDED = 'refunded'

class PaymentMethod(enum.Enum):
    """Méthodes de paiement"""
    CASH_ON_DELIVERY = 'cash_on_delivery'
    ONLINE_PAYMENT = 'online_payment'
    CASH = 'cash'
    CARD = 'card'

class OnlineOrder(db.Model):
    """Commandes passées via les sites de commande en ligne"""
    __tablename__ = 'online_orders'
    
    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(20), unique=True, nullable=False)
    
    # Relations
    site_id = db.Column(db.Integer, db.ForeignKey('online_ordering_sites.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer_users.id'), nullable=False)
    delivery_address_id = db.Column(db.Integer, db.ForeignKey('delivery_addresses.id'), nullable=True)
    assigned_deliverer_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    table_id = db.Column(db.Integer, db.ForeignKey('tables.id'), nullable=True)
    
    # Type et statut
    order_type = db.Column(db.Enum(OrderType), nullable=False)
    status = db.Column(db.Enum(OnlineOrderStatus), default=OnlineOrderStatus.PENDING)
    payment_status = db.Column(db.Enum(PaymentStatus), default=PaymentStatus.PENDING)
    payment_method = db.Column(db.Enum(PaymentMethod), default=PaymentMethod.CASH_ON_DELIVERY)
    
    # Montants
    subtotal = db.Column(db.Float, nullable=False)
    tax_amount = db.Column(db.Float, default=0.0)
    delivery_fee = db.Column(db.Float, default=0.0)
    discount_amount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, nullable=False)
    
    # Informations de livraison
    delivery_address = db.Column(db.Text)
    delivery_instructions = db.Column(db.Text)
    delivery_latitude = db.Column(db.Float)
    delivery_longitude = db.Column(db.Float)
    
    # Notes et instructions
    customer_notes = db.Column(db.Text)
    kitchen_notes = db.Column(db.Text)
    
    # Horaires
    ordered_at = db.Column(db.DateTime, default=datetime.utcnow)
    requested_delivery_time = db.Column(db.DateTime)
    confirmed_at = db.Column(db.DateTime)
    prepared_at = db.Column(db.DateTime)
    ready_at = db.Column(db.DateTime)
    out_for_delivery_at = db.Column(db.DateTime)
    delivered_at = db.Column(db.DateTime)
    
    # Relations
    items = db.relationship('OnlineOrderItem', backref='order', lazy='dynamic', cascade='all, delete-orphan')
    assigned_deliverer = db.relationship('User', foreign_keys=[assigned_deliverer_id])
    delivery_address_obj = db.relationship('DeliveryAddress', foreign_keys=[delivery_address_id])
    table = db.relationship('Table', foreign_keys=[table_id])
    
    def __repr__(self):
        return f'<OnlineOrder {self.order_number}>'

class OnlineOrderItem(db.Model):
    """Articles dans une commande en ligne"""
    __tablename__ = 'online_order_items'
    
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('online_orders.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    
    quantity = db.Column(db.Float, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)
    
    # Notes spéciales pour cet article
    special_instructions = db.Column(db.Text)
    
    # Relations
    product = db.relationship('Product')
    
    def __repr__(self):
        return f'<OnlineOrderItem {self.product.name} x{self.quantity}>'
