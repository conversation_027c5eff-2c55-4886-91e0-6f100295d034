from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, FloatField, SelectField, SubmitField, HiddenField
from wtforms.validators import DataRequired, Optional, NumberRange
from app.modules.cash_register.models_cash_register import PaymentMethod

class SaleForm(FlaskForm):
    table_number = StringField('Numéro de table')
    kitchen_note = TextAreaField('Note pour la cuisine')
    tax_rate = FloatField('Taux de TVA (%)', validators=[
        Optional(),
        NumberRange(min=0, max=100)
    ])
    discount_amount = FloatField('Montant de la réduction', validators=[
        Optional(),
        NumberRange(min=0)
    ])
    submit = SubmitField('Créer la vente')

class SaleItemForm(FlaskForm):
    product_id = HiddenField('ID Produit', validators=[DataRequired()])
    quantity = FloatField('Quantité', validators=[
        DataRequired(),
        NumberRange(min=0.1)
    ])
    submit = SubmitField('Ajouter au ticket')

class PaymentForm(FlaskForm):
    amount = FloatField('Montant', validators=[
        DataRequired(),
        NumberRange(min=0.01)
    ])
    method = SelectField('Mode de paiement', choices=[
        (method.name, method.value.capitalize())
        for method in PaymentMethod
    ], validators=[DataRequired()])
    reference = StringField('Référence')
    submit = SubmitField('Effectuer le paiement')

class QuickSaleForm(FlaskForm):
    product_search = StringField('Rechercher un produit')
    quantity = FloatField('Quantité', default=1, validators=[
        DataRequired(),
        NumberRange(min=0.1)
    ])
    submit = SubmitField('Ajouter')

class SaleFilterForm(FlaskForm):
    status = SelectField('Statut', choices=[
        ('all', 'Tous'),
        ('pending', 'En attente'),
        ('paid', 'Payé'),
        ('cancelled', 'Annulé')
    ])
    payment_method = SelectField('Mode de paiement', choices=[
        ('all', 'Tous')] + [
        (method.value, method.value.capitalize())
        for method in PaymentMethod
    ])
    date_range = SelectField('Période', choices=[
        ('today', 'Aujourd\'hui'),
        ('yesterday', 'Hier'),
        ('this_week', 'Cette semaine'),
        ('this_month', 'Ce mois'),
        ('custom', 'Personnalisé')
    ])
    submit = SubmitField('Filtrer')

class KitchenOrderForm(FlaskForm):
    status = SelectField('Statut', choices=[
        ('kitchen_pending', 'En attente'),
        ('kitchen_ready', 'Prêt'),
        ('delivered', 'Livré')
    ], validators=[DataRequired()])
    note = TextAreaField('Note')
    submit = SubmitField('Mettre à jour') 