{% extends "employees/base_hr.html" %}

{% block title %}{{ title }}{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-money-bill-wave me-2"></i>{{ title }}
        <small class="text-muted">{{ employee.full_name }}</small>
    </h1>
    <a href="{{ url_for('employees.detail', id=employee.id) }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>Retour
    </a>
</div>

<div class="row">
    <div class="col-lg-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice-dollar me-2"></i>Fiche de Paie
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <!-- Période de paie -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Période de Paie</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.pay_period_start.label(class="form-label") }}
                                    {{ form.pay_period_start(class="form-control" + (" is-invalid" if form.pay_period_start.errors else "")) }}
                                    {% if form.pay_period_start.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.pay_period_start.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    {{ form.pay_period_end.label(class="form-label") }}
                                    {{ form.pay_period_end(class="form-control" + (" is-invalid" if form.pay_period_end.errors else "")) }}
                                    {% if form.pay_period_end.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.pay_period_end.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Heures travaillées -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Heures Travaillées</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.regular_hours.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.regular_hours(class="form-control" + (" is-invalid" if form.regular_hours.errors else "")) }}
                                        <span class="input-group-text">h</span>
                                    </div>
                                    {% if form.regular_hours.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.regular_hours.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    {{ form.overtime_hours.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.overtime_hours(class="form-control" + (" is-invalid" if form.overtime_hours.errors else "")) }}
                                        <span class="input-group-text">h</span>
                                    </div>
                                    {% if form.overtime_hours.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.overtime_hours.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Salaires -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Calcul du Salaire</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.gross_pay.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.gross_pay(class="form-control" + (" is-invalid" if form.gross_pay.errors else ""), readonly=true) }}
                                        <span class="input-group-text">€</span>
                                    </div>
                                    <small class="form-text text-muted">Calculé automatiquement</small>
                                    {% if form.gross_pay.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.gross_pay.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    {{ form.net_pay.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.net_pay(class="form-control" + (" is-invalid" if form.net_pay.errors else ""), readonly=true) }}
                                        <span class="input-group-text">€</span>
                                    </div>
                                    <small class="form-text text-muted">Calculé automatiquement</small>
                                    {% if form.net_pay.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.net_pay.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Déductions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Déductions</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    {{ form.tax_deductions.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.tax_deductions(class="form-control" + (" is-invalid" if form.tax_deductions.errors else "")) }}
                                        <span class="input-group-text">€</span>
                                    </div>
                                    {% if form.tax_deductions.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.tax_deductions.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    {{ form.social_security_deductions.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.social_security_deductions(class="form-control" + (" is-invalid" if form.social_security_deductions.errors else "")) }}
                                        <span class="input-group-text">€</span>
                                    </div>
                                    {% if form.social_security_deductions.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.social_security_deductions.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    {{ form.other_deductions.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.other_deductions(class="form-control" + (" is-invalid" if form.other_deductions.errors else "")) }}
                                        <span class="input-group-text">€</span>
                                    </div>
                                    {% if form.other_deductions.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.other_deductions.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.total_deductions.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.total_deductions(class="form-control" + (" is-invalid" if form.total_deductions.errors else ""), readonly=true) }}
                                        <span class="input-group-text">€</span>
                                    </div>
                                    <small class="form-text text-muted">Calculé automatiquement</small>
                                    {% if form.total_deductions.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.total_deductions.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informations supplémentaires -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Informations Supplémentaires</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.pay_date.label(class="form-label") }}
                                    {{ form.pay_date(class="form-control" + (" is-invalid" if form.pay_date.errors else "")) }}
                                    {% if form.pay_date.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.pay_date.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check mt-4">
                                        {{ form.is_processed(class="form-check-input" + (" is-invalid" if form.is_processed.errors else "")) }}
                                        {{ form.is_processed.label(class="form-check-label") }}
                                        {% if form.is_processed.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.is_processed.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                {{ form.notes.label(class="form-label") }}
                                {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="3") }}
                                {% if form.notes.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.notes.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('employees.detail', id=employee.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Annuler
                        </a>
                        <div>
                            <button type="button" class="btn btn-outline-info me-2" onclick="calculatePayroll()">
                                <i class="fas fa-calculator me-1"></i>Calculer
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Enregistrer
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const regularHoursInput = document.getElementById('regular_hours');
    const overtimeHoursInput = document.getElementById('overtime_hours');
    const grossPayInput = document.getElementById('gross_pay');
    const taxDeductionsInput = document.getElementById('tax_deductions');
    const socialSecurityInput = document.getElementById('social_security_deductions');
    const otherDeductionsInput = document.getElementById('other_deductions');
    const totalDeductionsInput = document.getElementById('total_deductions');
    const netPayInput = document.getElementById('net_pay');

    // Données de l'employé (à récupérer depuis le serveur)
    const employeeData = {
        baseSalary: {{ (employee.base_salary or 0)|float }},
        hourlyRate: {{ (employee.hourly_rate or 0)|float }}
    };

    function calculatePayroll() {
        const regularHours = parseFloat(regularHoursInput.value) || 0;
        const overtimeHours = parseFloat(overtimeHoursInput.value) || 0;
        
        // Calcul du salaire brut
        let grossPay = 0;
        if (employeeData.hourlyRate > 0) {
            // Employé payé à l'heure
            grossPay = (regularHours * employeeData.hourlyRate) + (overtimeHours * employeeData.hourlyRate * 1.5);
        } else {
            // Employé avec salaire fixe
            grossPay = employeeData.baseSalary;
        }
        
        grossPayInput.value = grossPay.toFixed(2);
        
        // Calcul des déductions
        const taxRate = 0.20; // 20% d'impôts
        const socialSecurityRate = 0.15; // 15% de sécurité sociale
        
        const taxDeductions = grossPay * taxRate;
        const socialSecurityDeductions = grossPay * socialSecurityRate;
        const otherDeductions = parseFloat(otherDeductionsInput.value) || 0;
        
        taxDeductionsInput.value = taxDeductions.toFixed(2);
        socialSecurityInput.value = socialSecurityDeductions.toFixed(2);
        
        const totalDeductions = taxDeductions + socialSecurityDeductions + otherDeductions;
        totalDeductionsInput.value = totalDeductions.toFixed(2);
        
        // Calcul du salaire net
        const netPay = grossPay - totalDeductions;
        netPayInput.value = netPay.toFixed(2);
    }

    // Calculer automatiquement quand les heures changent
    regularHoursInput.addEventListener('input', calculatePayroll);
    overtimeHoursInput.addEventListener('input', calculatePayroll);
    otherDeductionsInput.addEventListener('input', calculatePayroll);

    // Calculer au chargement
    calculatePayroll();
});
</script>
{% endblock %}
