{% extends "employees/base_hr.html" %}

{% block title %}Évaluations en Attente{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-clipboard-list me-2"></i>Évaluations en Attente
    </h1>
    <a href="{{ url_for('employees.performance_reports') }}" class="btn btn-outline-secondary">
        <i class="fas fa-chart-line me-1"></i>Voir les Rapports
    </a>
</div>

<div class="alert alert-info">
    <i class="fas fa-info-circle me-2"></i>
    <strong>Critère :</strong> Employés sans évaluation depuis plus de 6 mois (depuis le {{ six_months_ago.strftime('%d/%m/%Y') }})
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-exclamation-triangle me-2 text-warning"></i>Employés Nécessitant une Évaluation
        </h5>
    </div>
    <div class="card-body p-0">
        {% if employees_need_evaluation %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Employé</th>
                        <th>Poste</th>
                        <th>Date d'embauche</th>
                        <th>Dernière évaluation</th>
                        <th>Ancienneté</th>
                        <th>Priorité</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees_need_evaluation %}
                    {% set last_performance = None %}
                    {% set days_since_hire = 0 %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                    {{ employee.first_name[0] }}{{ employee.last_name[0] }}
                                </div>
                                <div>
                                    <strong>{{ employee.full_name }}</strong><br>
                                    <small class="text-muted">{{ employee.employee_id }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            {{ employee.position }}<br>
                            <small class="text-muted">{{ employee.department or '-' }}</small>
                        </td>
                        <td>
                            {{ employee.hire_date.strftime('%d/%m/%Y') }}
                        </td>
                        <td>
                            <span class="text-muted">Pas d'évaluation</span>
                        </td>
                        <td>
                            {{ employee.hire_date.strftime('%d/%m/%Y') }}
                        </td>
                        <td>
                            <span class="badge bg-warning">Haute</span>
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('employees.new_performance', id=employee.id) }}" 
                                   class="btn btn-sm btn-primary" title="Nouvelle Évaluation">
                                    <i class="fas fa-plus"></i>
                                </a>
                                {% if last_performance %}
                                <a href="{{ url_for('employees.performance_detail', performance_id=last_performance.id) }}" 
                                   class="btn btn-sm btn-outline-info" title="Voir Dernière">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% endif %}
                                <a href="{{ url_for('employees.performance_history', id=employee.id) }}" 
                                   class="btn btn-sm btn-outline-secondary" title="Historique">
                                    <i class="fas fa-history"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
            <h5 class="text-success">Toutes les évaluations sont à jour !</h5>
            <p class="text-muted">Tous les employés actifs ont été évalués récemment.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Actions en lot -->
{% if employees_need_evaluation %}
<div class="card mt-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-tasks me-2"></i>Actions en Lot
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <button class="btn btn-primary w-100" onclick="scheduleEvaluations()">
                    <i class="fas fa-calendar-plus me-2"></i>Programmer toutes les évaluations
                </button>
            </div>
            <div class="col-md-6">
                <button class="btn btn-outline-info w-100" onclick="sendReminders()">
                    <i class="fas fa-bell me-2"></i>Envoyer des rappels
                </button>
            </div>
        </div>
    </div>
</div>
{% endif %}

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
    font-weight: bold;
}
</style>

<script>
function scheduleEvaluations() {
    if (confirm('Programmer des évaluations pour tous les employés listés ?')) {
        alert('Fonctionnalité de programmation à implémenter');
    }
}

function sendReminders() {
    if (confirm('Envoyer des rappels d\'évaluation aux managers ?')) {
        alert('Fonctionnalité de rappels à implémenter');
    }
}
</script>
{% endblock %}
