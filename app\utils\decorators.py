from functools import wraps
from flask import abort
from flask_login import current_user

def permission_required(permission):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.has_permission(permission):
                abort(403)
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def owner_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_owner:
            abort(403)
        return f(*args, **kwargs)
    return decorated_function

def manager_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not (current_user.is_owner or current_user.is_manager):
            abort(403)
        return f(*args, **kwargs)
    return decorated_function

def reports_access_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.can_access_reports():
            abort(403)
        return f(*args, **kwargs)
    return decorated_function

def inventory_access_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.can_manage_inventory():
            abort(403)
        return f(*args, **kwargs)
    return decorated_function

def sales_access_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.can_process_sales():
            abort(403)
        return f(*args, **kwargs)
    return decorated_function

def kitchen_access_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.can_access_kitchen():
            abort(403)
        return f(*args, **kwargs)
    return decorated_function

def system_admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_system_admin:
            abort(403)
        return f(*args, **kwargs)
    return decorated_function