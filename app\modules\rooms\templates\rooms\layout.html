{% extends "base.html" %}

{% block title %}Plan de salle - {{ room.name }}{% endblock %}

{% block head_scripts %}
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3><i class="fas fa-map"></i> Plan de salle - {{ room.name }}</h3>
        <div>
            <button class="btn btn-outline-primary btn-sm me-2" id="editModeBtn">
                <i class="fas fa-edit"></i> Mode édition
            </button>
            <a href="{{ url_for('rooms.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Retour
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Panneau de contrôle -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-tools"></i> Outils</h6>
                </div>
                <div class="card-body">
                    <!-- Informations de la salle -->
                    <div class="mb-3">
                        <h6>Informations</h6>
                        <small class="text-muted">
                            <div><strong>Dimensions:</strong> {{ room.width }}x{{ room.height }}px</div>
                            <div><strong>Tables:</strong> {{ tables|length }}</div>
                            <div><strong>Occupées:</strong> {{ tables|selectattr('status', 'equalto', 'occupied')|list|length }}</div>
                        </small>
                    </div>

                    <!-- Ajouter une table -->
                    <div class="mb-3" id="addTableSection" style="display: none;">
                        <h6>Ajouter une table</h6>
                        <form id="addTableForm">
                            <div class="mb-2">
                                <label class="form-label">Numéro</label>
                                <input type="text" class="form-control form-control-sm" id="tableNumber" required>
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Capacité</label>
                                <input type="number" class="form-control form-control-sm" id="tableCapacity" min="1" max="20" value="4" required>
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Forme</label>
                                <select class="form-select form-select-sm" id="tableShape">
                                    <option value="round">Ronde</option>
                                    <option value="square">Carrée</option>
                                    <option value="rectangle">Rectangulaire</option>
                                </select>
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Taille</label>
                                <select class="form-select form-select-sm" id="tableSize">
                                    <option value="small">Petite</option>
                                    <option value="medium" selected>Moyenne</option>
                                    <option value="large">Grande</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> Ajouter
                            </button>
                        </form>
                    </div>

                    <!-- Propriétés de la table sélectionnée -->
                    <div class="mb-3" id="tablePropertiesSection" style="display: none;">
                        <h6>Propriétés de la table</h6>
                        <div id="tableProperties">
                            <!-- Sera rempli dynamiquement -->
                        </div>
                    </div>

                    <!-- Légende -->
                    <div class="mb-3">
                        <h6>Légende</h6>
                        <div class="d-flex align-items-center mb-1">
                            <div class="table-legend available me-2"></div>
                            <small>Libre</small>
                        </div>
                        <div class="d-flex align-items-center mb-1">
                            <div class="table-legend occupied me-2"></div>
                            <small>Occupée</small>
                        </div>
                        <div class="d-flex align-items-center mb-1">
                            <div class="table-legend reserved me-2"></div>
                            <small>Réservée</small>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="table-legend cleaning me-2"></div>
                            <small>Nettoyage</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Plan de la salle -->
        <div class="col-md-9">
            <div class="card">
                <div class="card-body p-0">
                    <div id="roomLayout" class="room-layout" 
                         style="width: {{ room.width }}px; height: {{ room.height }}px; background-color: {{ room.background_color }};">
                        
                        {% for table in tables %}
                        {% if table.use_image and table.table_image %}
                        <!-- Table avec image personnalisée -->
                        <div class="table-item table-{{ table.status }} table-image"
                             data-table-id="{{ table.id }}"
                             data-table-number="{{ table.number }}"
                             data-table-capacity="{{ table.capacity }}"
                             data-table-status="{{ table.status }}"
                             data-table-covers="{{ table.current_covers }}"
                             data-table-amount="{{ table.current_amount }}"
                             style="left: {{ table.position_x }}px; top: {{ table.position_y }}px; background-image: url('{{ table.image_url }}'); background-size: cover; background-position: center;">

                            <div class="table-overlay">
                                <div class="table-number">{{ table.number }}</div>

                                {% if table.current_covers > 0 %}
                                <div class="table-covers">
                                    <i class="fas fa-users"></i> {{ table.current_covers }}
                                </div>
                                {% endif %}

                                {% if table.current_amount > 0 %}
                                <div class="table-amount">
                                    {{ table.current_amount|format_currency }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% else %}
                        <!-- Table avec forme géométrique -->
                        <div class="table-item table-{{ table.status }} table-{{ table.table_shape }} table-{{ table.table_size }}"
                             data-table-id="{{ table.id }}"
                             data-table-number="{{ table.number }}"
                             data-table-capacity="{{ table.capacity }}"
                             data-table-status="{{ table.status }}"
                             data-table-covers="{{ table.current_covers }}"
                             data-table-amount="{{ table.current_amount }}"
                             style="left: {{ table.position_x }}px; top: {{ table.position_y }}px; background-color: {{ table.table_color }};">

                            <div class="table-number">{{ table.number }}</div>

                            {% if table.current_covers > 0 %}
                            <div class="table-covers">
                                <i class="fas fa-users"></i> {{ table.current_covers }}
                            </div>
                            {% endif %}

                            {% if table.current_amount > 0 %}
                            <div class="table-amount">
                                {{ table.current_amount|format_currency }}
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.room-layout {
    position: relative;
    border: 2px solid #dee2e6;
    margin: 0 auto;
    overflow: hidden;
    background-image: 
        linear-gradient(rgba(0,0,0,.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,0,0,.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

.table-item {
    position: absolute;
    cursor: pointer;
    border: 2px solid #333;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
    transition: all 0.3s ease;
    user-select: none;
}

.table-item:hover {
    transform: scale(1.05);
    z-index: 10;
}

.table-item.selected {
    border-color: #007bff;
    box-shadow: 0 0 10px rgba(0,123,255,0.5);
}

/* Formes des tables */
.table-round { border-radius: 50%; }
.table-square { border-radius: 4px; }
.table-rectangle { border-radius: 8px; }

/* Tailles des tables */
.table-small { width: 40px; height: 40px; font-size: 10px; }
.table-medium { width: 60px; height: 60px; font-size: 12px; }
.table-large { width: 80px; height: 80px; font-size: 14px; }

/* Tables avec images */
.table-image {
    width: 80px !important;
    height: 80px !important;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.table-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.table-image .table-number {
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 2px;
}

.table-image .table-covers,
.table-image .table-amount {
    font-size: 10px;
    margin: 1px 0;
}

/* États des tables */
.table-available { border-color: #28a745; }
.table-occupied { border-color: #dc3545; }
.table-reserved { border-color: #ffc107; }
.table-cleaning { border-color: #6c757d; }

.table-number {
    font-size: inherit;
    font-weight: bold;
}

.table-covers {
    font-size: 8px;
    position: absolute;
    top: -8px;
    left: -8px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.table-amount {
    font-size: 8px;
    position: absolute;
    bottom: -8px;
    right: -8px;
    background: #28a745;
    color: white;
    border-radius: 4px;
    padding: 2px 4px;
    white-space: nowrap;
}

.table-legend {
    width: 20px;
    height: 20px;
    border: 2px solid;
    border-radius: 50%;
}

.table-legend.available { border-color: #28a745; background-color: #8B4513; }
.table-legend.occupied { border-color: #dc3545; background-color: #8B4513; }
.table-legend.reserved { border-color: #ffc107; background-color: #8B4513; }
.table-legend.cleaning { border-color: #6c757d; background-color: #8B4513; }

.edit-mode .table-item {
    cursor: move;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let editMode = false;
    let selectedTable = null;
    const roomLayout = document.getElementById('roomLayout');
    const editModeBtn = document.getElementById('editModeBtn');
    const addTableSection = document.getElementById('addTableSection');
    const tablePropertiesSection = document.getElementById('tablePropertiesSection');

    // Toggle edit mode
    editModeBtn.addEventListener('click', function() {
        editMode = !editMode;
        roomLayout.classList.toggle('edit-mode', editMode);
        addTableSection.style.display = editMode ? 'block' : 'none';

        if (editMode) {
            editModeBtn.innerHTML = '<i class="fas fa-eye"></i> Mode visualisation';
            editModeBtn.classList.remove('btn-outline-primary');
            editModeBtn.classList.add('btn-warning');
            enableDragAndDrop();
        } else {
            editModeBtn.innerHTML = '<i class="fas fa-edit"></i> Mode édition';
            editModeBtn.classList.remove('btn-warning');
            editModeBtn.classList.add('btn-outline-primary');
            disableDragAndDrop();
            deselectTable();
        }
    });

    // Enable drag and drop
    function enableDragAndDrop() {
        const tables = document.querySelectorAll('.table-item');
        tables.forEach(table => {
            table.draggable = true;
            table.addEventListener('dragstart', handleDragStart);
            table.addEventListener('dragend', handleDragEnd);
            table.addEventListener('click', selectTable);
        });

        roomLayout.addEventListener('dragover', handleDragOver);
        roomLayout.addEventListener('drop', handleDrop);
    }

    // Disable drag and drop
    function disableDragAndDrop() {
        const tables = document.querySelectorAll('.table-item');
        tables.forEach(table => {
            table.draggable = false;
            table.removeEventListener('dragstart', handleDragStart);
            table.removeEventListener('dragend', handleDragEnd);
            table.removeEventListener('click', selectTable);
        });

        roomLayout.removeEventListener('dragover', handleDragOver);
        roomLayout.removeEventListener('drop', handleDrop);
    }

    let draggedElement = null;

    function handleDragStart(e) {
        draggedElement = e.target;
        e.target.style.opacity = '0.5';
    }

    function handleDragEnd(e) {
        e.target.style.opacity = '';
        draggedElement = null;
    }

    function handleDragOver(e) {
        e.preventDefault();
    }

    function handleDrop(e) {
        e.preventDefault();
        if (draggedElement) {
            const rect = roomLayout.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Contraindre dans les limites de la salle
            const tableWidth = draggedElement.offsetWidth;
            const tableHeight = draggedElement.offsetHeight;
            const maxX = roomLayout.offsetWidth - tableWidth;
            const maxY = roomLayout.offsetHeight - tableHeight;

            const newX = Math.max(0, Math.min(x - tableWidth/2, maxX));
            const newY = Math.max(0, Math.min(y - tableHeight/2, maxY));

            draggedElement.style.left = newX + 'px';
            draggedElement.style.top = newY + 'px';

            // Sauvegarder la position
            updateTablePosition(draggedElement.dataset.tableId, newX, newY);
        }
    }

    // Select table
    function selectTable(e) {
        if (!editMode) return;

        deselectTable();
        selectedTable = e.currentTarget;
        selectedTable.classList.add('selected');
        showTableProperties(selectedTable);
    }

    function deselectTable() {
        if (selectedTable) {
            selectedTable.classList.remove('selected');
            selectedTable = null;
        }
        tablePropertiesSection.style.display = 'none';
    }

    function showTableProperties(table) {
        const properties = document.getElementById('tableProperties');
        properties.innerHTML = `
            <div class="mb-2">
                <strong>Table ${table.dataset.tableNumber}</strong>
            </div>
            <div class="mb-2">
                <small>Capacité: ${table.dataset.tableCapacity} personnes</small>
            </div>
            <div class="mb-2">
                <small>Statut: ${getStatusText(table.dataset.tableStatus)}</small>
            </div>
            <button class="btn btn-danger btn-sm" onclick="deleteTable(${table.dataset.tableId})">
                <i class="fas fa-trash"></i> Supprimer
            </button>
        `;
        tablePropertiesSection.style.display = 'block';
    }

    function getStatusText(status) {
        const statusMap = {
            'available': 'Libre',
            'occupied': 'Occupée',
            'reserved': 'Réservée',
            'cleaning': 'Nettoyage'
        };
        return statusMap[status] || status;
    }

    // Update table position via API
    function updateTablePosition(tableId, x, y) {
        fetch('/rooms/api/update_table_position', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({
                table_id: parseInt(tableId),
                position_x: Math.round(x),
                position_y: Math.round(y)
            })
        })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                console.error('Erreur lors de la mise à jour de la position:', data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
        });
    }

    // Add new table
    document.getElementById('addTableForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData();
        formData.append('number', document.getElementById('tableNumber').value);
        formData.append('capacity', document.getElementById('tableCapacity').value);
        formData.append('room_id', {{ room.id }});
        formData.append('table_shape', document.getElementById('tableShape').value);
        formData.append('table_size', document.getElementById('tableSize').value);

        // Ajouter la table via l'API des tables
        fetch('/tables/new', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                location.reload(); // Recharger la page pour voir la nouvelle table
            } else {
                alert('Erreur lors de l\'ajout de la table');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de l\'ajout de la table');
        });
    });

    // Delete table
    window.deleteTable = function(tableId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette table ?')) {
            fetch(`/tables/${tableId}/delete`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
                }
            })
            .then(response => {
                if (response.ok) {
                    location.reload();
                } else {
                    alert('Erreur lors de la suppression de la table');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                alert('Erreur lors de la suppression de la table');
            });
        }
    };
});
</script>
{% endblock %}
