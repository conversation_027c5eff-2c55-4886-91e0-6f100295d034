from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON><PERSON>ield, FileAllowed
from wtforms import <PERSON><PERSON>ield, TextAreaField, FloatField, SelectField, DateField, BooleanField, IntegerField, SubmitField
from wtforms.validators import DataRequired, Length, Optional, NumberRange

# Fonction de coercion personnalisée pour gérer les valeurs vides
def safe_int_coerce(value):
    if value == '' or value is None:
        return None
    return int(value)

class ExpenseCategoryForm(FlaskForm):
    name = StringField('Nom de la catégorie', validators=[
        DataRequired(),
        Length(min=2, max=64)
    ])
    description = TextAreaField('Description')
    image = FileField('Image', validators=[
        FileAllowed(['jpg', 'png', 'jpeg', 'gif'], 'Images uniquement!')
    ])
    submit = SubmitField('Enregistrer')

class ExpenseForm(FlaskForm):
    category_id = SelectField('Catégorie', coerce=int, validators=[DataRequired()])
    date = DateField('Date', validators=[DataRequired()])
    amount = FloatField('Montant', validators=[
        DataRequired(),
        NumberRange(min=0)
    ])
    description = TextAreaField('Description')
    reference = StringField('Référence', validators=[Length(max=50)])
    payment_method = SelectField('Mode de paiement', choices=[
        ('cash', 'Espèces'),
        ('card', 'Carte'),
        ('transfer', 'Virement'),
        ('check', 'Chèque'),
        ('other', 'Autre')
    ])
    is_recurring = BooleanField('Dépense récurrente')
    recurring_interval = SelectField('Intervalle de récurrence', choices=[
        ('', 'Non récurrent'),
        ('monthly', 'Mensuel'),
        ('quarterly', 'Trimestriel'),
        ('yearly', 'Annuel')
    ])
    recurring_day = IntegerField('Jour de récurrence', validators=[
        Optional(),
        NumberRange(min=1, max=31)
    ])
    image = FileField('Reçu/Facture', validators=[
        FileAllowed(['jpg', 'png', 'jpeg', 'pdf'], 'Images ou PDF uniquement!')
    ])
    submit = SubmitField('Enregistrer')

class BudgetForm(FlaskForm):
    category_id = SelectField('Catégorie', coerce=int, validators=[DataRequired()])
    amount = FloatField('Montant', validators=[
        DataRequired(),
        NumberRange(min=0)
    ])
    period = SelectField('Période', choices=[
        ('monthly', 'Mensuel'),
        ('yearly', 'Annuel')
    ], validators=[DataRequired()])
    start_date = DateField('Date de début', validators=[DataRequired()])
    end_date = DateField('Date de fin', validators=[Optional()])
    submit = SubmitField('Enregistrer')

class ExpenseFilterForm(FlaskForm):
    category_id = SelectField('Catégorie', coerce=safe_int_coerce, choices=[], validators=[Optional()])
    date_range = SelectField('Période', choices=[
        ('today', 'Aujourd\'hui'),
        ('this_week', 'Cette semaine'),
        ('this_month', 'Ce mois'),
        ('this_year', 'Cette année'),
        ('custom', 'Personnalisé')
    ])
    start_date = DateField('Date de début', validators=[Optional()])
    end_date = DateField('Date de fin', validators=[Optional()])
    payment_method = SelectField('Mode de paiement', choices=[
        ('', 'Tous'),
        ('cash', 'Espèces'),
        ('card', 'Carte'),
        ('transfer', 'Virement'),
        ('check', 'Chèque'),
        ('other', 'Autre')
    ], validators=[Optional()])
    min_amount = FloatField('Montant minimum', validators=[Optional(), NumberRange(min=0)])
    max_amount = FloatField('Montant maximum', validators=[Optional(), NumberRange(min=0)])
    submit = SubmitField('Filtrer') 