from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, FloatField, IntegerField, DateTimeField, BooleanField, SelectMultipleField
from wtforms.validators import DataRequired, Optional, NumberRange
from app.modules.promotions.models_promotion import PromotionType
from app.modules.inventory.models_product import Product

class PromotionForm(FlaskForm):
    name = StringField('Nom', validators=[DataRequired()])
    description = TextAreaField('Description', validators=[Optional()])
    promotion_type = SelectField('Type de promotion', 
        choices=[
            (PromotionType.PERCENTAGE, 'Pourcentage'),
            (PromotionType.FIXED, 'Montant fixe'),
            (PromotionType.BUY_X_GET_Y, 'Achetez X obtenez Y')
        ],
        validators=[DataRequired()]
    )
    value = FloatField('Valeur', validators=[DataRequired(), NumberRange(min=0)])
    buy_x = IntegerField('Acheter X', validators=[Optional(), NumberRange(min=1)])
    get_y = IntegerField('Obtenir Y', validators=[Optional(), NumberRange(min=1)])
    start_date = DateTimeField('Date de début', format='%Y-%m-%dT%H:%M', validators=[DataRequired()])
    end_date = DateTimeField('Date de fin', format='%Y-%m-%dT%H:%M', validators=[DataRequired()])
    min_purchase = FloatField('Montant minimum d\'achat', validators=[Optional(), NumberRange(min=0)])
    max_discount = FloatField('Réduction maximum', validators=[Optional(), NumberRange(min=0)])
    usage_limit = IntegerField('Limite d\'utilisation', validators=[Optional(), NumberRange(min=1)])
    is_active = BooleanField('Actif')
    
    # Liste des produits disponibles pour la promotion
    products = SelectMultipleField('Produits concernés', coerce=int, validators=[Optional()])
    
    def __init__(self, *args, **kwargs):
        super(PromotionForm, self).__init__(*args, **kwargs)
        # Charger la liste des produits disponibles
        self.products.choices = [(p.id, p.name) for p in Product.query.order_by(Product.name).all()] 