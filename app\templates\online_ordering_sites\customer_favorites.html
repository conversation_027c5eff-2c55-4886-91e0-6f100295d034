{% extends "online_ordering_sites/base.html" %}

{% block title %}Mes Favoris - {{ site.site_name }}{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-heart text-danger"></i> Mes Favoris</h2>
        <div>
            <a href="{{ url_for('online_ordering_sites.customer_profile') }}" class="btn btn-outline-secondary">
                <i class="fas fa-user"></i> Mon Profil
            </a>
            <a href="{{ url_for('online_ordering_sites.menu') }}" class="btn btn-primary">
                <i class="fas fa-utensils"></i> Voir le Menu
            </a>
        </div>
    </div>

    {% if favorite_products %}
        <div class="row">
            {% for product in favorite_products %}
            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 mb-4">
                <div class="card product-card h-100 border-0 shadow-sm">
                    {% if product.image_path %}
                        <img src="{{ url_for('static', filename=product.image_path) }}" 
                             class="card-img-top" alt="{{ product.name }}" 
                             style="height: 200px; object-fit: cover;">
                    {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                             style="height: 200px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                    {% endif %}
                    
                    <div class="card-body d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title mb-0">{{ product.name }}</h5>
                            <button class="btn btn-sm btn-outline-danger favorite-btn" 
                                    data-product-id="{{ product.id }}"
                                    data-is-favorite="true"
                                    title="Retirer des favoris">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                        
                        {% if product.description %}
                            <p class="card-text text-muted small flex-grow-1">
                                {{ product.description[:100] }}{% if product.description|length > 100 %}...{% endif %}
                            </p>
                        {% endif %}
                        
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="h5 text-primary mb-0">{{ "%.2f"|format(product.price) }}€</span>
                                {% if not product.is_available %}
                                    <span class="badge bg-danger">Indisponible</span>
                                {% elif product.stock_quantity is defined and product.stock_quantity <= 5 %}
                                    <span class="badge bg-warning">Stock limité</span>
                                {% endif %}
                            </div>
                            
                            {% if product.is_available %}
                                <div class="d-flex align-items-center gap-2">
                                    <div class="input-group input-group-sm" style="width: 120px;">
                                        <button class="btn btn-outline-secondary quantity-btn" type="button" data-action="decrease">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" class="form-control text-center quantity-input" 
                                               value="1" min="1" max="10" data-product-id="{{ product.id }}">
                                        <button class="btn btn-outline-secondary quantity-btn" type="button" data-action="increase">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                    <button class="btn btn-primary flex-grow-1 add-to-cart-btn" 
                                            data-product-id="{{ product.id }}"
                                            data-product-name="{{ product.name }}"
                                            data-product-price="{{ product.price }}">
                                        <i class="fas fa-cart-plus me-1"></i>
                                        Ajouter
                                    </button>
                                </div>
                            {% else %}
                                <button class="btn btn-secondary w-100" disabled>
                                    <i class="fas fa-times me-1"></i>
                                    Indisponible
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-heart fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">Aucun favori</h3>
            <p class="text-muted">Vous n'avez pas encore ajouté de produits à vos favoris.</p>
            <a href="{{ url_for('online_ordering_sites.menu') }}" class="btn btn-primary">
                <i class="fas fa-utensils"></i> Découvrir le menu
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
    .product-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
    
    .favorite-btn {
        border: none !important;
        padding: 0.25rem 0.5rem;
    }
    
    .favorite-btn:hover {
        background-color: #dc3545;
        color: white;
    }
    
    .quantity-btn {
        width: 35px;
        padding: 0.25rem 0;
    }
    
    .quantity-input {
        border-left: 0;
        border-right: 0;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quantity controls
    document.addEventListener('click', function(e) {
        if (e.target.closest('.quantity-btn')) {
            const btn = e.target.closest('.quantity-btn');
            const action = btn.getAttribute('data-action');
            const input = btn.parentElement.querySelector('.quantity-input');
            let value = parseInt(input.value);
            
            if (action === 'increase' && value < 10) {
                input.value = value + 1;
            } else if (action === 'decrease' && value > 1) {
                input.value = value - 1;
            }
        }
    });
    
    // Add to cart functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.add-to-cart-btn')) {
            const btn = e.target.closest('.add-to-cart-btn');
            const productId = btn.getAttribute('data-product-id');
            const productName = btn.getAttribute('data-product-name');
            const productPrice = parseFloat(btn.getAttribute('data-product-price'));
            const quantityInput = btn.parentElement.querySelector('.quantity-input');
            const quantity = parseInt(quantityInput.value);
            
            addToCart(productId, productName, productPrice, quantity);
            
            // Reset quantity to 1
            quantityInput.value = 1;
            
            // Visual feedback
            btn.innerHTML = '<i class="fas fa-check me-1"></i>Ajouté !';
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-success');
            
            setTimeout(() => {
                btn.innerHTML = '<i class="fas fa-cart-plus me-1"></i>Ajouter';
                btn.classList.remove('btn-success');
                btn.classList.add('btn-primary');
            }, 1500);
        }
    });
    
    // Favorite toggle functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.favorite-btn')) {
            const btn = e.target.closest('.favorite-btn');
            const productId = btn.getAttribute('data-product-id');
            
            toggleFavorite(productId, btn);
        }
    });
});

function toggleFavorite(productId, btn) {
    fetch('/api/toggle_favorite', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            if (data.action === 'removed') {
                // Retirer la carte du DOM avec animation
                const card = btn.closest('.col-xl-3, .col-lg-4, .col-md-6, .col-sm-6');
                card.style.transition = 'opacity 0.3s ease';
                card.style.opacity = '0';
                setTimeout(() => {
                    card.remove();
                    
                    // Vérifier s'il reste des favoris
                    const remainingCards = document.querySelectorAll('.product-card').length;
                    if (remainingCards === 0) {
                        location.reload(); // Recharger pour afficher le message "Aucun favori"
                    }
                }, 300);
            }
            
            // Afficher un message de succès
            showToast(data.message, 'success');
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showToast('Erreur lors de la mise à jour des favoris', 'error');
    });
}

function showToast(message, type) {
    // Créer un toast simple
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(toast);
    
    // Supprimer automatiquement après 3 secondes
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}
</script>
{% endblock %}
