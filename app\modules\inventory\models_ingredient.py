from app import db
from datetime import datetime
from flask_login import current_user
from app.modules.inventory.models_stock_movement import StockMovement, StockMovementType, StockMovementReason

class IngredientCategory(db.Model):
    __tablename__ = 'ingredient_categories'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    description = db.Column(db.Text)
    image_path = db.Column(db.String(255))
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    ingredients = db.relationship('Ingredient', backref='category', lazy='dynamic')
    
    def __repr__(self):
        return f'<IngredientCategory {self.name}>'

class Ingredient(db.Model):
    __tablename__ = 'ingredients'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    description = db.Column(db.Text)
    category_id = db.Column(db.Integer, db.ForeignKey('ingredient_categories.id'))
    unit = db.Column(db.String(20), default='unit')  # kg, l, unit, etc.
    price_per_unit = db.Column(db.Float, default=0)
    stock_quantity = db.Column(db.Float, default=0)
    minimum_stock = db.Column(db.Float, default=0)
    expiry_date = db.Column(db.Date)
    image_path = db.Column(db.String(255))
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    recipe_items = db.relationship('RecipeItem', back_populates='ingredient', lazy='dynamic')
    
    def update_stock(self, quantity, operation='subtract', reason=None, reference=None, notes=None, commit=True, user_id=None, customer_id=None):
        """Mettre à jour le stock et enregistrer le mouvement"""
        from flask_login import current_user

        # Gérer le cas où current_user est Anonymous (utilisateur non connecté)
        if user_id is None and customer_id is None:
            if hasattr(current_user, 'id') and current_user.id:
                user_id = current_user.id
            else:
                # Pour les commandes anonymes, utiliser None
                user_id = None

        previous_quantity = self.stock_quantity

        if operation == 'add':
            self.stock_quantity += quantity
            movement_type = StockMovementType.IN
        else:  # subtract
            if self.stock_quantity >= quantity:
                self.stock_quantity -= quantity
                movement_type = StockMovementType.OUT
            else:
                return False

        # Créer le mouvement de stock
        movement = StockMovement(
            owner_id=self.owner_id,
            user_id=user_id,
            customer_id=customer_id,
            ingredient_id=self.id,
            type=movement_type,
            reason=reason or StockMovementReason.ADJUSTMENT,
            quantity=quantity,
            previous_quantity=previous_quantity,
            new_quantity=self.stock_quantity,
            reference=reference,
            notes=notes
        )

        db.session.add(movement)
        if commit:
            db.session.commit()
        return True
    
    def is_low_stock(self):
        """Vérifier si le stock est bas"""
        return self.stock_quantity <= self.minimum_stock
    
    def is_out_of_stock(self):
        """Vérifier si l'ingrédient est en rupture de stock"""
        return self.stock_quantity <= 0
    
    def is_expired(self):
        """Vérifier si l'ingrédient est expiré"""
        if self.expiry_date:
            return self.expiry_date <= datetime.now().date()
        return False
    
    def get_stock_status(self):
        """Obtenir le statut du stock"""
        if self.is_out_of_stock():
            return 'out_of_stock'
        elif self.is_low_stock():
            return 'low_stock'
        return 'in_stock'
    
    def get_expiry_status(self):
        """Obtenir le statut d'expiration"""
        if not self.expiry_date:
            return 'no_expiry'
        elif self.is_expired():
            return 'expired'
        else:
            days_until_expiry = (self.expiry_date - datetime.now().date()).days
            if days_until_expiry <= 7:
                return 'expiring_soon'
            return 'valid'
    
    def __repr__(self):
        return f'<Ingredient {self.name}>' 