{% extends "base.html" %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">{{ title }}</h1>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.name.label(class="form-label") }}
                            {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                            {% for error in form.name.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows=3) }}
                            {% for error in form.description.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.image.label(class="form-label") }}
                            {{ form.image(class="form-control" + (" is-invalid" if form.image.errors else ""), onchange="previewImage(this)") }}
                            {% for error in form.image.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                            <div class="form-text">Formats acceptés : PNG, JPG, JPEG, GIF. Taille maximale : 5 MB</div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                            <a href="{{ url_for('inventory.ingredient_categories') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Aperçu de l'image</h5>
                </div>
                <div class="card-body">
                    <div id="imagePreview" class="text-center">
                        {% if category and category.image_path %}
                        <img src="{{ url_for('static', filename=category.image_path) }}" 
                             alt="Aperçu" 
                             class="img-fluid mb-3" 
                             style="max-height: 300px;">
                        <form action="{{ url_for('inventory.delete_ingredient_category_image', id=category.id) }}" 
                              method="POST" 
                              onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette image ?');">
                            <button type="submit" class="btn btn-danger btn-sm">
                                <i class="fas fa-trash"></i> Supprimer l'image
                            </button>
                        </form>
                        {% else %}
                        <div class="bg-light d-flex align-items-center justify-content-center" style="height: 300px;">
                            <i class="fas fa-image fa-4x text-secondary"></i>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `
                <img src="${e.target.result}" 
                     alt="Aperçu" 
                     class="img-fluid mb-3" 
                     style="max-height: 300px;">
            `;
        }
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.innerHTML = `
            <div class="bg-light d-flex align-items-center justify-content-center" style="height: 300px;">
                <i class="fas fa-image fa-4x text-secondary"></i>
            </div>
        `;
    }
}
</script>
{% endblock %} 