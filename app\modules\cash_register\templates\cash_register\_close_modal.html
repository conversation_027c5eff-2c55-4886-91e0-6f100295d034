<div class="modal fade" id="closeRegisterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{{ url_for('cash_register.close_register') }}" id="closeRegisterForm">
                {{ close_form.csrf_token }}
                <div class="modal-header">
                    <h5 class="modal-title">Fermeture de caisse</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            Assurez-vous d'avoir bien compté votre caisse avant de la fermer.
                            Le montant final doit correspondre au solde actuel.
                        </div>
                        <label class="form-label">{{ close_form.final_amount.label }}</label>
                        {{ close_form.final_amount(class="form-control", type="number", step="0.01", value=register.current_balance if register else 0) }}
                        <small class="text-muted">
                            {% if register and register.float_amount > 0 %}
                            Le fond de caisse ({{ format_currency(register.float_amount) }}) sera conservé,
                            le reste sera transféré à la trésorerie.
                            {% else %}
                            La totalité sera transférée à la trésorerie.
                            {% endif %}
                        </small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ close_form.note.label }}</label>
                        {{ close_form.note(class="form-control", rows=3) }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-danger">Fermer la caisse</button>
                </div>
            </form>
        </div>
    </div>
</div>
