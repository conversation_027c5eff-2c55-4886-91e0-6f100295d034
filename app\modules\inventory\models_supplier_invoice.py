from app import db
from datetime import datetime, timedelta
import enum

class PaymentStatus(enum.Enum):
    """Statuts de paiement des factures"""
    PENDING = "pending"           # En attente de paiement
    PAID = "paid"                # Payée
    PARTIAL = "partial"          # Partiellement payée
    OVERDUE = "overdue"          # En retard
    CANCELLED = "cancelled"       # Annulée

    def __str__(self):
        return self.value

    @classmethod
    def from_str(cls, value):
        try:
            return cls(value.lower())
        except ValueError:
            return cls.PENDING

class SupplierPaymentMethod(enum.Enum):
    """Méthodes de paiement pour les fournisseurs"""
    CASH_CAISSE = "cash_caisse"                    # Cash depuis la caisse
    CHEQUE_COMPTE_BANQUE = "cheque_compte_banque"  # Chèque depuis compte banque
    VIREMENT_COMPTE_BANQUE = "virement_depuis_compte_banque"  # Virement bancaire
    SORTIE_CASH_BANQUE = "sortie_cash_banque"      # Sortie cash banque
    OTHER = "other"                                # Autre

    def __str__(self):
        return self.value

    @classmethod
    def from_str(cls, value):
        try:
            return cls(value.lower())
        except ValueError:
            return cls.OTHER

class SupplierInvoice(db.Model):
    """Modèle pour les factures fournisseurs"""
    __tablename__ = 'supplier_invoices'
    
    id = db.Column(db.Integer, primary_key=True)
    reference = db.Column(db.String(50), nullable=False)  # Référence de la facture
    supplier_reference = db.Column(db.String(50))  # Référence du fournisseur
    
    # Relations
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    purchase_order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'), nullable=True)
    
    # Montants
    amount = db.Column(db.Float, nullable=False)
    paid_amount = db.Column(db.Float, default=0)
    tax_amount = db.Column(db.Float, default=0)
    
    # Statut et paiement
    payment_status = db.Column(db.Enum(PaymentStatus), default=PaymentStatus.PENDING)
    payment_method = db.Column(db.Enum(SupplierPaymentMethod), nullable=True)
    
    # Dates
    invoice_date = db.Column(db.DateTime, default=datetime.utcnow)
    due_date = db.Column(db.DateTime)
    payment_date = db.Column(db.DateTime)
    
    # Informations complémentaires
    description = db.Column(db.Text)
    notes = db.Column(db.Text)
    
    # Utilisateurs
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    supplier = db.relationship('Supplier', backref='invoices')
    user = db.relationship('User', foreign_keys=[user_id], backref='created_supplier_invoices')
    owner = db.relationship('User', foreign_keys=[owner_id], backref='owned_supplier_invoices')
    payments = db.relationship('SupplierPayment', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<SupplierInvoice {self.reference}>'
    
    @property
    def supplier_name(self):
        """Nom du fournisseur"""
        return self.supplier.name if self.supplier else 'Fournisseur inconnu'
    
    @property
    def remaining_amount(self):
        """Montant restant à payer"""
        return self.amount - self.paid_amount
    
    @property
    def is_overdue(self):
        """Vérifie si la facture est en retard"""
        if self.due_date and self.payment_status == PaymentStatus.PENDING:
            return datetime.utcnow() > self.due_date
        return False
    
    @property
    def days_overdue(self):
        """Nombre de jours de retard"""
        if self.is_overdue:
            return (datetime.utcnow() - self.due_date).days
        return 0
    
    def update_payment_status(self):
        """Met à jour le statut de paiement basé sur le montant payé"""
        if self.paid_amount >= self.amount:
            self.payment_status = PaymentStatus.PAID
            if not self.payment_date:
                self.payment_date = datetime.utcnow()
        elif self.paid_amount > 0:
            self.payment_status = PaymentStatus.PARTIAL
        elif self.is_overdue:
            self.payment_status = PaymentStatus.OVERDUE
        else:
            self.payment_status = PaymentStatus.PENDING
        
        db.session.commit()
    
    def add_payment(self, amount, method, notes=None, user_id=None):
        """Ajoute un paiement à la facture"""
        if amount <= 0:
            return False, "Le montant doit être positif"
        
        if amount > self.remaining_amount:
            return False, "Le montant dépasse le solde restant"
        
        payment = SupplierPayment(
            invoice=self,
            amount=amount,
            payment_method=method,
            notes=notes,
            user_id=user_id or self.user_id,
            owner_id=self.owner_id
        )
        
        db.session.add(payment)
        self.paid_amount += amount
        self.update_payment_status()
        
        return True, "Paiement ajouté avec succès"
    
    @staticmethod
    def generate_reference():
        """Génère une référence unique pour la facture"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        count = SupplierInvoice.query.count() + 1
        return f"INV{timestamp}{count:04d}"

class SupplierPayment(db.Model):
    """Modèle pour les paiements de factures fournisseurs"""
    __tablename__ = 'supplier_payments'
    
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('supplier_invoices.id'), nullable=False)
    
    # Montant et méthode
    amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.Enum(SupplierPaymentMethod), nullable=False)
    
    # Informations de paiement
    reference = db.Column(db.String(100))  # Référence du paiement (chèque, virement, etc.)
    bank_account_id = db.Column(db.Integer, db.ForeignKey('bank_accounts.id'), nullable=True)
    cash_register_operation_id = db.Column(db.Integer, nullable=True)  # Lien vers l'opération de caisse
    
    # Informations complémentaires
    notes = db.Column(db.Text)
    
    # Utilisateurs
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Timestamps
    payment_date = db.Column(db.DateTime, default=datetime.utcnow)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    bank_account = db.relationship('BankAccount', backref='supplier_payments')
    user = db.relationship('User', foreign_keys=[user_id], backref='created_supplier_payments')
    owner = db.relationship('User', foreign_keys=[owner_id], backref='owned_supplier_payments')
    
    def __repr__(self):
        return f'<SupplierPayment {self.amount}€ via {self.payment_method}>'
    
    @property
    def payment_method_display(self):
        """Affichage lisible de la méthode de paiement"""
        method_names = {
            SupplierPaymentMethod.CASH_CAISSE: "Cash depuis caisse",
            SupplierPaymentMethod.CHEQUE_COMPTE_BANQUE: "Chèque bancaire",
            SupplierPaymentMethod.VIREMENT_COMPTE_BANQUE: "Virement bancaire",
            SupplierPaymentMethod.SORTIE_CASH_BANQUE: "Sortie cash banque",
            SupplierPaymentMethod.OTHER: "Autre"
        }
        return method_names.get(self.payment_method, "Inconnu")
    
    def process_cash_register_operation(self):
        """Traite l'opération de caisse pour les paiements cash"""
        if self.payment_method == SupplierPaymentMethod.CASH_CAISSE:
            from app.modules.cash_register.models_cash_register import CashRegister, CashOperation, CashRegisterOperationType
            
            # Récupérer la caisse actuelle
            cash_register = CashRegister.get_current(self.owner_id)
            if not cash_register or not cash_register.is_open:
                return False, "La caisse doit être ouverte pour effectuer ce paiement"
            
            # Calculer le solde total EXACTEMENT comme dans la page caisse
            total_sales = 0
            total_deposits = 0
            total_withdrawals = 0

            if cash_register.last_opened_at:
                all_operations = CashOperation.query.filter(
                    CashOperation.register_id == cash_register.id,
                    CashOperation.date >= cash_register.last_opened_at
                ).all()

                for op in all_operations:
                    if op.type == CashRegisterOperationType.SALE:
                        total_sales += op.amount
                    elif op.type == CashRegisterOperationType.CASH_IN:
                        total_deposits += op.amount
                    elif op.type == CashRegisterOperationType.CASH_OUT:
                        total_withdrawals += abs(op.amount)
                    elif op.type == CashRegisterOperationType.SUPPLIER_PAYMENT:
                        total_withdrawals += abs(op.amount)
                    elif op.type == CashRegisterOperationType.BANK_DEPOSIT:
                        total_withdrawals += abs(op.amount)

            # Calcul final identique à la page caisse
            total_balance = cash_register.float_amount + total_sales + total_deposits - total_withdrawals

            if self.amount > total_balance:
                return False, f"Solde insuffisant en caisse (disponible: {total_balance:.2f}€)"
            
            # Créer l'opération de sortie de caisse
            operation = CashOperation(
                register_id=cash_register.id,
                type=CashRegisterOperationType.SUPPLIER_PAYMENT,
                amount=-self.amount,  # Montant négatif pour une sortie
                note=f"Paiement fournisseur - Facture {self.invoice.reference}",
                user_id=self.user_id,
                owner_id=self.owner_id
            )
            
            # Mettre à jour le solde de la caisse
            cash_register.current_balance -= self.amount
            
            db.session.add(operation)
            self.cash_register_operation_id = operation.id
            db.session.commit()
            
            return True, "Paiement traité avec succès"
        
        return True, "Aucune opération de caisse nécessaire"
    
    def process_bank_operation(self):
        """Traite l'opération bancaire pour les paiements bancaires"""
        if self.payment_method in [
            SupplierPaymentMethod.CHEQUE_COMPTE_BANQUE,
            SupplierPaymentMethod.VIREMENT_COMPTE_BANQUE,
            SupplierPaymentMethod.SORTIE_CASH_BANQUE
        ]:
            if not self.bank_account:
                return False, "Un compte bancaire doit être spécifié"
            
            if self.amount > self.bank_account.balance:
                return False, "Solde bancaire insuffisant"
            
            # Mettre à jour le solde bancaire
            self.bank_account.balance -= self.amount
            
            # Créer l'opération bancaire
            from app.modules.inventory.models_bank_account import BankOperation, BankOperationType
            
            operation = BankOperation(
                bank_account=self.bank_account,
                type=BankOperationType.SUPPLIER_PAYMENT,
                amount=-self.amount,
                description=f"Paiement fournisseur - Facture {self.invoice.reference}",
                reference=self.reference,
                user_id=self.user_id,
                owner_id=self.owner_id
            )
            
            db.session.add(operation)
            db.session.commit()
            
            return True, "Paiement bancaire traité avec succès"
        
        return True, "Aucune opération bancaire nécessaire"
