import os
import uuid
from werkzeug.utils import secure_filename
from flask import current_app
from PIL import Image

def allowed_file(filename):
    """Vérifie si le fichier a une extension autorisée"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def save_table_image(file, table_id=None):
    """
    Sauvegarde une image de table et retourne le nom du fichier
    
    Args:
        file: Fichier uploadé
        table_id: ID de la table (optionnel, pour nommer le fichier)
    
    Returns:
        str: Nom du fichier sauvegardé ou None si erreur
    """
    if not file or not allowed_file(file.filename):
        return None
    
    # Créer le dossier s'il n'existe pas
    upload_folder = os.path.join(current_app.static_folder, 'uploads', 'tables')
    os.makedirs(upload_folder, exist_ok=True)
    
    # Générer un nom de fichier unique
    file_extension = file.filename.rsplit('.', 1)[1].lower()
    if table_id:
        filename = f"table_{table_id}_{uuid.uuid4().hex[:8]}.{file_extension}"
    else:
        filename = f"table_{uuid.uuid4().hex}.{file_extension}"
    
    filename = secure_filename(filename)
    file_path = os.path.join(upload_folder, filename)
    
    try:
        # Sauvegarder le fichier
        file.save(file_path)
        
        # Redimensionner l'image pour optimiser l'affichage
        optimize_table_image(file_path)
        
        return filename
    except Exception as e:
        current_app.logger.error(f"Erreur lors de la sauvegarde de l'image: {str(e)}")
        return None

def optimize_table_image(file_path, max_size=(200, 200)):
    """
    Optimise une image de table en la redimensionnant
    
    Args:
        file_path: Chemin vers le fichier image
        max_size: Taille maximale (largeur, hauteur)
    """
    try:
        with Image.open(file_path) as img:
            # Convertir en RGB si nécessaire (pour les PNG avec transparence)
            if img.mode in ('RGBA', 'LA', 'P'):
                # Créer un fond blanc pour les images avec transparence
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            
            # Redimensionner en gardant les proportions
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # Sauvegarder l'image optimisée
            img.save(file_path, 'JPEG', quality=85, optimize=True)
            
    except Exception as e:
        current_app.logger.error(f"Erreur lors de l'optimisation de l'image: {str(e)}")

def delete_table_image(filename):
    """
    Supprime une image de table
    
    Args:
        filename: Nom du fichier à supprimer
    
    Returns:
        bool: True si supprimé avec succès, False sinon
    """
    if not filename:
        return True
    
    try:
        file_path = os.path.join(current_app.static_folder, 'uploads', 'tables', filename)
        if os.path.exists(file_path):
            os.remove(file_path)
        return True
    except Exception as e:
        current_app.logger.error(f"Erreur lors de la suppression de l'image: {str(e)}")
        return False

def get_table_image_url(filename):
    """
    Retourne l'URL d'une image de table
    
    Args:
        filename: Nom du fichier image
    
    Returns:
        str: URL de l'image ou None
    """
    if not filename:
        return None
    
    return f"/static/uploads/tables/{filename}"
