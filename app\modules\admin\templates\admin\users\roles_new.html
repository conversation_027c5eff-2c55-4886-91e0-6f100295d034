{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <h1>Rôles et permissions</h1>
    
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5>Total des rôles</h5>
                    <p class="h2">{{ roles|length }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5>Total des utilisateurs</h5>
                    <p class="h2">{{ total_users }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5>Rôle le plus utilisé</h5>
                    <p class="h2">{{ most_used_role.value if most_used_role else 'Aucun' }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5>Permissions moyennes</h5>
                    <p class="h2">{{ "%.1f"|format(average_permissions) }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-body">
            <table class="table">
                <thead>
                    <tr>
                        <th>Rôle</th>
                        <th>Utilisateurs</th>
                        <th>Permissions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for role in roles %}
                    <tr>
                        <td>{{ role.value }}</td>
                        <td>
                            {{ role_counts[role] }}
                            <div class="progress" style="height: 4px;">
                                <div class="progress-bar" 
                                     role="progressbar" 
                                     style="width: {{ (role_counts[role] / max_users_count * 100)|round if max_users_count > 0 else 0 }}%">
                                </div>
                            </div>
                        </td>
                        <td>
                            {% for permission in role_permissions[role] %}
                            <span class="badge bg-secondary">{{ permission }}</span>
                            {% endfor %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}