from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, FloatField, IntegerField, SelectField, TextAreaField, SubmitField, DateTimeField, BooleanField, HiddenField
from wtforms.validators import <PERSON>Required, NumberRange, Optional, Length
from wtforms.widgets import TextArea
from app.modules.inventory.models_supplier import Supplier
from app.modules.inventory.models_supplier_category import SupplierCategory
from app.modules.inventory.models_product import Product, ProductCategory
from app.modules.inventory.models_ingredient import Ingredient, IngredientCategory
from app.modules.inventory.models_supplier_invoice import SupplierPaymentMethod
from app.modules.inventory.models_bank_account import BankAccount
from flask_login import current_user

class StockReplenishmentModeForm(FlaskForm):
    """Formulaire pour sélectionner le mode d'approvisionnement"""
    mode = SelectField('Mode d\'approvisionnement', 
                      choices=[('form', 'Mode Formulaire'), ('pos', 'Mode POS')],
                      default='form',
                      validators=[DataRequired()])
    submit = SubmitField('Continuer')

class StockReplenishmentFormMode(FlaskForm):
    """Formulaire pour le mode formulaire d'approvisionnement"""
    # Sélection du fournisseur (optionnel)
    supplier_category_id = SelectField('Catégorie de fournisseur', 
                                     coerce=int, 
                                     validators=[Optional()])
    supplier_id = SelectField('Fournisseur', 
                             coerce=int, 
                             validators=[Optional()])
    
    # Type d'article
    item_type = SelectField('Type d\'article',
                           choices=[('product', 'Produit sans recette'), ('ingredient', 'Ingrédient')],
                           validators=[DataRequired()])
    
    # Sélection de l'article
    product_id = SelectField('Produit', coerce=int, validators=[Optional()])
    ingredient_id = SelectField('Ingrédient', coerce=int, validators=[Optional()])
    
    # Quantité et prix
    quantity = FloatField('Quantité', 
                         validators=[DataRequired(), NumberRange(min=0.01)])
    unit_price = FloatField('Prix unitaire', 
                           validators=[DataRequired(), NumberRange(min=0)])
    
    # Dates
    expected_delivery_date = DateTimeField('Date de livraison prévue', 
                                         validators=[Optional()])
    
    # Informations complémentaires
    notes = TextAreaField('Notes', validators=[Optional(), Length(max=500)])
    
    # Options de traitement
    is_order_only = BooleanField('Bon de commande seulement (ne pas mettre à jour le stock)')
    
    submit = SubmitField('Ajouter à la commande')

class PurchaseOrderPaymentForm(FlaskForm):
    """Formulaire pour le paiement d'une commande d'achat"""
    payment_method = SelectField('Méthode de paiement',
                                choices=[(method.value, method.value.replace('_', ' ').title()) 
                                        for method in SupplierPaymentMethod],
                                validators=[DataRequired()])
    
    # Pour les paiements bancaires
    bank_account_id = SelectField('Compte bancaire', coerce=int, validators=[Optional()])
    
    # Référence du paiement (chèque, virement, etc.)
    payment_reference = StringField('Référence du paiement', validators=[Optional(), Length(max=100)])
    
    # Notes
    payment_notes = TextAreaField('Notes sur le paiement', validators=[Optional(), Length(max=500)])
    
    submit = SubmitField('Effectuer le paiement')
    
    def __init__(self, *args, **kwargs):
        super(PurchaseOrderPaymentForm, self).__init__(*args, **kwargs)
        
        # Charger les comptes bancaires de l'utilisateur
        if current_user.is_authenticated:
            bank_accounts = BankAccount.query.filter_by(
                owner_id=current_user.id, 
                is_active=True
            ).all()
            self.bank_account_id.choices = [(0, 'Sélectionner un compte')] + [
                (account.id, f"{account.name} ({account.balance:.2f}€)")
                for account in bank_accounts
            ]

class SupplierInvoiceForm(FlaskForm):
    """Formulaire pour créer/modifier une facture fournisseur"""
    supplier_reference = StringField('Référence fournisseur', 
                                    validators=[Optional(), Length(max=50)])
    amount = FloatField('Montant', 
                       validators=[DataRequired(), NumberRange(min=0.01)])
    tax_amount = FloatField('Montant TVA', 
                           validators=[Optional(), NumberRange(min=0)])
    due_date = DateTimeField('Date d\'échéance', validators=[Optional()])
    description = TextAreaField('Description', validators=[Optional(), Length(max=500)])
    notes = TextAreaField('Notes', validators=[Optional(), Length(max=500)])
    
    submit = SubmitField('Créer la facture')

class BulkSupplierPaymentForm(FlaskForm):
    """Formulaire pour payer plusieurs factures d'un fournisseur"""
    supplier_id = HiddenField('Fournisseur', validators=[DataRequired()])
    invoice_ids = HiddenField('IDs des factures', validators=[DataRequired()])
    
    payment_method = SelectField('Méthode de paiement',
                                choices=[(method.value, method.value.replace('_', ' ').title()) 
                                        for method in SupplierPaymentMethod],
                                validators=[DataRequired()])
    
    bank_account_id = SelectField('Compte bancaire', coerce=int, validators=[Optional()])
    payment_reference = StringField('Référence du paiement', validators=[Optional(), Length(max=100)])
    payment_notes = TextAreaField('Notes sur le paiement', validators=[Optional(), Length(max=500)])
    
    submit = SubmitField('Payer les factures sélectionnées')
    
    def __init__(self, *args, **kwargs):
        super(BulkSupplierPaymentForm, self).__init__(*args, **kwargs)
        
        # Charger les comptes bancaires de l'utilisateur
        if current_user.is_authenticated:
            bank_accounts = BankAccount.query.filter_by(
                owner_id=current_user.id, 
                is_active=True
            ).all()
            self.bank_account_id.choices = [(0, 'Sélectionner un compte')] + [
                (account.id, f"{account.name} ({account.balance:.2f}€)")
                for account in bank_accounts
            ]

class POSStockReplenishmentForm(FlaskForm):
    """Formulaire pour le mode POS d'approvisionnement (données JSON)"""
    # Données du ticket en JSON
    cart_data = HiddenField('Données du panier', validators=[DataRequired()])
    
    # Fournisseur sélectionné (optionnel)
    supplier_id = HiddenField('Fournisseur')
    
    # Total de la commande
    total_amount = FloatField('Montant total', validators=[DataRequired(), NumberRange(min=0)])
    
    # Notes générales
    notes = TextAreaField('Notes générales', validators=[Optional(), Length(max=500)])
    
    # Type de traitement
    processing_type = SelectField('Type de traitement',
                                 choices=[
                                     ('receive_and_pay', 'Recevoir et payer maintenant'),
                                     ('receive_pay_later', 'Recevoir et payer plus tard'),
                                     ('order_only', 'Bon de commande seulement')
                                 ],
                                 default='receive_and_pay',
                                 validators=[DataRequired()])
    
    submit = SubmitField('Valider la commande')

class QuickStockAdjustmentForm(FlaskForm):
    """Formulaire pour les ajustements rapides de stock"""
    product_id = SelectField('Produit',
                            choices=[],
                            validators=[Optional()],
                            coerce=int)

    ingredient_id = SelectField('Ingrédient',
                               choices=[],
                               validators=[Optional()],
                               coerce=int)

    adjustment_type = SelectField('Type d\'ajustement',
                                 choices=[
                                     ('increase', 'Augmenter le stock'),
                                     ('decrease', 'Diminuer le stock'),
                                     ('set', 'Définir le stock')
                                 ],
                                 validators=[DataRequired()])

    quantity = FloatField('Quantité',
                         validators=[DataRequired(), NumberRange(min=0.01)])

    reason = StringField('Raison', validators=[DataRequired(), Length(max=200)])
    notes = TextAreaField('Notes', validators=[Optional(), Length(max=500)])

    submit = SubmitField('Appliquer l\'ajustement')

def populate_form_choices():
    """Fonction pour peupler les choix des formulaires"""
    def populate_supplier_choices(form):
        """Peuple les choix de fournisseurs"""
        if current_user.is_authenticated:
            # Catégories de fournisseurs
            categories = SupplierCategory.query.filter_by(
                owner_id=current_user.id, 
                is_active=True
            ).all()
            form.supplier_category_id.choices = [(0, 'Toutes les catégories')] + [
                (cat.id, cat.name) for cat in categories
            ]
            
            # Fournisseurs
            suppliers = Supplier.query.filter_by(
                owner_id=current_user.id, 
                is_active=True
            ).all()
            form.supplier_id.choices = [(0, 'Aucun fournisseur')] + [
                (supplier.id, supplier.name) for supplier in suppliers
            ]
    
    def populate_product_choices(form):
        """Peuple les choix de produits sans recettes"""
        if current_user.is_authenticated:
            products = Product.query.filter_by(
                owner_id=current_user.id,
                has_recipe=False,
                is_active=True
            ).all()
            form.product_id.choices = [(0, 'Sélectionner un produit')] + [
                (product.id, f"{product.name} (Stock: {product.stock_quantity})")
                for product in products
            ]
    
    def populate_ingredient_choices(form):
        """Peuple les choix d'ingrédients"""
        if current_user.is_authenticated:
            ingredients = Ingredient.query.filter_by(
                owner_id=current_user.id
            ).all()
            form.ingredient_id.choices = [(0, 'Sélectionner un ingrédient')] + [
                (ingredient.id, f"{ingredient.name} (Stock: {ingredient.stock_quantity})")
                for ingredient in ingredients
            ]
    
    return {
        'populate_supplier_choices': populate_supplier_choices,
        'populate_product_choices': populate_product_choices,
        'populate_ingredient_choices': populate_ingredient_choices
    }
