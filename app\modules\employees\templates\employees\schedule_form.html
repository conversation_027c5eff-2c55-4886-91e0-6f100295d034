{% extends "employees/base_hr.html" %}

{% block title %}{{ title }}{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-calendar me-2"></i>{{ title }}
        <small class="text-muted">{{ employee.full_name }}</small>
    </h1>
    <a href="{{ url_for('employees.detail', id=employee.id) }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>Retour
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>Informations de Planning
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.day_of_week.label(class="form-label") }}
                            {{ form.day_of_week(class="form-select" + (" is-invalid" if form.day_of_week.errors else "")) }}
                            {% if form.day_of_week.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.day_of_week.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.shift_type.label(class="form-label") }}
                            {{ form.shift_type(class="form-select" + (" is-invalid" if form.shift_type.errors else "")) }}
                            {% if form.shift_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.shift_type.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.start_time.label(class="form-label") }}
                            {{ form.start_time(class="form-control" + (" is-invalid" if form.start_time.errors else "")) }}
                            {% if form.start_time.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.start_time.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.end_time.label(class="form-label") }}
                            {{ form.end_time(class="form-control" + (" is-invalid" if form.end_time.errors else "")) }}
                            {% if form.end_time.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.end_time.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.start_date.label(class="form-label") }}
                            {{ form.start_date(class="form-control" + (" is-invalid" if form.start_date.errors else "")) }}
                            {% if form.start_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.start_date.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.end_date.label(class="form-label") }}
                            {{ form.end_date(class="form-control" + (" is-invalid" if form.end_date.errors else "")) }}
                            {% if form.end_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.end_date.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_recurring(class="form-check-input" + (" is-invalid" if form.is_recurring.errors else "")) }}
                            {{ form.is_recurring.label(class="form-check-label") }}
                            {% if form.is_recurring.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.is_recurring.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <small class="form-text text-muted">Si coché, ce planning se répétera chaque semaine</small>
                    </div>

                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="3") }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('employees.detail', id=employee.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');
    const shiftTypeSelect = document.getElementById('shift_type');

    // Prédéfinir les horaires selon le type de shift
    shiftTypeSelect.addEventListener('change', function() {
        const shiftType = this.value;
        
        switch(shiftType) {
            case 'MORNING':
                startTimeInput.value = '08:00';
                endTimeInput.value = '16:00';
                break;
            case 'AFTERNOON':
                startTimeInput.value = '14:00';
                endTimeInput.value = '22:00';
                break;
            case 'EVENING':
                startTimeInput.value = '18:00';
                endTimeInput.value = '02:00';
                break;
            case 'NIGHT':
                startTimeInput.value = '22:00';
                endTimeInput.value = '06:00';
                break;
            case 'SPLIT':
                startTimeInput.value = '11:00';
                endTimeInput.value = '15:00';
                break;
        }
    });

    // Validation des heures
    function validateTimes() {
        const startTime = startTimeInput.value;
        const endTime = endTimeInput.value;
        
        if (startTime && endTime) {
            const start = new Date(`2000-01-01T${startTime}`);
            const end = new Date(`2000-01-01T${endTime}`);
            
            // Pour les shifts de nuit, l'heure de fin peut être le lendemain
            if (end <= start && shiftTypeSelect.value !== 'NIGHT' && shiftTypeSelect.value !== 'EVENING') {
                endTimeInput.setCustomValidity('L\'heure de fin doit être après l\'heure de début');
            } else {
                endTimeInput.setCustomValidity('');
            }
        }
    }

    startTimeInput.addEventListener('change', validateTimes);
    endTimeInput.addEventListener('change', validateTimes);
});
</script>
{% endblock %}
