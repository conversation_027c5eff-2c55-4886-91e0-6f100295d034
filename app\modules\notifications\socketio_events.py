"""
Événements SocketIO pour les notifications en temps réel
"""

from flask import request
from flask_socketio import emit, join_room, leave_room, disconnect
from flask_login import current_user
from app.extensions import socketio
from app.modules.notifications.models import Notification, NotificationStatus
from app.modules.online_ordering_sites.models import OnlineOrder
from app import db
import json

# Dictionnaire pour stocker les connexions actives
active_connections = {}

@socketio.on('connect')
def handle_connect():
    """Gérer la connexion d'un client"""
    if current_user.is_authenticated:
        user_id = current_user.id
        user_type = 'restaurant'  # Par défaut pour les utilisateurs du POS
        
        # Ajouter l'utilisateur à sa room personnelle
        room = f"user_{user_type}_{user_id}"
        join_room(room)
        
        # Stocker la connexion
        active_connections[request.sid] = {
            'user_id': user_id,
            'user_type': user_type,
            'room': room
        }
        
        print(f"Utilisateur {user_id} connecté à la room {room}")
        
        # Envoyer les notifications non lues
        send_unread_notifications(user_id, user_type)
        
        emit('connected', {'status': 'success', 'room': room})
    else:
        print("Connexion non authentifiée refusée")
        disconnect()

@socketio.on('connect_customer')
def handle_customer_connect(data):
    """Gérer la connexion d'un client du site de commande"""
    customer_id = data.get('customer_id')
    site_id = data.get('site_id')
    
    if customer_id and site_id:
        # Ajouter le client à sa room personnelle
        room = f"customer_{customer_id}_{site_id}"
        join_room(room)
        
        # Stocker la connexion
        active_connections[request.sid] = {
            'user_id': customer_id,
            'user_type': 'customer',
            'site_id': site_id,
            'room': room
        }
        
        print(f"Client {customer_id} connecté à la room {room}")
        
        # Envoyer les notifications non lues
        send_unread_notifications(customer_id, 'customer')
        
        emit('connected', {'status': 'success', 'room': room})
    else:
        print("Données de connexion client invalides")
        disconnect()

@socketio.on('disconnect')
def handle_disconnect():
    """Gérer la déconnexion d'un client"""
    if request.sid in active_connections:
        connection_info = active_connections[request.sid]
        print(f"Utilisateur {connection_info['user_id']} déconnecté de {connection_info['room']}")
        del active_connections[request.sid]

@socketio.on('mark_notification_read')
def handle_mark_notification_read(data):
    """Marquer une notification comme lue"""
    notification_id = data.get('notification_id')
    
    if notification_id:
        notification = Notification.query.get(notification_id)
        if notification:
            notification.mark_as_read()
            emit('notification_marked_read', {'notification_id': notification_id})

@socketio.on('join_order_room')
def handle_join_order_room(data):
    """Rejoindre la room d'une commande spécifique pour le suivi"""
    order_number = data.get('order_number')
    
    if order_number:
        room = f"order_{order_number}"
        join_room(room)
        emit('joined_order_room', {'order_number': order_number, 'room': room})

def send_unread_notifications(user_id, user_type):
    """Envoyer les notifications non lues à un utilisateur"""
    notifications = Notification.query.filter_by(
        recipient_id=user_id,
        recipient_type=user_type,
        status=NotificationStatus.SENT
    ).order_by(Notification.created_at.desc()).limit(10).all()
    
    notifications_data = []
    for notification in notifications:
        notifications_data.append({
            'id': notification.id,
            'type': notification.type.value,
            'title': notification.title,
            'message': notification.message,
            'created_at': notification.created_at.isoformat(),
            'data': notification.data
        })
    
    emit('unread_notifications', {'notifications': notifications_data})

def broadcast_order_notification(order, notification_type, message):
    """Diffuser une notification de commande"""
    # Notifier le restaurant
    restaurant_room = f"user_restaurant_{order.site.owner_id}"
    socketio.emit('order_notification', {
        'type': notification_type,
        'order_number': order.order_number,
        'order_id': order.id,
        'message': message,
        'customer_name': f"{order.customer.first_name} {order.customer.last_name}",
        'total_amount': float(order.total_amount),
        'order_type': order.order_type.value,
        'timestamp': order.ordered_at.isoformat()
    }, room=restaurant_room)
    
    # Notifier le client
    customer_room = f"customer_{order.customer_id}_{order.site_id}"
    socketio.emit('order_status_update', {
        'order_number': order.order_number,
        'status': order.status.value,
        'message': message,
        'timestamp': order.ordered_at.isoformat()
    }, room=customer_room)
    
    # Notifier tous ceux qui suivent cette commande
    order_room = f"order_{order.order_number}"
    socketio.emit('order_update', {
        'order_number': order.order_number,
        'status': order.status.value,
        'message': message,
        'timestamp': order.ordered_at.isoformat()
    }, room=order_room)

def notify_new_order(order):
    """Notifier une nouvelle commande"""
    broadcast_order_notification(
        order, 
        'new_order', 
        f"Nouvelle commande #{order.order_number} reçue"
    )

def notify_order_status_change(order, old_status, new_status):
    """Notifier un changement de statut de commande"""
    status_messages = {
        'confirmed': 'Votre commande a été confirmée',
        'preparing': 'Votre commande est en préparation',
        'ready': 'Votre commande est prête',
        'out_for_delivery': 'Votre commande est en livraison',
        'delivered': 'Votre commande a été livrée',
        'cancelled': 'Votre commande a été annulée'
    }
    
    message = status_messages.get(new_status, f"Statut mis à jour: {new_status}")
    broadcast_order_notification(order, 'status_change', message)

def get_active_connections_count():
    """Obtenir le nombre de connexions actives"""
    return len(active_connections)

def get_active_users():
    """Obtenir la liste des utilisateurs connectés"""
    users = {}
    for sid, connection in active_connections.items():
        user_type = connection['user_type']
        user_id = connection['user_id']
        
        if user_type not in users:
            users[user_type] = []
        
        if user_id not in users[user_type]:
            users[user_type].append(user_id)
    
    return users
