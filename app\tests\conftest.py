import pytest
import os
import tempfile
import random
import string
from datetime import datetime, timedelta

from app import create_app
from app.extensions import db
from app.modules.auth.models import User, UserRole
from config import TestingConfig

class TestConfig(TestingConfig):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'  # Utilisation de SQLite en mémoire
    WTF_CSRF_ENABLED = False  # Désactivation de CSRF pour les tests
    SECRET_KEY = 'test-key'

@pytest.fixture
def app():
    """Create and configure a new app instance for each test."""
    app = create_app(TestConfig)
    
    with app.app_context():
        db.create_all()  # Création des tables
        yield app  # Test s'exécute ici
        db.session.remove()
        db.drop_all()  # Nettoyage après le test

@pytest.fixture
def client(app):
    """A test client for the app."""
    return app.test_client()

@pytest.fixture
def runner(app):
    """A test runner for the app's Click commands."""
    return app.test_cli_runner()

@pytest.fixture
def test_user(app):
    """Create a test user."""
    with app.app_context():
        user = User(
            username='test_user',
            email='<EMAIL>',
            role=UserRole.MANAGER
        )
        user.set_password('test123')
        db.session.add(user)
        db.session.commit()
        return user 