{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">Paramètres de la Caisse</h3>
                    <a href="{{ url_for('cash_register.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <div class="mb-4">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                Choisissez l'une des deux options suivantes :
                                <ul class="mb-0 mt-2">
                                    <li>Exiger un montant minimum pour l'ouverture de caisse</li>
                                    <li>Utiliser le dernier montant de fermeture et exiger un motif si le montant est différent</li>
                                </ul>
                            </div>
                            
                            <div class="mb-4">
                                <div class="form-check form-switch custom-switch">
                                    {{ form.minimum_amount_required(class="form-check-input", type="checkbox", role="switch", id="minimumAmountSwitch", **{'data-bs-toggle': 'collapse', 'data-bs-target': '#minimumAmountGroup'}) }}
                                    <label class="form-check-label" for="minimumAmountSwitch">
                                        {{ form.minimum_amount_required.label.text }}
                                    </label>
                                </div>
                                
                                <div class="collapse mt-3" id="minimumAmountGroup">
                                    <div class="card card-body bg-light border-0">
                                        <label class="form-label">{{ form.minimum_amount.label }}</label>
                                        <div class="input-group">
                                            <span class="input-group-text">€</span>
                                            {{ form.minimum_amount(class="form-control", type="number", step="0.01", min="0") }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <div class="form-check form-switch custom-switch">
                                    {{ form.use_last_closing_amount(class="form-check-input", type="checkbox", role="switch", id="useLastAmountSwitch") }}
                                    <label class="form-check-label" for="useLastAmountSwitch">
                                        {{ form.use_last_closing_amount.label.text }}
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.custom-switch {
    padding-left: 3.25rem;
}
.custom-switch .form-check-input {
    width: 3rem;
    height: 1.5rem;
    margin-left: -3.25rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
    background-position: left center;
    border-radius: 2rem;
    transition: background-position .15s ease-in-out;
}
.custom-switch .form-check-input:checked {
    background-position: right center;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}
.form-check-label {
    font-size: 1.1rem;
    padding-top: 0.25rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const minimumAmountSwitch = document.querySelector('#minimumAmountSwitch');
    const useLastAmountSwitch = document.querySelector('#useLastAmountSwitch');
    const minimumAmountGroup = document.querySelector('#minimumAmountGroup');
    
    function updateSwitches(activeSwitch, inactiveSwitch) {
        if (activeSwitch.checked) {
            inactiveSwitch.checked = false;
        }
    }
    
    minimumAmountSwitch.addEventListener('change', function() {
        updateSwitches(this, useLastAmountSwitch);
        if (this.checked) {
            minimumAmountGroup.classList.add('show');
        } else {
            minimumAmountGroup.classList.remove('show');
        }
    });
    
    useLastAmountSwitch.addEventListener('change', function() {
        updateSwitches(this, minimumAmountSwitch);
        if (this.checked) {
            minimumAmountGroup.classList.remove('show');
        }
    });
    
    // Initialiser l'affichage du groupe montant minimum
    if (minimumAmountSwitch.checked) {
        minimumAmountGroup.classList.add('show');
    }
});
</script>
{% endblock %} 