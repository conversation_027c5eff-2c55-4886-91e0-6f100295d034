// JavaScript pour la gestion bancaire
const BankManagement = {
    init: function() {
        this.initEventListeners();
        this.initTooltips();
        this.startAutoRefresh();
    },

    initEventListeners: function() {
        // Confirmation pour les actions dangereuses
        document.querySelectorAll('form[onsubmit*="confirm"]').forEach(form => {
            form.addEventListener('submit', function(e) {
                const message = this.getAttribute('onsubmit').match(/confirm\('([^']+)'\)/)[1];
                if (!confirm(message)) {
                    e.preventDefault();
                }
            });
        });

        // Formatage automatique des montants
        document.querySelectorAll('input[type="number"][step="0.01"]').forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value && !isNaN(this.value)) {
                    this.value = parseFloat(this.value).toFixed(2);
                }
            });
        });

        // Validation IBAN
        document.querySelectorAll('input[name="iban"]').forEach(input => {
            input.addEventListener('input', function() {
                this.value = this.value.replace(/[^A-Z0-9]/gi, '').toUpperCase();
                // Ajouter des espaces tous les 4 caractères
                this.value = this.value.replace(/(.{4})/g, '$1 ').trim();
                
                // Validation basique IBAN français
                if (this.value.length >= 2) {
                    const countryCode = this.value.substring(0, 2);
                    if (countryCode === 'FR' && this.value.replace(/\s/g, '').length === 27) {
                        this.classList.add('is-valid');
                        this.classList.remove('is-invalid');
                    } else if (this.value.replace(/\s/g, '').length > 15) {
                        this.classList.add('is-invalid');
                        this.classList.remove('is-valid');
                    } else {
                        this.classList.remove('is-valid', 'is-invalid');
                    }
                }
            });
        });

        // Validation BIC
        document.querySelectorAll('input[name="bic"]').forEach(input => {
            input.addEventListener('input', function() {
                this.value = this.value.replace(/[^A-Z0-9]/gi, '').toUpperCase();
                
                // Validation BIC (8 ou 11 caractères)
                if (this.value.length === 8 || this.value.length === 11) {
                    this.classList.add('is-valid');
                    this.classList.remove('is-invalid');
                } else if (this.value.length > 11) {
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                } else {
                    this.classList.remove('is-valid', 'is-invalid');
                }
            });
        });

        // Gestion des virements
        this.initTransferForm();
    },

    initTransferForm: function() {
        const sourceSelect = document.getElementById('source_account_id');
        const targetSelect = document.getElementById('target_account_id');
        const amountInput = document.getElementById('amount');

        if (sourceSelect && targetSelect) {
            // Empêcher la sélection du même compte source et cible
            sourceSelect.addEventListener('change', function() {
                const sourceValue = this.value;
                Array.from(targetSelect.options).forEach(option => {
                    if (option.value === sourceValue && sourceValue !== '0') {
                        option.disabled = true;
                        if (option.selected) {
                            targetSelect.value = '0';
                        }
                    } else {
                        option.disabled = false;
                    }
                });
            });

            targetSelect.addEventListener('change', function() {
                const targetValue = this.value;
                Array.from(sourceSelect.options).forEach(option => {
                    if (option.value === targetValue && targetValue !== '0') {
                        option.disabled = true;
                        if (option.selected) {
                            sourceSelect.value = '0';
                        }
                    } else {
                        option.disabled = false;
                    }
                });
            });

            // Vérification du solde disponible
            if (amountInput) {
                amountInput.addEventListener('input', function() {
                    const sourceAccountId = sourceSelect.value;
                    const amount = parseFloat(this.value);

                    if (sourceAccountId && sourceAccountId !== '0' && amount > 0) {
                        this.checkAvailableBalance(sourceAccountId, amount);
                    }
                }.bind(this));
            }
        }
    },

    checkAvailableBalance: function(accountId, amount) {
        fetch(`/inventory/bank-management/api/account-balance/${accountId}`)
            .then(response => response.json())
            .then(data => {
                const amountInput = document.getElementById('amount');
                const warningDiv = document.getElementById('balance-warning') || this.createBalanceWarning();

                if (amount > data.available_balance) {
                    amountInput.classList.add('is-invalid');
                    warningDiv.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            Montant supérieur au solde disponible (${data.available_balance.toFixed(2)} €)
                        </div>
                    `;
                    warningDiv.style.display = 'block';
                } else {
                    amountInput.classList.remove('is-invalid');
                    warningDiv.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Erreur lors de la vérification du solde:', error);
            });
    },

    createBalanceWarning: function() {
        const div = document.createElement('div');
        div.id = 'balance-warning';
        div.style.display = 'none';
        
        const amountInput = document.getElementById('amount');
        if (amountInput && amountInput.parentNode) {
            amountInput.parentNode.insertBefore(div, amountInput.nextSibling);
        }
        
        return div;
    },

    initTooltips: function() {
        // Initialiser les tooltips Bootstrap
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },

    startAutoRefresh: function() {
        // Actualisation automatique des soldes toutes les 2 minutes
        setInterval(() => {
            this.refreshAccountBalances();
        }, 120000);
    },

    refreshAccountBalances: function() {
        const balanceElements = document.querySelectorAll('[data-account-id]');
        
        balanceElements.forEach(element => {
            const accountId = element.getAttribute('data-account-id');
            
            fetch(`/inventory/bank-management/api/account-balance/${accountId}`)
                .then(response => response.json())
                .then(data => {
                    // Mettre à jour l'affichage du solde
                    const balanceSpan = element.querySelector('.account-balance');
                    if (balanceSpan) {
                        balanceSpan.textContent = data.balance.toFixed(2) + ' €';
                        balanceSpan.className = `account-balance text-${data.balance >= 0 ? 'success' : 'danger'}`;
                    }

                    // Mettre à jour les alertes de découvert
                    const overdraftAlert = element.querySelector('.overdraft-alert');
                    if (data.is_overdrawn) {
                        if (!overdraftAlert) {
                            const alert = document.createElement('div');
                            alert.className = 'alert alert-warning alert-sm mt-2 overdraft-alert';
                            alert.innerHTML = `
                                <i class="fas fa-exclamation-triangle"></i>
                                Découvert de ${data.overdraft_amount.toFixed(2)} €
                            `;
                            element.appendChild(alert);
                        } else {
                            overdraftAlert.innerHTML = `
                                <i class="fas fa-exclamation-triangle"></i>
                                Découvert de ${data.overdraft_amount.toFixed(2)} €
                            `;
                        }
                    } else if (overdraftAlert) {
                        overdraftAlert.remove();
                    }
                })
                .catch(error => {
                    console.error('Erreur lors de l\'actualisation du solde:', error);
                });
        });
    },

    // Fonctions utilitaires
    formatCurrency: function(amount, currency = '€') {
        return new Intl.NumberFormat('fr-FR', {
            style: 'currency',
            currency: currency === '€' ? 'EUR' : currency
        }).format(amount);
    },

    validateIBAN: function(iban) {
        // Validation IBAN simplifiée
        const cleanIban = iban.replace(/\s/g, '').toUpperCase();
        
        if (cleanIban.length < 15 || cleanIban.length > 34) {
            return false;
        }

        // Vérification du code pays
        const countryCode = cleanIban.substring(0, 2);
        const validCountries = ['FR', 'DE', 'ES', 'IT', 'BE', 'NL', 'LU', 'CH'];
        
        return validCountries.includes(countryCode);
    },

    validateBIC: function(bic) {
        // Validation BIC simplifiée
        const cleanBic = bic.replace(/\s/g, '').toUpperCase();
        return cleanBic.length === 8 || cleanBic.length === 11;
    },

    // Gestion des modales
    showTransferModal: function(sourceAccountId = null, targetAccountId = null) {
        const modal = new bootstrap.Modal(document.getElementById('transferModal'));
        
        if (sourceAccountId) {
            document.getElementById('source_account_id').value = sourceAccountId;
        }
        if (targetAccountId) {
            document.getElementById('target_account_id').value = targetAccountId;
        }
        
        modal.show();
    },

    showDepositModal: function(accountId = null) {
        const modal = new bootstrap.Modal(document.getElementById('depositModal'));
        
        if (accountId) {
            document.getElementById('bank_account_id').value = accountId;
        }
        
        modal.show();
    },

    // Exportation des données
    exportAccountStatement: function(accountId, format = 'csv') {
        const url = `/inventory/bank-management/export/${accountId}?format=${format}`;
        window.open(url, '_blank');
    },

    // Réconciliation
    startReconciliation: function(accountId) {
        window.location.href = `/inventory/bank-management/reconciliation?account_id=${accountId}`;
    }
};

// Fonctions globales pour les événements onclick
function refreshAccountBalances() {
    BankManagement.refreshAccountBalances();
}

function showTransferModal(sourceId, targetId) {
    BankManagement.showTransferModal(sourceId, targetId);
}

function showDepositModal(accountId) {
    BankManagement.showDepositModal(accountId);
}

function exportAccountStatement(accountId, format) {
    BankManagement.exportAccountStatement(accountId, format);
}

function startReconciliation(accountId) {
    BankManagement.startReconciliation(accountId);
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    BankManagement.init();
});
