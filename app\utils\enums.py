from enum import Enum

class UserRole(Enum):
    SYSTEM_ADMIN = 'SYSTEM_ADMIN'  # Administrateur système
    OWNER = 'OWNER'               # Propriétaire
    ADMIN = 'ADMIN'               # Administrateur
    MANAGER = 'MANAGER'           # Manager
    EMPLOYEE = 'EMPLOYEE'         # Employé
    CASHIER = 'CASHIER'          # Caissier
    KITCHEN = 'KITCHEN'          # Personnel de cuisine
    WAITER = 'WAITER'            # Serveur
    ACCOUNTANT = 'ACCOUNTANT'    # Comptable

class CashRegisterOperationType(Enum):
    OPEN = 'open'
    CLOSE = 'close'
    SALE = 'sale'
    REFUND = 'refund'
    WITHDRAWAL = 'withdrawal'
    DEPOSIT = 'deposit'
    ADJUSTMENT = 'adjustment'
    CASH_IN = 'cash_in'
    CASH_OUT = 'cash_out'

class PaymentMethod(Enum):
    CASH = 'cash'
    CARD = 'card'
    TRANSFER = 'transfer'
    CHECK = 'check'
    OTHER = 'other'

class SaleStatus(Enum):
    PENDING = 'pending'
    COMPLETED = 'completed'
    CANCELLED = 'cancelled'
    REFUNDED = 'refunded'

class TableStatus(Enum):
    AVAILABLE = 'available'
    OCCUPIED = 'occupied'
    RESERVED = 'reserved'
    CLEANING = 'cleaning'

class PromotionType(Enum):
    PERCENTAGE = 'percentage'
    FIXED_AMOUNT = 'fixed_amount'
    BUY_X_GET_Y = 'buy_x_get_y'
    BUNDLE = 'bundle'

class CashOutReason(Enum):
    SUPPLIER_PAYMENT = 'supplier_payment'
    EXPENSE = 'expense'
    REFUND = 'refund'
    WITHDRAWAL = 'withdrawal'
    OTHER = 'other' 