/**
 * Module Reports - Gestion des graphiques et tableaux
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM chargé - Initialisation des charts');
    
    // Gestion du filtre de période
    const periodSelect = document.getElementById('period');
    if (periodSelect) {
        periodSelect.addEventListener('change', function() {
            const customDateRange = document.getElementById('customDateRange');
            if (customDateRange) {
                if (this.value === 'custom') {
                    customDateRange.classList.remove('d-none');
                } else {
                    customDateRange.classList.add('d-none');
                }
            }
        });
    }

    // Gestion du filtre de période pour la page d'inventaire
    const periodSelectInventory = document.getElementById('periodSelect');
    if (periodSelectInventory) {
        periodSelectInventory.addEventListener('change', function() {
            const customDateFields = document.getElementById('customDateFields');
            if (customDateFields) {
                if (this.value === 'custom') {
                    customDateFields.style.display = 'block';
                } else {
                    customDateFields.style.display = 'none';
                }
            }
        });

        // Initialiser l'affichage au chargement de la page
        const customDateFields = document.getElementById('customDateFields');
        if (customDateFields && periodSelectInventory.value === 'custom') {
            customDateFields.style.display = 'block';
        }
    }
    
    // Formulaire de mise à jour rapide du stock (page inventory)
    const quickUpdateForm = document.getElementById('quickUpdateForm');
    if (quickUpdateForm) {
        quickUpdateForm.addEventListener('submit', function(e) {
            // Validation
            const quantityInput = document.getElementById('updateQuantity');
            if (quantityInput) {
                const quantity = parseFloat(quantityInput.value);
                if (isNaN(quantity) || quantity <= 0) {
                    e.preventDefault();
                    alert('Veuillez entrer une quantité valide (supérieure à 0).');
                    return false;
                }
            }
            return true;
        });
    }
    
    // Nettoyer les anciens messages d'état avant de réinitialiser les graphiques
    ['salesFlatMessage', 'expenseFlatMessage', 'stockFlatMessage'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.remove();
        }
    });
    
    // Initialiser les graphiques une seule fois
    initializeCharts();
});

// Variable globale pour suivre si les graphiques ont été initialisés
let chartsInitialized = false;

// Fonction utilitaire pour détruire un graphique existant
function destroyExistingChart(chartId) {
    if (window.chartInstances && window.chartInstances[chartId]) {
        window.chartInstances[chartId].destroy();
        delete window.chartInstances[chartId];
    }
}

/**
 * Initialise les graphiques si présents sur la page
 */
function initializeCharts() {
    console.log('Tentative d\'initialisation des graphiques...');
    
    // Éviter l'initialisation multiple
    if (chartsInitialized) {
        console.log('Les graphiques ont déjà été initialisés');
        return;
    }
    
    // Vérification que Chart.js est chargé
    if (typeof Chart === 'undefined') {
        console.error('Chart.js n\'est pas chargé!');
        return;
    }
    
    console.log('Chart.js est bien chargé, version:', Chart.version);
    
    // Initialiser une seule fois
    chartsInitialized = true;
    
    // Délai pour s'assurer que tout est chargé
    setTimeout(() => {
        try {
            // Initialisation du graphique des ventes (page products)
            initSalesTrendChart();
            
            // Initialisation des graphiques de dépenses (page expenses)
            initExpenseTrendChart();
            initExpenseCategoryChart();
            
            // Plus de graphique d'inventaire
        } catch (err) {
            console.error('Erreur lors de l\'initialisation des graphiques:', err);
        }
    }, 300);
}

/**
 * Initialise le graphique de tendance des ventes
 */
function initSalesTrendChart() {
    const canvas = document.getElementById('salesTrendChart');
    if (!canvas) {
        console.log('salesTrendChart n\'existe pas sur cette page');
        return;
    }
    
    try {
        // Détruire le graphique existant s'il y en a un
        destroyExistingChart('salesTrendChart');
        
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error('Impossible d\'obtenir le contexte 2D pour salesTrendChart');
            return;
        }
        
        console.log('salesTrendChart: contexte obtenu');
        
        // Les données sont définies dans le template via Jinja
        // Nous devons les extraire du DOM ou elles sont générées par le modèle
        const datesEl = document.getElementById('salesDates');
        const posValuesEl = document.getElementById('posSalesValues');
        const onlineValuesEl = document.getElementById('onlineSalesValues');
        const totalValuesEl = document.getElementById('totalSalesValues');

        if (!datesEl || !posValuesEl || !onlineValuesEl || !totalValuesEl) {
            console.warn('Éléments de données manquants pour le graphique de ventes');
            return;
        }

        const dates = JSON.parse(datesEl.textContent);
        const posValues = JSON.parse(posValuesEl.textContent);
        const onlineValues = JSON.parse(onlineValuesEl.textContent);
        const totalValues = JSON.parse(totalValuesEl.textContent);

        console.log('Données du graphique de ventes:', { dates, posValues, onlineValues, totalValues });

        // Vérifier si les données sont toutes à zéro ou identiques
        const isFlat = totalValues.length <= 0 || (totalValues.length > 0 && new Set(totalValues).size <= 1);
        const allZeros = totalValues.every(value => value === 0);
        
        if (allZeros) {
            console.warn('Toutes les valeurs de vente sont à zéro - le graphique sera plat');
        } else if (isFlat) {
            console.warn('Toutes les valeurs de vente sont identiques - le graphique sera plat');
            // Ajouter un message sous le graphique (une seule fois)
            const messageId = 'salesFlatMessage';
            if (!document.getElementById(messageId)) {
                const messageDiv = document.createElement('div');
                messageDiv.id = messageId;
                messageDiv.className = 'text-center mt-2';
                messageDiv.innerHTML = '<small class="text-muted">Les valeurs affichées sont constantes sur la période.</small>';
                canvas.parentNode.appendChild(messageDiv);
            }
        }
        
        // Stocker le graphique pour pouvoir le détruire plus tard
        window.chartInstances = window.chartInstances || {};
        window.chartInstances['salesTrendChart'] = new Chart(ctx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [
                    {
                        label: 'Ventes Totales',
                        data: totalValues,
                        borderColor: 'rgb(78, 115, 223)',
                        backgroundColor: 'rgba(78, 115, 223, 0.1)',
                        tension: 0.3,
                        fill: true,
                        borderWidth: 3
                    },
                    {
                        label: 'Ventes POS',
                        data: posValues,
                        borderColor: 'rgb(28, 200, 138)',
                        backgroundColor: 'rgba(28, 200, 138, 0.1)',
                        tension: 0.3,
                        fill: false,
                        borderWidth: 2
                    },
                    {
                        label: 'Ventes En ligne',
                        data: onlineValues,
                        borderColor: 'rgb(246, 194, 62)',
                        backgroundColor: 'rgba(246, 194, 62, 0.1)',
                        tension: 0.3,
                        fill: false,
                        borderWidth: 2
                    }
                ]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: ${context.raw.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' })}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: isFlat || allZeros, // Commencer à zéro si le graphique est plat ou toutes les valeurs sont zéro
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' });
                            }
                        }
                    }
                }
            }
        });
        
        console.log('Graphique de tendance des ventes initialisé avec succès');
    } catch (err) {
        console.error('Erreur lors de l\'initialisation du graphique de ventes:', err);
    }
}

/**
 * Initialise le graphique de tendance des dépenses
 */
function initExpenseTrendChart() {
    const canvas = document.getElementById('expenseTrendChart');
    if (!canvas) {
        console.log('expenseTrendChart n\'existe pas sur cette page');
        return;
    }
    
    try {
        // Détruire le graphique existant s'il y en a un
        destroyExistingChart('expenseTrendChart');
        
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error('Impossible d\'obtenir le contexte 2D pour expenseTrendChart');
            return;
        }
        
        console.log('expenseTrendChart: contexte obtenu');
        
        // Les données sont définies dans le template via Jinja
        const datesEl = document.getElementById('expenseDates');
        const valuesEl = document.getElementById('expenseValues');
        
        if (!datesEl || !valuesEl) {
            console.warn('Éléments de données manquants pour le graphique de dépenses');
            return;
        }
        
        const dates = JSON.parse(datesEl.textContent);
        const values = JSON.parse(valuesEl.textContent);
        
        console.log('Données du graphique de dépenses:', { dates, values });
        
        // Vérifier si les données sont toutes à zéro ou identiques
        const isFlat = values.length <= 0 || (values.length > 0 && new Set(values).size <= 1);
        const allZeros = values.every(value => value === 0);
        
        if (allZeros) {
            console.warn('Toutes les valeurs de dépenses sont à zéro - le graphique sera plat');
        } else if (isFlat) {
            console.warn('Toutes les valeurs de dépenses sont identiques - le graphique sera plat');
            // Ajouter un message sous le graphique (une seule fois)
            const messageId = 'expenseFlatMessage';
            if (!document.getElementById(messageId)) {
                const messageDiv = document.createElement('div');
                messageDiv.id = messageId;
                messageDiv.className = 'text-center mt-2';
                messageDiv.innerHTML = '<small class="text-muted">Les valeurs affichées sont constantes sur la période.</small>';
                canvas.parentNode.appendChild(messageDiv);
            }
        }
        
        // Stocker le graphique pour pouvoir le détruire plus tard
        window.chartInstances = window.chartInstances || {};
        window.chartInstances['expenseTrendChart'] = new Chart(ctx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [{
                    label: 'Dépenses',
                    data: values,
                    borderColor: '#e74a3b',
                    backgroundColor: 'rgba(231, 74, 59, 0.1)',
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Dépenses: ${context.raw.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' })}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: isFlat || allZeros, // Commencer à zéro si le graphique est plat ou toutes les valeurs sont zéro
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' });
                            }
                        }
                    }
                }
            }
        });
        
        console.log('Graphique de tendance des dépenses initialisé avec succès');
    } catch (err) {
        console.error('Erreur lors de l\'initialisation du graphique de dépenses:', err);
    }
}

/**
 * Initialise le graphique des catégories de dépenses
 */
function initExpenseCategoryChart() {
    const canvas = document.getElementById('expenseCategoryChart');
    if (!canvas) {
        console.log('expenseCategoryChart n\'existe pas sur cette page');
        return;
    }
    
    try {
        // Détruire le graphique existant s'il y en a un
        destroyExistingChart('expenseCategoryChart');
        
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error('Impossible d\'obtenir le contexte 2D pour expenseCategoryChart');
            return;
        }
        
        console.log('expenseCategoryChart: contexte obtenu');
        
        // Les données sont définies dans le template via Jinja
        const labelsEl = document.getElementById('categoryLabels');
        const dataEl = document.getElementById('categoryData');
        const colorsEl = document.getElementById('categoryColors');
        
        if (!labelsEl || !dataEl || !colorsEl) {
            console.warn('Éléments de données manquants pour le graphique de catégories');
            return;
        }
        
        const labels = JSON.parse(labelsEl.textContent);
        const data = JSON.parse(dataEl.textContent);
        const colors = JSON.parse(colorsEl.textContent);
        
        console.log('Données du graphique de catégories:', { labels, data, colors });
        
        // Vérifier si toutes les données sont à zéro
        const allZeros = data.every(value => value === 0);
        if (allZeros) {
            console.warn('Toutes les valeurs de catégories sont à zéro - le graphique ne montrera aucune donnée');
        }
        
        // Stocker le graphique pour pouvoir le détruire plus tard
        window.chartInstances = window.chartInstances || {};
        window.chartInstances['expenseCategoryChart'] = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors,
                    hoverOffset: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
        
        console.log('Graphique des catégories de dépenses initialisé avec succès');
    } catch (err) {
        console.error('Erreur lors de l\'initialisation du graphique de catégories:', err);
    }
}

/**
 * Fonctions pour la page d'inventaire
 */

// Fonction pour ouvrir la fenêtre modale d'historique
window.viewHistory = function(itemId) {
    if (document.getElementById('historyModal')) {
        fetch(`/reports/inventory/history/${itemId}`)
            .then(response => response.text())
            .then(html => {
                document.getElementById('historyModalContent').innerHTML = html;
                var historyModal = new bootstrap.Modal(document.getElementById('historyModal'));
                historyModal.show();
            })
            .catch(error => {
                console.error('Erreur lors du chargement de l\'historique', error);
                alert('Erreur lors du chargement de l\'historique');
            });
    }
};

// Fonction pour ouvrir la modale de mise à jour rapide du stock
window.quickUpdate = function(itemId, itemType, itemName) {
    const updateItemId = document.getElementById('updateItemId');
    const updateItemType = document.getElementById('updateItemType');
    const updateItemName = document.getElementById('updateItemName');

    if (updateItemId && updateItemType && updateItemName) {
        updateItemId.value = itemId;
        updateItemType.value = itemType;
        updateItemName.textContent = itemName;

        var quickUpdateModal = new bootstrap.Modal(document.getElementById('quickUpdateModal'));
        quickUpdateModal.show();
    }
};

/**
 * Fonctions d'export pour les différents rapports
 */

// Export du rapport des produits
window.exportProductReport = function() {
    const urlParams = new URLSearchParams(window.location.search);
    const exportUrl = '/reports/products/export?' + urlParams.toString();
    window.location.href = exportUrl;
};

// Export du rapport d'inventaire
window.exportInventoryReport = function(format = 'csv') {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('format', format);
    const exportUrl = '/reports/inventory/export?' + urlParams.toString();
    window.location.href = exportUrl;
};

// Export du rapport des dépenses
window.exportExpenseReport = function() {
    const urlParams = new URLSearchParams(window.location.search);
    const exportUrl = '/reports/expenses/export?' + urlParams.toString();
    window.location.href = exportUrl;
};

/**
 * Fonctions pour les actions sur l'inventaire
 */

// Fonction pour afficher l'historique d'un article
window.showHistory = function(itemId) {
    console.log('Affichage de l\'historique pour l\'article:', itemId);
    loadHistoryPage(itemId, 1);
};

// Fonction pour charger une page spécifique de l'historique
window.loadHistoryPage = function(itemId, page = 1) {
    console.log('Chargement de la page', page, 'de l\'historique pour l\'article:', itemId);
    fetch(`/reports/inventory/history/${itemId}?page=${page}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Erreur lors du chargement de l\'historique');
            }
            return response.text();
        })
        .then(html => {
            const historyContent = document.getElementById('historyContent');
            if (historyContent) {
                historyContent.innerHTML = html;
                // Si c'est la première page, afficher la modal
                if (page === 1) {
                    const historyModal = new bootstrap.Modal(document.getElementById('historyModal'));
                    historyModal.show();
                }
            }
        })
        .catch(error => {
            console.error('Erreur lors du chargement de l\'historique:', error);
            alert('Erreur lors du chargement de l\'historique');
        });
};

// Fonction pour ajuster le stock d'un article
window.adjustStock = function(itemId, itemType) {
    console.log('Ajustement du stock pour l\'article:', itemId, 'type:', itemType);

    // Remplir les champs cachés du formulaire
    const form = document.getElementById('adjustStockForm');
    if (form) {
        // Supprimer les anciens champs cachés s'ils existent
        const oldItemIdInput = form.querySelector('input[name="item_id"]');
        if (oldItemIdInput) {
            oldItemIdInput.remove();
        }
        const oldItemTypeInput = form.querySelector('input[name="item_type"]');
        if (oldItemTypeInput) {
            oldItemTypeInput.remove();
        }

        // Créer de nouveaux champs cachés
        const itemIdInput = document.createElement('input');
        itemIdInput.type = 'hidden';
        itemIdInput.name = 'item_id';
        itemIdInput.value = itemId;
        form.appendChild(itemIdInput);

        const itemTypeInput = document.createElement('input');
        itemTypeInput.type = 'hidden';
        itemTypeInput.name = 'item_type';
        itemTypeInput.value = itemType;
        form.appendChild(itemTypeInput);

        // Réinitialiser le formulaire
        form.reset();

        // Remettre les champs cachés après le reset
        itemIdInput.value = itemId;
        itemTypeInput.value = itemType;

        console.log('Champs cachés ajoutés:', {
            item_id: itemIdInput.value,
            item_type: itemTypeInput.value
        });

        // Afficher la modale
        const adjustModal = new bootstrap.Modal(document.getElementById('adjustStockModal'));
        adjustModal.show();
    } else {
        console.error('Formulaire adjustStockForm non trouvé');
    }
};

/**
 * Fonctions pour les actions sur les dépenses
 */

// Fonction pour voir les détails d'une dépense
window.viewExpense = function(expenseId) {
    console.log('Affichage des détails de la dépense:', expenseId);
    // Rediriger vers la page de détails de la dépense
    window.location.href = `/expenses/view/${expenseId}`;
};

// Fonction pour éditer une dépense
window.editExpense = function(expenseId) {
    console.log('Édition de la dépense:', expenseId);
    // Rediriger vers la page d'édition de la dépense dans le module expenses
    window.location.href = `/expenses/edit/${expenseId}`;
};

// Fonction pour supprimer une dépense
window.deleteExpense = function(expenseId) {
    console.log('Suppression de la dépense:', expenseId);
    if (confirm('Êtes-vous sûr de vouloir supprimer cette dépense ?')) {
        // Créer un formulaire pour envoyer une requête POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/expenses/delete/${expenseId}`;

        // Ajouter le token CSRF si disponible
        const csrfToken = document.querySelector('meta[name=csrf-token]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
};

