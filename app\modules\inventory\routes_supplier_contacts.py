from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app.extensions import db
from app.modules.inventory.models_supplier import Supplier
from app.modules.inventory.models_supplier_contact import SupplierContact
from app.modules.inventory.forms import SupplierContactForm
from app.utils.decorators import inventory_access_required as permission_required
from datetime import datetime
from . import bp

@bp.route('/suppliers/<int:supplier_id>/contacts')
@login_required
@permission_required
def supplier_contacts(supplier_id):
    """Liste des contacts d'un fournisseur"""
    supplier = Supplier.query.filter_by(
        id=supplier_id, 
        owner_id=current_user.get_owner_id
    ).first_or_404()
    
    contacts = SupplierContact.query.filter_by(
        supplier_id=supplier_id
    ).order_by(SupplierContact.contact_date.desc()).all()
    
    return render_template('inventory/suppliers/contacts.html',
                         supplier=supplier,
                         contacts=contacts)

@bp.route('/suppliers/<int:supplier_id>/contacts/add', methods=['GET', 'POST'])
@login_required
@permission_required
def add_supplier_contact(supplier_id):
    """Ajouter un contact fournisseur"""
    supplier = Supplier.query.filter_by(
        id=supplier_id, 
        owner_id=current_user.get_owner_id
    ).first_or_404()
    
    form = SupplierContactForm()
    
    if form.validate_on_submit():
        contact = SupplierContact(
            supplier_id=supplier_id,
            contact_type=form.contact_type.data,
            subject=form.subject.data,
            content=form.content.data,
            contact_date=datetime.strptime(form.contact_date.data, '%Y-%m-%dT%H:%M') if form.contact_date.data else datetime.utcnow(),
            is_important=form.is_important.data,
            follow_up_date=datetime.strptime(form.follow_up_date.data, '%Y-%m-%dT%H:%M') if form.follow_up_date.data else None,
            status=form.status.data,
            user_id=current_user.id
        )
        
        db.session.add(contact)
        db.session.commit()
        flash('Contact ajouté avec succès!', 'success')
        return redirect(url_for('inventory.supplier_contacts', supplier_id=supplier_id))
    
    return render_template('inventory/suppliers/contact_form.html',
                         title='Ajouter un contact',
                         form=form,
                         supplier=supplier)

@bp.route('/suppliers/<int:supplier_id>/contacts/<int:contact_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required
def edit_supplier_contact(supplier_id, contact_id):
    """Modifier un contact fournisseur"""
    supplier = Supplier.query.filter_by(
        id=supplier_id, 
        owner_id=current_user.get_owner_id
    ).first_or_404()
    
    contact = SupplierContact.query.filter_by(
        id=contact_id,
        supplier_id=supplier_id
    ).first_or_404()
    
    form = SupplierContactForm(obj=contact)
    
    if request.method == 'GET':
        # Pré-remplir les champs de date
        if contact.contact_date:
            form.contact_date.data = contact.contact_date.strftime('%Y-%m-%dT%H:%M')
        if contact.follow_up_date:
            form.follow_up_date.data = contact.follow_up_date.strftime('%Y-%m-%dT%H:%M')
    
    if form.validate_on_submit():
        contact.contact_type = form.contact_type.data
        contact.subject = form.subject.data
        contact.content = form.content.data
        contact.contact_date = datetime.strptime(form.contact_date.data, '%Y-%m-%dT%H:%M') if form.contact_date.data else datetime.utcnow()
        contact.is_important = form.is_important.data
        contact.follow_up_date = datetime.strptime(form.follow_up_date.data, '%Y-%m-%dT%H:%M') if form.follow_up_date.data else None
        contact.status = form.status.data
        
        db.session.commit()
        flash('Contact modifié avec succès!', 'success')
        return redirect(url_for('inventory.supplier_contacts', supplier_id=supplier_id))
    
    return render_template('inventory/suppliers/contact_form.html',
                         title='Modifier le contact',
                         form=form,
                         supplier=supplier,
                         contact=contact)

@bp.route('/suppliers/<int:supplier_id>/contacts/<int:contact_id>/delete', methods=['POST'])
@login_required
@permission_required
def delete_supplier_contact(supplier_id, contact_id):
    """Supprimer un contact fournisseur"""
    contact = SupplierContact.query.filter_by(
        id=contact_id,
        supplier_id=supplier_id
    ).first_or_404()
    
    db.session.delete(contact)
    db.session.commit()
    flash('Contact supprimé avec succès!', 'success')
    return redirect(url_for('inventory.supplier_contacts', supplier_id=supplier_id))

@bp.route('/suppliers/contacts/pending')
@login_required
@permission_required
def pending_supplier_contacts():
    """Liste des contacts en attente de suivi"""
    contacts = SupplierContact.query.join(Supplier).filter(
        Supplier.owner_id == current_user.get_owner_id,
        SupplierContact.status == 'pending'
    ).order_by(SupplierContact.follow_up_date.asc()).all()
    
    return render_template('inventory/suppliers/pending_contacts.html',
                         contacts=contacts)
