from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON><PERSON>ield, FileAllowed
from wtforms import <PERSON><PERSON>ield, TextAreaField, FloatField, BooleanField, SelectField, SubmitField
from wtforms.validators import DataRequired, Length, Optional, NumberRange

class ProductCategoryForm(FlaskForm):
    name = StringField('Nom de la catégorie', validators=[
        DataRequired(),
        Length(min=2, max=64)
    ])
    description = TextAreaField('Description')
    color = StringField('Couleur', default='#6c757d')
    image = FileField('Image', validators=[
        FileAllowed(['jpg', 'png', 'jpeg', 'gif'], 'Images uniquement!')
    ])
    submit = SubmitField('Enregistrer')

class ProductForm(FlaskForm):
    name = StringField('Nom', validators=[DataRequired(message="Le nom est requis")])
    description = TextAreaField('Description')
    category_id = SelectField('<PERSON>é<PERSON>ie', coerce=int, 
                            validators=[DataRequired(message="La catégorie est requise")])
    price = FloatField('Prix de vente', 
                      validators=[DataRequired(message="Le prix est requis"),
                                NumberRange(min=0, message="Le prix doit être positif")])
    cost_price = FloatField('Prix de revient', 
                          validators=[Optional(),
                                    NumberRange(min=0, message="Le prix de revient doit être positif")])
    stock_quantity = FloatField('Quantité en stock',
                              validators=[Optional(),
                                        NumberRange(min=0, message="La quantité doit être positive")])
    minimum_stock = FloatField('Stock minimum',
                             validators=[Optional(),
                                       NumberRange(min=0, message="Le stock minimum doit être positif")])
    unit = StringField('Unité', default='unité')
    has_recipe = BooleanField('Basé sur une recette')
    image = FileField('Image', validators=[
        FileAllowed(['jpg', 'png', 'jpeg', 'gif'], 'Images uniquement!')
    ])
    submit = SubmitField('Enregistrer')
    
    def validate(self, extra_validators=None):
        if not super().validate(extra_validators=extra_validators):
            return False
            
        if self.has_recipe.data:
            # Si c'est un produit basé sur une recette, on met la quantité en stock à 0
            self.stock_quantity.data = 0
        else:
            # Si ce n'est pas un produit basé sur une recette, on doit avoir une quantité en stock
            if self.stock_quantity.data is None:
                self.stock_quantity.errors.append(
                    'La quantité en stock est requise pour un produit non basé sur une recette'
                )
                return False
        
        return True 