<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h5>Informations générales</h5>
            <table class="table table-sm">
                <tr>
                    <th>Référence :</th>
                    <td>{{ sale.reference }}</td>
                </tr>
                <tr>
                    <th>Date :</th>
                    <td>{{ sale.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                </tr>
                <tr>
                    <th>Statut :</th>
                    <td>
                        {% if sale.status.value == 'PAID' %}
                        <span class="badge bg-success">Terminée</span>
                        {% elif sale.status.value == 'PENDING' %}
                        <span class="badge bg-warning">En cours</span>
                        {% elif sale.status.value == 'CANCELLED' %}
                        <span class="badge bg-danger">Annulée</span>
                        {% endif %}
                    </td>
                </tr>
                {% if sale.table %}
                <tr>
                    <th>Table :</th>
                    <td>Table {{ sale.table.number }} - {{ sale.table.location }}</td>
                </tr>
                {% endif %}
            </table>
        </div>
        <div class="col-md-6">
            <h5>Totaux</h5>
            <table class="table table-sm">
                <tr>
                    <th>Total HT :</th>
                    <td>{{ "%.2f"|format(sale.total_ht) }} €</td>
                </tr>
                <tr>
                    <th>TVA :</th>
                    <td>{{ "%.2f"|format(sale.total_tax) }} €</td>
                </tr>
                <tr>
                    <th>Total TTC :</th>
                    <td class="fw-bold">{{ "%.2f"|format(sale.total_ttc) }} €</td>
                </tr>
                {% if sale.discount_amount > 0 %}
                <tr>
                    <th>Remise :</th>
                    <td>{{ "%.2f"|format(sale.discount_amount) }} €</td>
                </tr>
                {% endif %}
            </table>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <h5>Produits</h5>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Produit</th>
                            <th class="text-center">Quantité</th>
                            <th class="text-end">Prix unitaire</th>
                            <th class="text-end">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in sale.items.all() %}
                        <tr>
                            <td>{{ item.product.name }}</td>
                            <td class="text-center">{{ item.quantity }}</td>
                            <td class="text-end">{{ "%.2f"|format(item.price) }} €</td>
                            <td class="text-end">{{ "%.2f"|format(item.total) }} €</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr class="fw-bold">
                            <td colspan="3" class="text-end">Total HT :</td>
                            <td class="text-end">{{ "%.2f"|format(sale.total_ht) }} €</td>
                        </tr>
                        <tr>
                            <td colspan="3" class="text-end">TVA :</td>
                            <td class="text-end">{{ "%.2f"|format(sale.total_tax) }} €</td>
                        </tr>
                        {% if sale.discount_amount > 0 %}
                        <tr>
                            <td colspan="3" class="text-end">Remise :</td>
                            <td class="text-end">{{ "%.2f"|format(sale.discount_amount) }} €</td>
                        </tr>
                        {% endif %}
                        <tr class="fw-bold">
                            <td colspan="3" class="text-end">Total TTC :</td>
                            <td class="text-end">{{ "%.2f"|format(sale.total_ttc) }} €</td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    {% if sale.kitchen_note %}
    <div class="row mt-4">
        <div class="col-12">
            <h5>Notes cuisine</h5>
            <div class="alert alert-info">
                {{ sale.kitchen_note }}
            </div>
        </div>
    </div>
    {% endif %}
</div> 