{% extends "base.html" %}

{% block title %}Mode POS - Approvisionnement{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
<script src="{{ url_for('inventory.static', filename='js/stock_replenishment_pos.js') }}"></script>
{% endblock %}

{% block content %}
<div class="pos-replenishment-container">
    <!-- En-tête Mode POS -->
    <div class="pos-replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-2">
                <h4 class="mb-0">
                    <i class="fas fa-truck-loading"></i> Mode POS - Approvisionnement
                </h4>
                <div class="header-actions">
                    <button class="btn btn-outline-light btn-sm me-2" onclick="clearCart()">
                        <i class="fas fa-trash"></i> Vider
                    </button>
                    <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- Colonne gauche - Interface de sélection -->
            <div class="col-lg-8">
                <!-- Barre de recherche -->
                <div class="search-section mb-3">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" id="searchInput" class="form-control" 
                               placeholder="Rechercher des produits ou ingrédients...">
                    </div>
                </div>

                <!-- Sélecteurs de fournisseurs -->
                <div class="supplier-section mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Catégorie de fournisseur (optionnel)</label>
                            <select id="supplierCategorySelect" class="form-select">
                                <option value="0">Toutes les catégories</option>
                                {% for category in supplier_categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Fournisseur (optionnel)</label>
                            <select id="supplierSelect" class="form-select">
                                <option value="0">Aucun fournisseur</option>
                                {% for supplier in suppliers %}
                                <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Boutons de catégories -->
                <div class="category-buttons mb-3">
                    <div class="btn-group w-100" role="group">
                        <button type="button" class="btn btn-outline-primary active" 
                                onclick="showCategory('all')">
                            <i class="fas fa-th-large"></i> Tous
                        </button>
                        <button type="button" class="btn btn-outline-success" 
                                onclick="showCategory('products')">
                            <i class="fas fa-box"></i> Produits sans recettes
                        </button>
                        <button type="button" class="btn btn-outline-info" 
                                onclick="showCategory('ingredients')">
                            <i class="fas fa-leaf"></i> Ingrédients
                        </button>
                    </div>
                </div>

                <!-- Grille des articles -->
                <div class="items-section">
                    <div id="itemsGrid" class="items-grid">
                        <!-- Les articles seront chargés ici via JavaScript -->
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-3">
                        <nav aria-label="Navigation des articles">
                            <ul class="pagination pagination-sm" id="itemsPagination">
                                <!-- Pagination sera générée par JavaScript -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Colonne droite - Ticket et Numpad -->
            <div class="col-lg-4">
                <!-- Ticket d'achat -->
                <div class="purchase-ticket mb-3">
                    <div class="ticket-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">Ticket d'Achat</h6>
                            <span id="ticketNumber">#{{ now.strftime('%y%m%d%H%M%S') }}</span>
                        </div>
                        <div class="ticket-info mt-2">
                            <small id="supplierInfo">Fournisseur: Autres</small><br>
                            <small>Utilisateur: {{ current_user.username }}</small><br>
                            <small id="ticketDate">{{ now.strftime('%d/%m/%Y %H:%M') }}</small>
                        </div>
                    </div>
                    
                    <div class="ticket-items" id="ticketItems">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                            <p>Aucun article sélectionné</p>
                        </div>
                    </div>
                    
                    <div class="ticket-total">
                        <div class="d-flex justify-content-between">
                            <strong>Total à payer:</strong>
                            <strong id="totalAmount">0.00 €</strong>
                        </div>
                        <button class="btn btn-success w-100 mt-2" id="payButton" 
                                onclick="openPaymentModal()" disabled>
                            <i class="fas fa-credit-card"></i> Payer la marchandise
                        </button>
                    </div>
                </div>

                <!-- Numpad -->
                <div class="numpad-container">
                    <div class="numpad-display" id="numpadDisplay">0</div>
                    <div class="numpad-grid">
                        <button class="numpad-btn" data-value="7">7</button>
                        <button class="numpad-btn" data-value="8">8</button>
                        <button class="numpad-btn" data-value="9">9</button>
                        <button class="numpad-btn" data-value="4">4</button>
                        <button class="numpad-btn" data-value="5">5</button>
                        <button class="numpad-btn" data-value="6">6</button>
                        <button class="numpad-btn" data-value="1">1</button>
                        <button class="numpad-btn" data-value="2">2</button>
                        <button class="numpad-btn" data-value="3">3</button>
                        <button class="numpad-btn" data-value="0">0</button>
                        <button class="numpad-btn" data-value=".">.</button>
                        <button class="numpad-btn special" data-value="clear">C</button>
                    </div>
                    <div class="mt-2">
                        <button class="numpad-btn special w-100" data-value="backspace">
                            <i class="fas fa-backspace"></i> Effacer
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de paiement -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-credit-card"></i> Paiement Fournisseur
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="payment-summary mb-4">
                    <h6>Résumé de la commande</h6>
                    <div class="d-flex justify-content-between">
                        <span>Montant total:</span>
                        <strong id="paymentTotal">0.00 €</strong>
                    </div>
                </div>

                <div class="payment-methods">
                    <h6>Méthode de paiement</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-primary w-100 payment-method-btn" 
                                    data-method="cash_caisse">
                                <i class="fas fa-money-bill-wave"></i><br>
                                Cash depuis Caisse
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-info w-100 payment-method-btn" 
                                    data-method="cheque_compte_banque">
                                <i class="fas fa-money-check"></i><br>
                                Chèque Bancaire
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-success w-100 payment-method-btn" 
                                    data-method="virement_depuis_compte_banque">
                                <i class="fas fa-exchange-alt"></i><br>
                                Virement Bancaire
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-warning w-100 payment-method-btn" 
                                    data-method="sortie_cash_banque">
                                <i class="fas fa-university"></i><br>
                                Sortie Cash Banque
                            </button>
                        </div>
                        <div class="col-12 mb-3">
                            <button class="btn btn-outline-secondary w-100 payment-method-btn" 
                                    data-method="pay_later">
                                <i class="fas fa-clock"></i> Payer plus tard
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Options de traitement -->
                <div class="processing-options mt-4">
                    <h6>Options de traitement</h6>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="processingType" 
                               id="receiveAndPay" value="receive_and_pay" checked>
                        <label class="form-check-label" for="receiveAndPay">
                            Recevoir la marchandise et payer maintenant
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="processingType" 
                               id="receivePayLater" value="receive_pay_later">
                        <label class="form-check-label" for="receivePayLater">
                            Recevoir la marchandise et payer plus tard
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="processingType" 
                               id="orderOnly" value="order_only">
                        <label class="form-check-label" for="orderOnly">
                            Bon de commande seulement (ne pas mettre à jour le stock)
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    Annuler
                </button>
                <button type="button" class="btn btn-success" id="confirmPaymentBtn" disabled>
                    <i class="fas fa-check"></i> Confirmer
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Données initiales pour JavaScript
window.stockReplenishmentData = {
    products: {{ products_json | safe }},
    ingredients: {{ ingredients_json | safe }},
    suppliers: {{ suppliers_json | safe }},
    productCategories: {{ product_categories_json | safe }},
    ingredientCategories: {{ ingredient_categories_json | safe }}
};
</script>
{% endblock %}
