{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-money-bill"></i>
            Paramètres de paiement
        </h1>
        <button type="submit" form="paymentForm" class="btn btn-primary">
            <i class="fas fa-save"></i>
            Enregistrer
        </button>
    </div>

    <!-- Settings Form -->
    <form id="paymentForm" method="POST">
        {{ form.hidden_tag() }}

        <div class="row">
            <!-- Payment Methods -->
            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-credit-card"></i>
                            Moyens de paiement
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            {{ form.accepted_payment_methods.label(class="form-label") }}
                            <div class="payment-methods-list">
                                {{ form.accepted_payment_methods(class="form-check-input") }}
                            </div>
                            {% for error in form.accepted_payment_methods.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.default_payment_method.label(class="form-label") }}
                            {{ form.default_payment_method(class="form-select" + (" is-invalid" if form.default_payment_method.errors else "")) }}
                            {% for error in form.default_payment_method.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.allow_split_payment(class="form-check-input") }}
                                <label class="form-check-label" for="{{ form.allow_split_payment.id }}">
                                    Paiement fractionné
                                </label>
                            </div>
                            {% for error in form.allow_split_payment.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.allow_partial_payment(class="form-check-input") }}
                                <label class="form-check-label" for="{{ form.allow_partial_payment.id }}">
                                    Paiement partiel
                                </label>
                            </div>
                            {% for error in form.allow_partial_payment.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.require_payment_reference(class="form-check-input") }}
                                <label class="form-check-label" for="{{ form.require_payment_reference.id }}">
                                    Référence de paiement
                                </label>
                            </div>
                            {% for error in form.require_payment_reference.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.round_amounts.label(class="form-label") }}
                            {{ form.round_amounts(class="form-select" + (" is-invalid" if form.round_amounts.errors else "")) }}
                            {% for error in form.round_amounts.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Card Payments -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-credit-card"></i>
                            Paiements par carte
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            {{ form.card_terminal.label(class="form-label") }}
                            {{ form.card_terminal(class="form-select" + (" is-invalid" if form.card_terminal.errors else "")) }}
                            {% for error in form.card_terminal.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.card_minimum.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.card_minimum(class="form-control" + (" is-invalid" if form.card_minimum.errors else "")) }}
                                <span class="input-group-text">€</span>
                            </div>
                            {% for error in form.card_minimum.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.card_fee.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.card_fee(class="form-control" + (" is-invalid" if form.card_fee.errors else "")) }}
                                <span class="input-group-text">%</span>
                            </div>
                            {% for error in form.card_fee.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Taxes -->
            <div class="col-lg-6">
                <!-- Tax Settings -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-percent"></i>
                            Taxes
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            {{ form.tax_number.label(class="form-label") }}
                            {{ form.tax_number(class="form-control" + (" is-invalid" if form.tax_number.errors else "")) }}
                            {% for error in form.tax_number.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.tax_enabled(class="form-check-input") }}
                                {{ form.tax_enabled.label(class="form-check-label") }}
                            </div>
                            {% for error in form.tax_enabled.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.prices_include_tax(class="form-check-input") }}
                                {{ form.prices_include_tax.label(class="form-check-label") }}
                            </div>
                            {% for error in form.prices_include_tax.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.default_tax_rate.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.default_tax_rate(class="form-control" + (" is-invalid" if form.default_tax_rate.errors else "")) }}
                                <span class="input-group-text">%</span>
                            </div>
                            {% for error in form.default_tax_rate.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Tax Rates -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-list"></i>
                            Taux de TVA
                        </h6>
                        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#taxRateModal">
                            <i class="fas fa-plus"></i>
                            Ajouter
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Nom</th>
                                        <th>Taux</th>
                                        <th>Description</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for rate in tax_rates %}
                                    <tr>
                                        <td>{{ rate.name }}</td>
                                        <td>{{ "%.2f"|format(rate.rate) }}%</td>
                                        <td>{{ rate.description }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#taxRateModal"
                                                        data-rate-id="{{ rate.id }}"
                                                        data-rate-name="{{ rate.name }}"
                                                        data-rate-value="{{ rate.rate }}"
                                                        data-rate-description="{{ rate.description }}">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#deleteTaxRateModal"
                                                        data-rate-id="{{ rate.id }}"
                                                        data-rate-name="{{ rate.name }}">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">
                                            Aucun taux de TVA
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Tax Rate Modal -->
<div class="modal fade" id="taxRateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Taux de TVA</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="taxRateForm" method="POST">
                    <input type="hidden" name="rate_id" id="rate-id">

                    <div class="mb-3">
                        <label class="form-label">Nom</label>
                        <input type="text" name="name" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Taux</label>
                        <div class="input-group">
                            <input type="number" name="rate" class="form-control" min="0" max="100" step="0.01" required>
                            <span class="input-group-text">%</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea name="description" class="form-control" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="submit" form="taxRateForm" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Enregistrer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Tax Rate Modal -->
<div class="modal fade" id="deleteTaxRateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le taux de TVA <strong id="delete-rate-name"></strong> ?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    Cette action est irréversible. Les produits utilisant ce taux seront réinitialisés au taux par défaut.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteTaxRateForm" method="POST">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i>
                        Supprimer
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tax Rate Modal
    const taxRateModal = document.getElementById('taxRateModal');
    const taxRateForm = document.getElementById('taxRateForm');
    const rateIdInput = document.getElementById('rate-id');
    const rateNameInput = taxRateForm.querySelector('input[name="name"]');
    const rateValueInput = taxRateForm.querySelector('input[name="rate"]');
    const rateDescriptionInput = taxRateForm.querySelector('textarea[name="description"]');

    taxRateModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const rateId = button.dataset.rateId;
        
        if (rateId) {
            // Edit mode
            rateIdInput.value = rateId;
            rateNameInput.value = button.dataset.rateName;
            rateValueInput.value = button.dataset.rateValue;
            rateDescriptionInput.value = button.dataset.rateDescription;
            taxRateForm.action = `/settings/payment/tax-rates/${rateId}`;
        } else {
            // Create mode
            taxRateForm.reset();
            rateIdInput.value = '';
            taxRateForm.action = '/settings/payment/tax-rates';
        }
    });

    // Delete Tax Rate Modal
    const deleteTaxRateModal = document.getElementById('deleteTaxRateModal');
    const deleteTaxRateForm = document.getElementById('deleteTaxRateForm');
    
    deleteTaxRateModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const rateId = button.dataset.rateId;
        const rateName = button.dataset.rateName;
        
        document.getElementById('delete-rate-name').textContent = rateName;
        deleteTaxRateForm.action = `/settings/payment/tax-rates/${rateId}/delete`;
    });

    // Toggle tax settings
    const taxEnabled = document.getElementById('tax_enabled');
    const taxFields = document.querySelectorAll('#prices_include_tax, #default_tax_rate');

    function toggleTaxFields() {
        taxFields.forEach(field => {
            field.disabled = !taxEnabled.checked;
        });
    }

    taxEnabled.addEventListener('change', toggleTaxFields);
    toggleTaxFields();
});
</script>
{% endblock %}
{% endblock %} 