from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, IntegerField, BooleanField, SelectField
from wtforms.validators import DataRequired, Length, NumberRange

class RoomForm(FlaskForm):
    name = StringField('Nom de la salle', validators=[
        DataRequired(message='Le nom de la salle est requis'),
        Length(min=2, max=100, message='Le nom doit contenir entre 2 et 100 caractères')
    ])
    
    description = TextAreaField('Description', validators=[
        Length(max=500, message='La description ne peut pas dépasser 500 caractères')
    ])
    
    width = IntegerField('Largeur (pixels)', validators=[
        DataRequired(message='La largeur est requise'),
        NumberRange(min=400, max=2000, message='La largeur doit être entre 400 et 2000 pixels')
    ], default=800)
    
    height = IntegerField('Hauteur (pixels)', validators=[
        DataRequired(message='La hauteur est requise'),
        NumberRange(min=300, max=1500, message='La hauteur doit être entre 300 et 1500 pixels')
    ], default=600)
    
    background_color = StringField('Couleur de fond', validators=[
        Length(min=7, max=7, message='Format de couleur invalide (ex: #ffffff)')
    ], default='#f8f9fa')
    
    is_default = BooleanField('Salle par défaut')
    is_active = BooleanField('Salle active', default=True)

class TablePositionForm(FlaskForm):
    table_id = IntegerField('ID de la table', validators=[DataRequired()])
    position_x = IntegerField('Position X', validators=[DataRequired()])
    position_y = IntegerField('Position Y', validators=[DataRequired()])
    
class TableAppearanceForm(FlaskForm):
    table_shape = SelectField('Forme', choices=[
        ('round', 'Ronde'),
        ('square', 'Carrée'),
        ('rectangle', 'Rectangulaire')
    ], default='round')
    
    table_size = SelectField('Taille', choices=[
        ('small', 'Petite'),
        ('medium', 'Moyenne'),
        ('large', 'Grande')
    ], default='medium')
    
    table_color = StringField('Couleur', validators=[
        Length(min=7, max=7, message='Format de couleur invalide (ex: #8B4513)')
    ], default='#8B4513')
