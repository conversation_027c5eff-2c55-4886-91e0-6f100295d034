{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="mb-0">{{ title }} - {{ supplier.name }}</h2>
                        <a href="{{ url_for('inventory.supplier_contacts', supplier_id=supplier.id) }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Retour
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Informations du fournisseur -->
                    <div class="alert alert-info mb-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle me-2"></i>
                            <div>
                                <strong>{{ supplier.name }}</strong>
                                {% if supplier.category %}
                                    <span class="badge ms-2" style="background-color: {{ supplier.category.color }};">
                                        <i class="{{ supplier.category.icon }}"></i> {{ supplier.category.name }}
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <form method="POST" novalidate>
                        {{ form.csrf_token }}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.contact_type.label(class="form-label") }}
                                {{ form.contact_type(class="form-control" + (" is-invalid" if form.contact_type.errors else "")) }}
                                {% for error in form.contact_type.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="col-md-6">
                                {{ form.status.label(class="form-label") }}
                                {{ form.status(class="form-control" + (" is-invalid" if form.status.errors else "")) }}
                                {% for error in form.status.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.subject.label(class="form-label") }}
                            {{ form.subject(class="form-control" + (" is-invalid" if form.subject.errors else ""), placeholder="Sujet du contact (optionnel)") }}
                            {% for error in form.subject.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.content.label(class="form-label") }}
                            {{ form.content(class="form-control" + (" is-invalid" if form.content.errors else ""), rows="4", placeholder="Décrivez le contenu de ce contact...") }}
                            {% for error in form.content.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.contact_date.label(class="form-label") }}
                                {{ form.contact_date(class="form-control" + (" is-invalid" if form.contact_date.errors else ""), type="datetime-local") }}
                                {% for error in form.contact_date.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                                <small class="form-text text-muted">Laissez vide pour utiliser la date/heure actuelle</small>
                            </div>
                            <div class="col-md-6">
                                {{ form.follow_up_date.label(class="form-label") }}
                                {{ form.follow_up_date(class="form-control" + (" is-invalid" if form.follow_up_date.errors else ""), type="datetime-local") }}
                                {% for error in form.follow_up_date.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                                <small class="form-text text-muted">Date de suivi prévue (optionnel)</small>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                {{ form.is_important(class="form-check-input") }}
                                {{ form.is_important.label(class="form-check-label") }}
                            </div>
                            <small class="form-text text-muted">Marquer ce contact comme important</small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('inventory.supplier_contacts', supplier_id=supplier.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Définir la date/heure actuelle par défaut si le champ est vide
    const contactDateField = document.getElementById('contact_date');
    if (contactDateField && !contactDateField.value) {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        
        contactDateField.value = `${year}-${month}-${day}T${hours}:${minutes}`;
    }
    
    // Mise à jour de l'icône selon le type de contact sélectionné
    const contactTypeField = document.getElementById('contact_type');
    if (contactTypeField) {
        contactTypeField.addEventListener('change', function() {
            // Vous pouvez ajouter ici une logique pour changer l'icône ou d'autres éléments
            // selon le type de contact sélectionné
        });
    }
});
</script>
{% endblock %}
