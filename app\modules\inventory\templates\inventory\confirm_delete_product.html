{% extends "base.html" %}

{% block title %}Confirmer la suppression du produit{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">Confirmer la suppression</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> Attention: Cette action est irréversible!
                    </div>
                    
                    <p>Êtes-vous sûr de vouloir supprimer le produit <strong>{{ product.name }}</strong>?</p>
                    
                    {% if product.image_path %}
                    <div class="text-center mb-3">
                        <img src="{{ url_for('static', filename=product.image_path) }}" 
                             alt="{{ product.name }}" 
                             class="img-thumbnail" 
                             style="max-height: 150px;">
                    </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Détails du produit:</h6>
                            <ul class="list-unstyled">
                                <li><strong>Prix:</strong> {{ product.price|round(2) }} €</li>
                                <li><strong>Catégorie:</strong> {{ product.category.name if product.category else 'Aucune' }}</li>
                                {% if product.description %}
                                <li><strong>Description:</strong> {{ product.description|truncate(100) }}</li>
                                {% endif %}
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Impact potentiel:</h6>
                            <ul>
                                <li>Le produit sera complètement supprimé de l'inventaire</li>
                                <li>Les ventes historiques conserveront une référence au produit</li>
                                <li>Les images associées seront supprimées</li>
                            </ul>
                        </div>
                    </div>
                    
                    <form action="{{ url_for('inventory.delete_product', id=product.id) }}" method="post" class="mt-4">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('inventory.products') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Annuler
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Confirmer la suppression
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 