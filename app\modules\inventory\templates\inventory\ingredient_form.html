{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-mortar-pestle"></i>
                    {{ title }}
                </h1>
                <a href="{{ url_for('inventory.ingredients') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Retour aux ingrédients
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}

                        <!-- Basic Information -->
                        <h5 class="mb-4">Informations de base</h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% for error in form.name.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="col-md-6 mb-3">
                                {{ form.category_id.label(class="form-label") }}
                                {{ form.category_id(class="form-select" + (" is-invalid" if form.category_id.errors else "")) }}
                                {% for error in form.category_id.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="3") }}
                            {% for error in form.description.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <!-- Stock Management -->
                        <h5 class="mb-4 mt-5">Gestion du stock</h5>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                {{ form.stock_quantity.label(class="form-label") }}
                                {{ form.stock_quantity(class="form-control" + (" is-invalid" if form.stock_quantity.errors else "")) }}
                                {% for error in form.stock_quantity.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="col-md-4 mb-3">
                                {{ form.unit.label(class="form-label") }}
                                {{ form.unit(class="form-select" + (" is-invalid" if form.unit.errors else "")) }}
                                {% for error in form.unit.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="col-md-4 mb-3">
                                {{ form.minimum_stock.label(class="form-label") }}
                                {{ form.minimum_stock(class="form-control" + (" is-invalid" if form.minimum_stock.errors else "")) }}
                                {% for error in form.minimum_stock.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Pricing -->
                        <h5 class="mb-4 mt-5">Prix</h5>
                        <div class="mb-3">
                            {{ form.price_per_unit.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.price_per_unit(class="form-control" + (" is-invalid" if form.price_per_unit.errors else "")) }}
                                <span class="input-group-text">€/{{ form.unit.data or 'unité' }}</span>
                            </div>
                            {% for error in form.price_per_unit.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <!-- Expiry Date -->
                        <h5 class="mb-4 mt-5">Date d'expiration</h5>
                        <div class="mb-4">
                            {{ form.expiry_date.label(class="form-label") }}
                            {{ form.expiry_date(class="form-control" + (" is-invalid" if form.expiry_date.errors else ""), type="date") }}
                            {% for error in form.expiry_date.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                            <small class="text-muted">
                                Laissez vide si l'ingrédient n'a pas de date d'expiration
                            </small>
                        </div>

                        <!-- Image -->
                        <h5 class="mb-4 mt-5">Image</h5>
                        <div class="mb-4">
                            {{ form.image.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.image(class="form-control" + (" is-invalid" if form.image.errors else ""), accept="image/*") }}
                                {% if ingredient and ingredient.image_path %}
                                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteImageModal">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                            <small class="text-muted">Formats acceptés: JPG, PNG, GIF</small>
                            {% for error in form.image.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                            
                            {% if ingredient and ingredient.image_path %}
                            <div class="mt-2">
                                <img src="{{ url_for('static', filename=ingredient.image_path) }}" class="img-thumbnail" style="max-height: 200px;">
                            </div>
                            {% endif %}
                        </div>

                        <!-- Submit -->
                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('inventory.ingredients') }}" class="btn btn-outline-secondary">
                                Annuler
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Help Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i>
                        Aide
                    </h6>
                </div>
                <div class="card-body">
                    <h6>Stock minimum</h6>
                    <p class="text-muted small">
                        Définissez un stock minimum pour être alerté lorsque le niveau de stock devient bas.
                        Vous recevrez des notifications lorsque le stock atteint ce seuil.
                    </p>

                    <h6 class="mt-4">Prix par unité</h6>
                    <p class="text-muted small">
                        Le prix par unité est utilisé pour calculer le coût des recettes.
                        Assurez-vous d'utiliser la même unité que celle définie pour l'ingrédient.
                    </p>

                    <h6 class="mt-4">Date d'expiration</h6>
                    <p class="text-muted small mb-0">
                        La date d'expiration est optionnelle mais recommandée pour la gestion des stocks.
                        Vous serez alerté lorsque les ingrédients approchent de leur date d'expiration.
                    </p>
                </div>
            </div>

            {% if ingredient %}
            <!-- Usage Card -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie"></i>
                        Utilisation
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-2">
                        <strong>Utilisé dans {{ ingredient.recipe_items.count() }} recette(s):</strong>
                    </p>
                    {% if ingredient.recipe_items %}
                    <ul class="list-unstyled">
                        {% for item in ingredient.recipe_items %}
                        <li class="mb-2">
                            <i class="fas fa-arrow-right text-muted"></i>
                            {{ item.recipe.product.name }}
                            <small class="text-muted">({{ item.quantity }} {{ ingredient.unit }})</small>
                        </li>
                        {% endfor %}
                    </ul>
                    {% else %}
                    <p class="text-muted mb-0">
                        Cet ingrédient n'est utilisé dans aucune recette.
                    </p>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% if ingredient and ingredient.image_path %}
<!-- Delete Image Modal -->
<div class="modal fade" id="deleteImageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer l'image de cet ingrédient ?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form action="{{ url_for('inventory.delete_ingredient_image', id=ingredient.id) }}" method="POST">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i>
                        Supprimer l'image
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update price unit when unit changes
    const unitSelect = document.getElementById('unit');
    const priceGroup = document.querySelector('#price_per_unit').closest('.input-group');
    const priceUnit = priceGroup.querySelector('.input-group-text');
    
    function updatePriceUnit() {
        priceUnit.textContent = `€/${unitSelect.value || 'unité'}`;
    }
    
    unitSelect.addEventListener('change', updatePriceUnit);

    // Preview image
    const imageInput = document.querySelector('input[type="file"]');
    imageInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.createElement('img');
                preview.src = e.target.result;
                preview.className = 'img-thumbnail mt-2';
                preview.style.maxHeight = '200px';
                
                const existingPreview = imageInput.parentElement.nextElementSibling.nextElementSibling;
                if (existingPreview && existingPreview.tagName === 'DIV') {
                    existingPreview.replaceWith(preview);
                } else {
                    imageInput.parentElement.nextElementSibling.after(preview);
                }
            }
            reader.readAsDataURL(this.files[0]);
        }
    });
});
</script>
{% endblock %} 