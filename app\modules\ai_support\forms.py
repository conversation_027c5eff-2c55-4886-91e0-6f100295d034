from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, IntegerField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, Optional, NumberRange
from .models import SupportTicketPriority, SupportTicketCategory, SupportTicketStatus

class CreateTicketForm(FlaskForm):
    """Formulaire pour créer un nouveau ticket de support"""
    title = StringField('Titre', validators=[
        DataRequired(message='Le titre est requis'),
        Length(min=5, max=200, message='Le titre doit contenir entre 5 et 200 caractères')
    ])
    
    description = TextAreaField('Description', validators=[
        DataRequired(message='La description est requise'),
        Length(min=10, message='La description doit contenir au moins 10 caractères')
    ])
    
    category = SelectField('Catégorie', 
                          choices=[(cat.value, cat.value.replace('_', ' ').title()) 
                                  for cat in SupportTicketCategory],
                          validators=[DataRequired(message='Veuillez sélectionner une catégorie')])
    
    priority = SelectField('Priorité',
                          choices=[(prio.value, prio.value.title()) 
                                  for prio in SupportTicketPriority],
                          default=SupportTicketPriority.MEDIUM.value,
                          validators=[DataRequired(message='Veuillez sélectionner une priorité')])
    
    submit = SubmitField('Créer le ticket')

class UpdateTicketForm(FlaskForm):
    """Formulaire pour mettre à jour un ticket de support"""
    title = StringField('Titre', validators=[
        DataRequired(message='Le titre est requis'),
        Length(min=5, max=200, message='Le titre doit contenir entre 5 et 200 caractères')
    ])
    
    description = TextAreaField('Description', validators=[
        DataRequired(message='La description est requise'),
        Length(min=10, message='La description doit contenir au moins 10 caractères')
    ])
    
    status = SelectField('Statut',
                        choices=[(status.value, status.value.replace('_', ' ').title()) 
                                for status in SupportTicketStatus],
                        validators=[DataRequired(message='Veuillez sélectionner un statut')])
    
    category = SelectField('Catégorie', 
                          choices=[(cat.value, cat.value.replace('_', ' ').title()) 
                                  for cat in SupportTicketCategory],
                          validators=[DataRequired(message='Veuillez sélectionner une catégorie')])
    
    priority = SelectField('Priorité',
                          choices=[(prio.value, prio.value.title()) 
                                  for prio in SupportTicketPriority],
                          validators=[DataRequired(message='Veuillez sélectionner une priorité')])
    
    submit = SubmitField('Mettre à jour')

class AddMessageForm(FlaskForm):
    """Formulaire pour ajouter un message à un ticket"""
    content = TextAreaField('Message', validators=[
        DataRequired(message='Le message est requis'),
        Length(min=1, message='Le message ne peut pas être vide')
    ])
    
    is_internal = BooleanField('Message interne (visible uniquement par les agents)')
    
    submit = SubmitField('Envoyer')

class ChatMessageForm(FlaskForm):
    """Formulaire pour les messages de chat en temps réel"""
    message = StringField('Message', validators=[
        DataRequired(message='Le message est requis'),
        Length(min=1, max=1000, message='Le message doit contenir entre 1 et 1000 caractères')
    ])

class SatisfactionForm(FlaskForm):
    """Formulaire pour l'évaluation de satisfaction"""
    rating = SelectField('Évaluation',
                        choices=[
                            ('1', '1 - Très insatisfait'),
                            ('2', '2 - Insatisfait'),
                            ('3', '3 - Neutre'),
                            ('4', '4 - Satisfait'),
                            ('5', '5 - Très satisfait')
                        ],
                        validators=[DataRequired(message='Veuillez donner une note')])
    
    comment = TextAreaField('Commentaire (optionnel)', validators=[
        Optional(),
        Length(max=500, message='Le commentaire ne peut pas dépasser 500 caractères')
    ])
    
    submit = SubmitField('Envoyer l\'évaluation')

class KnowledgeBaseForm(FlaskForm):
    """Formulaire pour créer/modifier des articles de la base de connaissances"""
    title = StringField('Titre', validators=[
        DataRequired(message='Le titre est requis'),
        Length(min=5, max=200, message='Le titre doit contenir entre 5 et 200 caractères')
    ])
    
    content = TextAreaField('Contenu', validators=[
        DataRequired(message='Le contenu est requis'),
        Length(min=10, message='Le contenu doit contenir au moins 10 caractères')
    ])
    
    category = SelectField('Catégorie', 
                          choices=[(cat.value, cat.value.replace('_', ' ').title()) 
                                  for cat in SupportTicketCategory],
                          validators=[DataRequired(message='Veuillez sélectionner une catégorie')])
    
    tags = StringField('Tags (séparés par des virgules)', validators=[
        Optional(),
        Length(max=500, message='Les tags ne peuvent pas dépasser 500 caractères')
    ])
    
    is_active = BooleanField('Article actif', default=True)
    
    submit = SubmitField('Sauvegarder')

class SearchForm(FlaskForm):
    """Formulaire de recherche"""
    query = StringField('Rechercher', validators=[
        DataRequired(message='Veuillez entrer un terme de recherche'),
        Length(min=2, max=100, message='La recherche doit contenir entre 2 et 100 caractères')
    ])
    
    category = SelectField('Catégorie',
                          choices=[('', 'Toutes les catégories')] + 
                                 [(cat.value, cat.value.replace('_', ' ').title()) 
                                  for cat in SupportTicketCategory],
                          validators=[Optional()])
    
    status = SelectField('Statut',
                        choices=[('', 'Tous les statuts')] + 
                               [(status.value, status.value.replace('_', ' ').title()) 
                                for status in SupportTicketStatus],
                        validators=[Optional()])
    
    submit = SubmitField('Rechercher')

class EscalationForm(FlaskForm):
    """Formulaire pour escalader un ticket vers un agent humain"""
    reason = TextAreaField('Raison de l\'escalade', validators=[
        DataRequired(message='Veuillez expliquer la raison de l\'escalade'),
        Length(min=10, max=500, message='La raison doit contenir entre 10 et 500 caractères')
    ])
    
    priority = SelectField('Nouvelle priorité',
                          choices=[(prio.value, prio.value.title()) 
                                  for prio in SupportTicketPriority],
                          validators=[DataRequired(message='Veuillez sélectionner une priorité')])
    
    submit = SubmitField('Escalader vers un agent')

class AIConfigForm(FlaskForm):
    """Formulaire pour configurer les paramètres de l'IA"""
    confidence_threshold = IntegerField('Seuil de confiance minimum (%)', 
                                       validators=[
                                           DataRequired(message='Le seuil de confiance est requis'),
                                           NumberRange(min=1, max=100, message='Le seuil doit être entre 1 et 100')
                                       ],
                                       default=70)
    
    auto_escalation_enabled = BooleanField('Escalade automatique activée', default=True)
    
    max_conversation_length = IntegerField('Longueur maximale de conversation',
                                          validators=[
                                              DataRequired(message='La longueur maximale est requise'),
                                              NumberRange(min=5, max=100, message='La longueur doit être entre 5 et 100')
                                          ],
                                          default=20)
    
    response_timeout = IntegerField('Timeout de réponse (secondes)',
                                   validators=[
                                       DataRequired(message='Le timeout est requis'),
                                       NumberRange(min=5, max=60, message='Le timeout doit être entre 5 et 60 secondes')
                                   ],
                                   default=30)
    
    submit = SubmitField('Sauvegarder la configuration')
