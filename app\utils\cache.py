import json
from functools import wraps
from flask import current_app
from app import redis_client

def cache_key(*args, **kwargs):
    """Génère une clé de cache unique basée sur les arguments"""
    key_parts = [str(arg) for arg in args]
    key_parts.extend(f"{k}:{v}" for k, v in sorted(kwargs.items()))
    return ":".join(key_parts)

def cache_set(key, data, timeout=300):
    """Stocke des données dans le cache Redis"""
    try:
        redis_client.setex(
            key,
            timeout,
            json.dumps(data, default=str)
        )
        return True
    except Exception as e:
        current_app.logger.error(f"Erreur lors de la mise en cache: {str(e)}")
        return False

def cache_get(key):
    """Récupère des données du cache Redis"""
    try:
        data = redis_client.get(key)
        if data:
            return json.loads(data)
    except Exception as e:
        current_app.logger.error(f"Erreur lors de la récupération du cache: {str(e)}")
    return None

def cache_delete(key):
    """Supprime des données du cache Redis"""
    try:
        redis_client.delete(key)
        return True
    except Exception as e:
        current_app.logger.error(f"Erreur lors de la suppression du cache: {str(e)}")
        return False

def cache_clear_pattern(pattern):
    """Supprime toutes les clés correspondant à un motif"""
    try:
        keys = redis_client.keys(pattern)
        if keys:
            redis_client.delete(*keys)
        return True
    except Exception as e:
        current_app.logger.error(f"Erreur lors de la suppression du cache par motif: {str(e)}")
        return False

def cached(timeout=300, key_prefix=''):
    """Décorateur pour mettre en cache le résultat d'une fonction"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Générer la clé de cache
            cache_prefix = key_prefix or f.__name__
            key = f"{cache_prefix}:{cache_key(*args, **kwargs)}"
            
            # Vérifier si le résultat est dans le cache
            result = cache_get(key)
            if result is not None:
                return result
            
            # Si non, exécuter la fonction et mettre en cache le résultat
            result = f(*args, **kwargs)
            cache_set(key, result, timeout)
            return result
        return decorated_function
    return decorator

def invalidate_cache(key_prefix):
    """Décorateur pour invalider le cache après l'exécution d'une fonction"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            result = f(*args, **kwargs)
            cache_clear_pattern(f"{key_prefix}:*")
            return result
        return decorated_function
    return decorator

def memoize(timeout=300):
    """Décorateur pour mettre en cache les résultats d'une fonction avec ses arguments"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Créer une clé unique basée sur la fonction et ses arguments
            key = f"memoize:{f.__name__}:{cache_key(*args, **kwargs)}"
            
            # Vérifier le cache
            result = cache_get(key)
            if result is not None:
                return result
            
            # Exécuter la fonction et mettre en cache
            result = f(*args, **kwargs)
            cache_set(key, result, timeout)
            return result
        return decorated_function
    return decorator 