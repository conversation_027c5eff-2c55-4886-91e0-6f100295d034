from flask import render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app.extensions import db
from app.utils.decorators import inventory_access_required as permission_required
from app.modules.inventory.models_bank_account import BankAccount, BankOperation, BankOperationType
from app.modules.inventory.forms_bank_management import (
    BankAccountForm, BankOperationForm, BankTransferForm, 
    CashToBankDepositForm, BankReconciliationForm, BankAccountSettingsForm
)
from app.modules.cash_register.models_cash_register import CashRegister, CashOperation, CashRegisterOperationType
from datetime import datetime
from . import bp

@bp.route('/bank-management')
@login_required
@permission_required
def bank_management_index():
    """Page principale de gestion bancaire"""
    # Récupérer tous les comptes bancaires de l'utilisateur
    bank_accounts = BankAccount.query.filter_by(
        owner_id=current_user.id,
        is_active=True
    ).order_by(BankAccount.is_default.desc(), BankAccount.name).all()
    
    # Calculer les statistiques
    total_balance = sum(account.balance for account in bank_accounts)
    overdrawn_accounts = [account for account in bank_accounts if account.is_overdrawn]
    
    # Récupérer les dernières opérations
    recent_operations = BankOperation.query.filter_by(
        owner_id=current_user.id
    ).order_by(BankOperation.operation_date.desc()).limit(10).all()
    
    stats = {
        'total_accounts': len(bank_accounts),
        'total_balance': total_balance,
        'overdrawn_accounts': len(overdrawn_accounts),
        'recent_operations_count': len(recent_operations)
    }
    
    return render_template('inventory/bank_management/index.html',
                         bank_accounts=bank_accounts,
                         recent_operations=recent_operations,
                         stats=stats)

@bp.route('/bank-management/accounts/add', methods=['GET', 'POST'])
@login_required
@permission_required
def add_bank_account():
    """Ajouter un nouveau compte bancaire"""
    form = BankAccountForm()
    
    if form.validate_on_submit():
        try:
            # Si c'est le premier compte ou si défini comme défaut
            if form.is_default.data or not BankAccount.query.filter_by(owner_id=current_user.id).first():
                # Retirer le statut par défaut des autres comptes
                BankAccount.query.filter_by(owner_id=current_user.id).update({'is_default': False})
            
            account = BankAccount(
                name=form.name.data,
                account_number=form.account_number.data,
                iban=form.iban.data,
                bic=form.bic.data,
                bank_name=form.bank_name.data,
                bank_address=form.bank_address.data,
                balance=form.initial_balance.data,
                initial_balance=form.initial_balance.data,
                overdraft_limit=form.overdraft_limit.data,
                currency=form.currency.data,
                is_default=form.is_default.data or not BankAccount.query.filter_by(owner_id=current_user.id).first(),
                description=form.description.data,
                notes=form.notes.data,
                owner_id=current_user.id
            )
            
            db.session.add(account)
            
            # Créer l'opération d'ouverture si solde initial > 0
            if form.initial_balance.data > 0:
                opening_operation = BankOperation(
                    bank_account=account,
                    type=BankOperationType.DEPOSIT,
                    amount=form.initial_balance.data,
                    description="Solde d'ouverture du compte",
                    user_id=current_user.id,
                    owner_id=current_user.id
                )
                db.session.add(opening_operation)
            
            db.session.commit()
            flash('Compte bancaire créé avec succès', 'success')
            return redirect(url_for('inventory.bank_management_index'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la création du compte: {str(e)}', 'error')
    
    return render_template('inventory/bank_management/add_account.html', form=form)



@bp.route('/bank-management/accounts/<int:account_id>')
@login_required
@permission_required
def bank_account_details(account_id):
    """Détails d'un compte bancaire"""
    account = BankAccount.query.filter_by(
        id=account_id,
        owner_id=current_user.id
    ).first_or_404()
    
    # Récupérer les opérations du compte
    page = request.args.get('page', 1, type=int)
    operations = BankOperation.query.filter_by(
        bank_account_id=account_id
    ).order_by(BankOperation.operation_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('inventory/bank_management/account_details.html',
                         account=account, operations=operations)

@bp.route('/bank-management/accounts/<int:account_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required
def edit_bank_account(account_id):
    """Modifier un compte bancaire"""
    account = BankAccount.query.filter_by(
        id=account_id,
        owner_id=current_user.id
    ).first_or_404()
    
    form = BankAccountForm(obj=account)
    
    if form.validate_on_submit():
        try:
            # Si défini comme défaut, retirer le statut des autres comptes
            if form.is_default.data and not account.is_default:
                BankAccount.query.filter_by(owner_id=current_user.id).update({'is_default': False})
            
            form.populate_obj(account)
            account.updated_at = datetime.utcnow()
            
            db.session.commit()
            flash('Compte bancaire mis à jour avec succès', 'success')
            return redirect(url_for('inventory.bank_account_details', account_id=account_id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la mise à jour: {str(e)}', 'error')
    
    return render_template('inventory/bank_management/edit_account.html', form=form, account=account)

@bp.route('/bank-management/operations/add', methods=['GET', 'POST'])
@login_required
@permission_required
def add_bank_operation():
    """Ajouter une opération bancaire"""
    form = BankOperationForm()
    
    if form.validate_on_submit():
        try:
            account = BankAccount.query.filter_by(
                id=form.bank_account_id.data,
                owner_id=current_user.id
            ).first_or_404()
            
            # Déterminer le montant (positif pour crédit, négatif pour débit)
            amount = form.amount.data
            if form.operation_type.data in ['withdrawal', 'transfer_out', 'supplier_payment', 'bank_fees']:
                amount = -amount
            
            # Vérifier si l'opération est possible
            if amount < 0 and not account.can_withdraw(abs(amount)):
                flash('Solde insuffisant pour cette opération', 'error')
                return render_template('inventory/bank_management/add_operation.html', form=form)
            
            operation = BankOperation(
                bank_account=account,
                type=BankOperationType.from_str(form.operation_type.data),
                amount=amount,
                description=form.description.data,
                reference=form.reference.data,
                operation_date=form.operation_date.data or datetime.utcnow(),
                value_date=form.value_date.data,
                notes=form.notes.data,
                user_id=current_user.id,
                owner_id=current_user.id
            )
            
            # Mettre à jour le solde du compte
            account.balance += amount
            
            db.session.add(operation)
            db.session.commit()
            
            flash('Opération bancaire enregistrée avec succès', 'success')
            return redirect(url_for('inventory.bank_account_details', account_id=account.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de l\'enregistrement: {str(e)}', 'error')
    
    return render_template('inventory/bank_management/add_operation.html', form=form)

@bp.route('/bank-management/transfer', methods=['GET', 'POST'])
@login_required
@permission_required
def bank_transfer():
    """Effectuer un virement entre comptes"""
    form = BankTransferForm()
    
    if form.validate_on_submit():
        try:
            if form.source_account_id.data == form.target_account_id.data:
                flash('Les comptes source et destinataire doivent être différents', 'error')
                return render_template('inventory/bank_management/transfer.html', form=form)
            
            source_account = BankAccount.query.filter_by(
                id=form.source_account_id.data,
                owner_id=current_user.id
            ).first_or_404()
            
            target_account = BankAccount.query.filter_by(
                id=form.target_account_id.data,
                owner_id=current_user.id
            ).first_or_404()
            
            success, message = source_account.transfer_to(
                target_account=target_account,
                amount=form.amount.data,
                description=form.description.data,
                reference=form.reference.data,
                user_id=current_user.id
            )
            
            if success:
                flash(message, 'success')
                return redirect(url_for('inventory.bank_management_index'))
            else:
                flash(message, 'error')
                
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors du virement: {str(e)}', 'error')
    
    # Préparer les données JSON pour JavaScript
    import json

    bank_accounts = BankAccount.query.filter_by(
        owner_id=current_user.id,
        is_active=True
    ).all()

    bank_accounts_json = json.dumps([{
        'id': account.id,
        'name': account.name,
        'balance': account.balance
    } for account in bank_accounts])

    return render_template('inventory/bank_management/transfer.html',
                         form=form,
                         bank_accounts=bank_accounts,
                         bank_accounts_json=bank_accounts_json)

@bp.route('/bank-management/cash-deposit', methods=['GET', 'POST'])
@login_required
@permission_required
def cash_to_bank_deposit():
    """Dépôt depuis la caisse vers un compte bancaire"""
    from app.modules.inventory.forms_bank_management import CashToBankDepositForm as CashDepositForm

    form = CashDepositForm()

    # Peupler les choix de comptes bancaires
    bank_accounts = BankAccount.query.filter_by(
        owner_id=current_user.id,
        is_active=True
    ).all()

    form.bank_account_id.choices = [
        (account.id, f"{account.name} ({account.balance:.2f} €)")
        for account in bank_accounts
    ]

    # Peupler les choix de caisses
    cash_registers = []
    try:
        from app.modules.cash_register.models_cash_register import CashRegister
        cash_registers = CashRegister.query.filter_by(
            owner_id=current_user.id,
            is_open=True
        ).all()

        form.cash_register_id.choices = []
        for register in cash_registers:
            # Calculer le solde total EXACTEMENT comme dans la page caisse
            total_sales = 0
            total_deposits = 0
            total_withdrawals = 0

            if register.last_opened_at:
                all_operations = CashOperation.query.filter(
                    CashOperation.register_id == register.id,
                    CashOperation.date >= register.last_opened_at
                ).all()

                for op in all_operations:
                    if op.type == CashRegisterOperationType.SALE:
                        total_sales += op.amount
                    elif op.type == CashRegisterOperationType.CASH_IN:
                        total_deposits += op.amount
                    elif op.type == CashRegisterOperationType.CASH_OUT:
                        total_withdrawals += abs(op.amount)
                    elif op.type == CashRegisterOperationType.SUPPLIER_PAYMENT:
                        total_withdrawals += abs(op.amount)
                    elif op.type == CashRegisterOperationType.BANK_DEPOSIT:
                        total_withdrawals += abs(op.amount)

            # Calcul final identique à la page caisse
            total_balance = register.float_amount + total_sales + total_deposits - total_withdrawals
            current_app.logger.debug(f"DEPOSIT PAGE - Register {register.name}: float={register.float_amount}, sales={total_sales}, deposits={total_deposits}, withdrawals={total_withdrawals}, total={total_balance}")

            form.cash_register_id.choices.append(
                (register.id, f"{register.name} ({total_balance:.2f} €)")
            )
    except ImportError:
        form.cash_register_id.choices = []

    if form.validate_on_submit():
        try:
            bank_account = BankAccount.query.filter_by(
                id=form.bank_account_id.data,
                owner_id=current_user.id
            ).first_or_404()

            cash_register = None
            if cash_registers:
                cash_register = next((r for r in cash_registers if r.id == form.cash_register_id.data), None)

            if not cash_register:
                flash('Caisse invalide', 'error')
            else:
                success, message = bank_account.deposit_from_cash_register(
                    amount=form.amount.data,
                    cash_register=cash_register,
                    user_id=current_user.id
                )

                if success:
                    flash('Dépôt effectué avec succès', 'success')
                    return redirect(url_for('inventory.bank_management_index'))
                else:
                    flash(f'Erreur: {message}', 'error')

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Erreur lors du dépôt: {str(e)}")
            flash('Erreur lors du dépôt', 'error')

    # Préparer les données JSON pour JavaScript
    import json

    bank_accounts_json = json.dumps([{
        'id': account.id,
        'name': account.name,
        'balance': account.balance
    } for account in bank_accounts])

    cash_registers_data = []
    for register in cash_registers:
        # Calculer le solde total EXACTEMENT comme dans la page caisse
        total_sales = 0
        total_deposits = 0
        total_withdrawals = 0

        if register.last_opened_at:
            all_operations = CashOperation.query.filter(
                CashOperation.register_id == register.id,
                CashOperation.date >= register.last_opened_at
            ).all()

            for op in all_operations:
                if op.type == CashRegisterOperationType.SALE:
                    total_sales += op.amount
                elif op.type == CashRegisterOperationType.CASH_IN:
                    total_deposits += op.amount
                elif op.type == CashRegisterOperationType.CASH_OUT:
                    total_withdrawals += abs(op.amount)
                elif op.type == CashRegisterOperationType.SUPPLIER_PAYMENT:
                    total_withdrawals += abs(op.amount)
                elif op.type == CashRegisterOperationType.BANK_DEPOSIT:
                    total_withdrawals += abs(op.amount)

        # Calcul final identique à la page caisse
        total_balance = register.float_amount + total_sales + total_deposits - total_withdrawals
        current_app.logger.debug(f"DEPOSIT JSON - Register {register.name}: float={register.float_amount}, sales={total_sales}, deposits={total_deposits}, withdrawals={total_withdrawals}, total={total_balance}")

        cash_registers_data.append({
            'id': register.id,
            'name': register.name,
            'current_balance': register.current_balance,
            'total_balance': total_balance
        })

    cash_registers_json = json.dumps(cash_registers_data)

    # Préparer les données des caisses avec soldes calculés pour le template
    cash_registers_with_totals = []
    for register in cash_registers:
        # Calculer le solde total EXACTEMENT comme dans la page caisse
        total_sales = 0
        total_deposits = 0
        total_withdrawals = 0

        if register.last_opened_at:
            all_operations = CashOperation.query.filter(
                CashOperation.register_id == register.id,
                CashOperation.date >= register.last_opened_at
            ).all()

            for op in all_operations:
                if op.type == CashRegisterOperationType.SALE:
                    total_sales += op.amount
                elif op.type == CashRegisterOperationType.CASH_IN:
                    total_deposits += op.amount
                elif op.type == CashRegisterOperationType.CASH_OUT:
                    total_withdrawals += abs(op.amount)
                elif op.type == CashRegisterOperationType.SUPPLIER_PAYMENT:
                    total_withdrawals += abs(op.amount)
                elif op.type == CashRegisterOperationType.BANK_DEPOSIT:
                    total_withdrawals += abs(op.amount)

        # Calcul final identique à la page caisse
        total_balance = register.float_amount + total_sales + total_deposits - total_withdrawals
        current_app.logger.debug(f"DEPOSIT TEMPLATE - Register {register.name}: float={register.float_amount}, sales={total_sales}, deposits={total_deposits}, withdrawals={total_withdrawals}, total={total_balance}")

        # Créer un objet avec les données nécessaires
        register_data = {
            'id': register.id,
            'name': register.name,
            'current_balance': register.current_balance,
            'total_balance': total_balance,
            'float_amount': register.float_amount,
            'is_open': register.is_open
        }
        cash_registers_with_totals.append(register_data)

    return render_template('inventory/bank_management/cash_deposit.html',
                         form=form,
                         bank_accounts=bank_accounts,
                         cash_registers=cash_registers,
                         cash_registers_with_totals=cash_registers_with_totals,
                         bank_accounts_json=bank_accounts_json,
                         cash_registers_json=cash_registers_json)

@bp.route('/bank-management/accounts/<int:account_id>/set-default', methods=['POST'])
@login_required
@permission_required
def set_default_account(account_id):
    """Définir un compte comme compte par défaut"""
    success = BankAccount.set_default(account_id, current_user.id)
    
    if success:
        flash('Compte défini comme compte par défaut', 'success')
    else:
        flash('Erreur lors de la définition du compte par défaut', 'error')
    
    return redirect(url_for('inventory.bank_management_index'))

@bp.route('/bank-management/accounts/<int:account_id>/deactivate', methods=['POST'])
@login_required
@permission_required
def deactivate_account(account_id):
    """Désactiver un compte bancaire"""
    account = BankAccount.query.filter_by(
        id=account_id,
        owner_id=current_user.id
    ).first_or_404()
    
    if account.is_default:
        flash('Impossible de désactiver le compte par défaut', 'error')
    else:
        account.is_active = False
        db.session.commit()
        flash('Compte désactivé avec succès', 'success')
    
    return redirect(url_for('inventory.bank_management_index'))

@bp.route('/bank-management/api/account-balance/<int:account_id>')
@login_required
@permission_required
def get_account_balance(account_id):
    """API pour récupérer le solde d'un compte"""
    account = BankAccount.query.filter_by(
        id=account_id,
        owner_id=current_user.id
    ).first_or_404()
    
    return jsonify({
        'balance': account.balance,
        'available_balance': account.available_balance,
        'is_overdrawn': account.is_overdrawn,
        'overdraft_amount': account.overdraft_amount
    })

@bp.route('/bank-management/reconciliation', methods=['GET', 'POST'])
@login_required
@permission_required
def bank_reconciliation():
    """Réconciliation bancaire"""
    from app.modules.inventory.forms_bank_management import BankReconciliationForm

    form = BankReconciliationForm()

    # Peupler les choix de comptes bancaires
    bank_accounts = BankAccount.query.filter_by(
        owner_id=current_user.id,
        is_active=True
    ).all()

    form.bank_account_id.choices = [
        (account.id, f"{account.name} ({account.balance:.2f} €)")
        for account in bank_accounts
    ]

    if form.validate_on_submit():
        try:
            account = BankAccount.query.filter_by(
                id=form.bank_account_id.data,
                owner_id=current_user.id
            ).first_or_404()

            difference = form.statement_balance.data - account.balance

            if abs(difference) > 0.01:  # Tolérance de 1 centime
                # Créer une opération d'ajustement
                adjustment_operation = BankOperation(
                    bank_account=account,
                    operation_type=BankOperationType.ADJUSTMENT,
                    amount=abs(difference),
                    description=f"Ajustement de réconciliation - Relevé du {form.statement_date.data.strftime('%d/%m/%Y')}",
                    notes=form.notes.data,
                    operation_date=form.statement_date.data,
                    user_id=current_user.id,
                    owner_id=current_user.id
                )

                # Ajuster le solde
                if difference > 0:
                    account.balance += difference
                else:
                    account.balance -= abs(difference)

                db.session.add(adjustment_operation)
                db.session.commit()

                flash(f'Réconciliation effectuée avec ajustement de {difference:.2f}€', 'success')
            else:
                flash('Compte déjà réconcilié - aucun ajustement nécessaire', 'info')

            return redirect(url_for('inventory.bank_management_index'))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Erreur lors de la réconciliation: {str(e)}")
            flash('Erreur lors de la réconciliation', 'error')

    # Préparer les données JSON pour JavaScript
    import json

    bank_accounts_json = json.dumps([{
        'id': account.id,
        'name': account.name,
        'balance': account.balance
    } for account in bank_accounts])

    # Réconciliations récentes (fictives pour l'instant)
    recent_reconciliations = []

    return render_template('inventory/bank_management/reconciliation.html',
                         form=form,
                         bank_accounts=bank_accounts,
                         bank_accounts_json=bank_accounts_json,
                         recent_reconciliations=recent_reconciliations)
