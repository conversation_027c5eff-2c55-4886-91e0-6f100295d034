- On a pas encore de rapport des ventes par Salle et par Table et par categorie des produits et par produit pour les ventes depuis POS et meme chose pour les ventes en ligne et avec filtrages.

- les ventes en lignes n'existent pas encore dans toutes les pages des rapport dans le module: app\modules\reports\

- les images des produits et categories ne s'affichent pas encore dans le site de commandes en ligne et l'UI/UX n'est pas bien amelioré ou optimisé et je veux que plusieurs produits s'affichent par ligne et pas que tout soit en colonne et meme chose pour les categories de produits

- je veux aussi que depuis le site de commandes en ligne les clients peuvent ajouter des des categories et des produits aux favoris pour pouvoir les commander plus rapidement sans devoir les rechercher à chaque fois.

- et dans le site de commandes en ligne il y a aussi dans cette page: http://azerty1.lvh.me:5000/customer/orders cette erreur: TypeError
TypeError: object of type 'AppenderQuery' has no len()