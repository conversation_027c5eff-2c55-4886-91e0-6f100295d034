{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-table text-primary"></i>
            {{ title }}
        </h1>
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-download"></i> Exporter
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="exportTablesReport('pdf')">
                    <i class="fas fa-file-pdf text-danger"></i> PDF
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="exportTablesReport('excel')">
                    <i class="fas fa-file-excel text-success"></i> Excel
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="exportTablesReport('csv')">
                    <i class="fas fa-file-csv text-info"></i> CSV
                </a></li>
            </ul>
        </div>
    </div>

    <!-- Period Filter -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" class="row align-items-end">
                <div class="col-md-3">
                    <label class="form-label">Période</label>
                    <select name="period" class="form-select" id="period" onchange="this.form.submit()">
                        <option value="today" {% if period == 'today' %}selected{% endif %}>Aujourd'hui</option>
                        <option value="week" {% if period == 'week' %}selected{% endif %}>Cette semaine</option>
                        <option value="month" {% if period == 'month' %}selected{% endif %}>Ce mois</option>
                        <option value="quarter" {% if period == 'quarter' %}selected{% endif %}>Ce trimestre</option>
                        <option value="year" {% if period == 'year' %}selected{% endif %}>Cette année</option>
                        <option value="custom" {% if period == 'custom' %}selected{% endif %}>Personnalisé</option>
                    </select>
                </div>
                
                <div class="col-md-3" id="custom-dates" style="display: {% if period == 'custom' %}block{% else %}none{% endif %};">
                    <label class="form-label">Du</label>
                    <input type="date" name="start_date" class="form-control" value="{{ start_date.strftime('%Y-%m-%d') if start_date }}">
                </div>
                
                <div class="col-md-3" id="custom-dates-end" style="display: {% if period == 'custom' %}block{% else %}none{% endif %};">
                    <label class="form-label">Au</label>
                    <input type="date" name="end_date" class="form-control" value="{{ end_date.strftime('%Y-%m-%d') if end_date }}">
                </div>
                
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filtrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Chiffre d'affaires total
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ "%.2f"|format(total_revenue) }}€
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-euro-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total commandes
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_orders }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Nombre de salles
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ rooms|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-door-open fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Nombre de tables
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ tables|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-table fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Performers -->
    <div class="row mb-4">
        <!-- Top Salles -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-trophy"></i> Top 5 Salles par CA
                    </h6>
                </div>
                <div class="card-body">
                    {% if top_rooms %}
                        {% for room_data in top_rooms %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>{{ room_data.room.name }}</strong>
                                <br>
                                <small class="text-muted">{{ room_data.orders_count }} commande(s)</small>
                            </div>
                            <div class="text-end">
                                <span class="h6 text-primary">{{ "%.2f"|format(room_data.revenue) }}€</span>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucune donnée disponible</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Top Tables -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-medal"></i> Top 5 Tables par CA
                    </h6>
                </div>
                <div class="card-body">
                    {% if top_tables %}
                        {% for table_data in top_tables %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>{{ table_data.table.display_name }}</strong>
                                <br>
                                <small class="text-muted">{{ table_data.orders_count }} commande(s) • Panier moyen: {{ "%.2f"|format(table_data.average_order) }}€</small>
                            </div>
                            <div class="text-end">
                                <span class="h6 text-primary">{{ "%.2f"|format(table_data.revenue) }}€</span>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucune donnée disponible</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Tables -->
    <div class="row">
        <!-- Rapport par Salle -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-door-open"></i> Rapport par Salle
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="roomsTable">
                            <thead>
                                <tr>
                                    <th>Salle</th>
                                    <th>Tables</th>
                                    <th>Commandes</th>
                                    <th>CA Total</th>
                                    <th>CA Moyen</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for room in rooms %}
                                {% set stats = room_stats[room.id] %}
                                <tr>
                                    <td>
                                        <strong>{{ room.name }}</strong>
                                        {% if room.description %}
                                        <br><small class="text-muted">{{ room.description }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ stats.tables_count }}</td>
                                    <td>{{ stats.orders_count }}</td>
                                    <td>{{ "%.2f"|format(stats.revenue) }}€</td>
                                    <td>{{ "%.2f"|format(stats.revenue / stats.orders_count if stats.orders_count > 0 else 0) }}€</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rapport par Table -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-table"></i> Rapport par Table
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="tablesTable">
                            <thead>
                                <tr>
                                    <th>Table</th>
                                    <th>Salle</th>
                                    <th>Commandes</th>
                                    <th>CA Total</th>
                                    <th>CA Moyen</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for table in tables %}
                                {% set stats = table_stats[table.id] %}
                                <tr>
                                    <td>
                                        <strong>Table {{ table.number }}</strong>
                                        <br><small class="text-muted">{{ table.capacity }} places</small>
                                    </td>
                                    <td>{{ table.room.name if table.room else 'N/A' }}</td>
                                    <td>{{ stats.orders_count }}</td>
                                    <td>{{ "%.2f"|format(stats.revenue) }}€</td>
                                    <td>{{ "%.2f"|format(stats.average_order) }}€</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Initialize DataTables
    $('#roomsTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
        },
        "order": [[3, "desc"]] // Trier par CA Total décroissant
    });
    
    $('#tablesTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
        },
        "order": [[3, "desc"]] // Trier par CA Total décroissant
    });
    
    // Handle period change
    $('#period').change(function() {
        const period = $(this).val();
        if (period === 'custom') {
            $('#custom-dates, #custom-dates-end').show();
        } else {
            $('#custom-dates, #custom-dates-end').hide();
        }
    });
});

function exportTablesReport(format) {
    // Get current filters
    const period = document.getElementById('period').value;
    
    // Build URL with parameters
    let url = new URL(`${window.location.origin}/reports/tables/export`);
    url.searchParams.set('period', period);
    url.searchParams.set('format', format);
    
    if (period === 'custom') {
        const startDate = document.querySelector('input[name="start_date"]').value;
        const endDate = document.querySelector('input[name="end_date"]').value;
        url.searchParams.set('start_date', startDate);
        url.searchParams.set('end_date', endDate);
    }
    
    // Trigger download
    window.location.href = url.toString();
}
</script>
{% endblock %}
