import pytest
from flask import url_for
from app.modules.inventory.models_product import Product
from app.modules.pos.models_sale import Sale, SaleItem, SaleStatus
from app.modules.cash_register.models_cash_register import PaymentMethod
from app.extensions import db

def test_complete_sale_flow(client, test_user, app):
    """
    Test d'intégration du flux complet d'une vente
    """
    with app.app_context():
        # Connexion de l'utilisateur
        client.post('/auth/login', data={
            'username': test_user.username,
            'password': 'test123'
        })

        # Création d'un produit de test
        product = Product(
            name='Test Product',
            price=10.99,
            stock_quantity=100,
            owner_id=test_user.id,
            category_id=1
        )
        db.session.add(product)
        db.session.commit()

        # Simulation d'une vente
        sale_data = {
            'items': [{
                'product_id': product.id,
                'quantity': 2,
                'price': product.price
            }],
            'payment_method': PaymentMethod.CASH.value
        }

        # Traitement du paiement
        response = client.post('/pos/process_payment',
                             json=sale_data,
                             content_type='application/json')
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True

        # Vérification de la mise à jour du stock
        updated_product = Product.query.get(product.id)
        assert updated_product.stock_quantity == 98

        # Vérification de la création de la vente
        sale = Sale.query.get(data['sale_id'])
        assert sale is not None
        assert sale.status == SaleStatus.PAID
        assert sale.total == product.price * 2

def test_insufficient_stock(client, test_user, app):
    """
    Test d'intégration pour la vérification du stock insuffisant
    """
    with app.app_context():
        # Connexion de l'utilisateur
        client.post('/auth/login', data={
            'username': test_user.username,
            'password': 'test123'
        })

        # Création d'un produit avec stock limité
        product = Product(
            name='Limited Product',
            price=10.99,
            stock_quantity=1,
            owner_id=test_user.id,
            category_id=1
        )
        db.session.add(product)
        db.session.commit()

        # Tentative de vente avec quantité supérieure au stock
        sale_data = {
            'items': [{
                'product_id': product.id,
                'quantity': 2,
                'price': product.price
            }],
            'payment_method': PaymentMethod.CASH.value
        }

        response = client.post('/pos/process_payment',
                             json=sale_data,
                             content_type='application/json')
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is False
        assert 'Stock insuffisant' in data['error']

def test_multiple_products_sale(client, test_user, app):
    """
    Test d'intégration pour une vente avec plusieurs produits
    """
    with app.app_context():
        # Connexion de l'utilisateur
        client.post('/auth/login', data={
            'username': test_user.username,
            'password': 'test123'
        })

        # Création de plusieurs produits
        product1 = Product(
            name='Product 1',
            price=10.99,
            stock_quantity=50,
            owner_id=test_user.id,
            category_id=1
        )
        product2 = Product(
            name='Product 2',
            price=15.99,
            stock_quantity=30,
            owner_id=test_user.id,
            category_id=1
        )
        db.session.add_all([product1, product2])
        db.session.commit()

        # Vente avec plusieurs produits
        sale_data = {
            'items': [
                {
                    'product_id': product1.id,
                    'quantity': 2,
                    'price': product1.price
                },
                {
                    'product_id': product2.id,
                    'quantity': 1,
                    'price': product2.price
                }
            ],
            'payment_method': PaymentMethod.CASH.value
        }

        response = client.post('/pos/process_payment',
                             json=sale_data,
                             content_type='application/json')
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True

        # Vérification des stocks mis à jour
        assert Product.query.get(product1.id).stock_quantity == 48
        assert Product.query.get(product2.id).stock_quantity == 29

        # Vérification du total de la vente
        sale = Sale.query.get(data['sale_id'])
        expected_total = (product1.price * 2) + (product2.price * 1)
        assert sale.total == expected_total 