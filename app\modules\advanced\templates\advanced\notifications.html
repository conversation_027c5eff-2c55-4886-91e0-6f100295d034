{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Notifications</h1>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newNotificationModal">
            <i class="fas fa-plus fa-sm"></i> Nouvelle notification
        </button>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Notification Rules Card -->
        <div class="col-xl-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Règles de notification</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Événement</th>
                                    <th>Condition</th>
                                    <th>Canal</th>
                                    <th>Destinataires</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Stock bas</td>
                                    <td>Quantité < seuil minimum</td>
                                    <td>Email, SMS</td>
                                    <td><EMAIL></td>
                                    <td><span class="badge bg-success">Actif</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-info" title="Éditer">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="Suspendre">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Notification History Card -->
        <div class="col-xl-8 col-lg-7 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Historique des notifications</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Canal</th>
                                    <th>Destinataire</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2024-03-20 10:30:00</td>
                                    <td>Stock bas</td>
                                    <td>Email</td>
                                    <td><EMAIL></td>
                                    <td><span class="badge bg-success">Envoyé</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#notificationDetailsModal">
                                            <i class="fas fa-info-circle"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="Renvoyer">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Channels Card -->
        <div class="col-xl-4 col-lg-5 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Canaux de notification</h6>
                </div>
                <div class="card-body">
                    <!-- Email Channel -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">Email</h6>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="emailChannel" checked>
                            </div>
                        </div>
                        <div class="small text-gray-500">
                            Configuration SMTP active
                            <a href="#" class="float-end">Configurer</a>
                        </div>
                    </div>

                    <!-- SMS Channel -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">SMS</h6>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="smsChannel">
                            </div>
                        </div>
                        <div class="small text-gray-500">
                            Non configuré
                            <a href="#" class="float-end">Configurer</a>
                        </div>
                    </div>

                    <!-- Slack Channel -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">Slack</h6>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="slackChannel">
                            </div>
                        </div>
                        <div class="small text-gray-500">
                            Non configuré
                            <a href="#" class="float-end">Configurer</a>
                        </div>
                    </div>

                    <!-- Discord Channel -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">Discord</h6>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="discordChannel">
                            </div>
                        </div>
                        <div class="small text-gray-500">
                            Non configuré
                            <a href="#" class="float-end">Configurer</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Notification Modal -->
<div class="modal fade" id="newNotificationModal" tabindex="-1" aria-labelledby="newNotificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newNotificationModalLabel">Nouvelle règle de notification</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="eventType" class="form-label">Type d'événement</label>
                        <select class="form-select" id="eventType" required>
                            <option value="">Sélectionner...</option>
                            <option value="stock">Stock bas</option>
                            <option value="sales">Objectif de ventes</option>
                            <option value="error">Erreur système</option>
                            <option value="backup">Sauvegarde</option>
                            <option value="custom">Personnalisé</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="condition" class="form-label">Condition</label>
                        <textarea class="form-control" id="condition" rows="3" placeholder="stock_quantity < minimum_stock"></textarea>
                        <div class="form-text">Expression de condition (optionnel)</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Canaux</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="emailNotif">
                            <label class="form-check-label" for="emailNotif">Email</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="smsNotif">
                            <label class="form-check-label" for="smsNotif">SMS</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="slackNotif">
                            <label class="form-check-label" for="slackNotif">Slack</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="discordNotif">
                            <label class="form-check-label" for="discordNotif">Discord</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="recipients" class="form-label">Destinataires</label>
                        <input type="text" class="form-control" id="recipients" placeholder="<EMAIL>, +33612345678">
                        <div class="form-text">Séparez les destinataires par des virgules</div>
                    </div>
                    <div class="mb-3">
                        <label for="template" class="form-label">Modèle de message</label>
                        <textarea class="form-control" id="template" rows="5"></textarea>
                        <div class="form-text">Variables disponibles : {event}, {date}, {details}, etc.</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary">Créer</button>
            </div>
        </div>
    </div>
</div>

<!-- Notification Details Modal -->
<div class="modal fade" id="notificationDetailsModal" tabindex="-1" aria-labelledby="notificationDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notificationDetailsModalLabel">Détails de la notification</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <h6>Informations</h6>
                    <dl class="row">
                        <dt class="col-sm-3">Date d'envoi</dt>
                        <dd class="col-sm-9">2024-03-20 10:30:00</dd>
                        
                        <dt class="col-sm-3">Type</dt>
                        <dd class="col-sm-9">Stock bas</dd>
                        
                        <dt class="col-sm-3">Canal</dt>
                        <dd class="col-sm-9">Email</dd>
                        
                        <dt class="col-sm-3">Destinataire</dt>
                        <dd class="col-sm-9"><EMAIL></dd>
                        
                        <dt class="col-sm-3">Statut</dt>
                        <dd class="col-sm-9"><span class="badge bg-success">Envoyé</span></dd>
                    </dl>
                </div>
                <div class="mb-3">
                    <h6>Contenu du message</h6>
                    <pre class="bg-light p-3">
Alerte stock bas

Le produit "Café Expresso" a atteint son seuil minimum de stock.
- Stock actuel : 5
- Seuil minimum : 10

Veuillez réapprovisionner le stock dès que possible.

Cordialement,
Système de notification</pre>
                </div>
                <div class="mb-3">
                    <h6>Historique des tentatives</h6>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>2024-03-20 10:30:00</strong>
                                    <br>
                                    <small class="text-muted">Première tentative</small>
                                </div>
                                <span class="badge bg-success">Succès</span>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary">Télécharger les logs</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Event type change handler
    const eventType = document.getElementById('eventType');
    const condition = document.getElementById('condition');
    const template = document.getElementById('template');
    
    eventType.addEventListener('change', function() {
        let defaultCondition = '';
        let defaultTemplate = '';
        
        switch(this.value) {
            case 'stock':
                defaultCondition = 'stock_quantity < minimum_stock';
                defaultTemplate = 'Alerte stock bas\n\nLe produit "{product_name}" a atteint son seuil minimum de stock.\n- Stock actuel : {current_stock}\n- Seuil minimum : {minimum_stock}\n\nVeuillez réapprovisionner le stock dès que possible.';
                break;
            case 'sales':
                defaultCondition = 'daily_sales > sales_target';
                defaultTemplate = 'Objectif de ventes atteint !\n\nFélicitations ! L\'objectif de ventes a été atteint.\n- Ventes du jour : {daily_sales}\n- Objectif : {sales_target}';
                break;
            case 'error':
                defaultCondition = 'error_level >= "ERROR"';
                defaultTemplate = 'Erreur système détectée\n\nUne erreur système est survenue :\n- Type : {error_type}\n- Message : {error_message}\n- Stack trace : {stack_trace}';
                break;
            case 'backup':
                defaultCondition = 'backup_status != "SUCCESS"';
                defaultTemplate = 'Statut de la sauvegarde\n\nLa sauvegarde {backup_id} s\'est terminée avec le statut : {backup_status}\n- Début : {start_time}\n- Fin : {end_time}\n- Taille : {backup_size}';
                break;
        }
        
        condition.value = defaultCondition;
        template.value = defaultTemplate;
    });
});
</script>
{% endblock %} 