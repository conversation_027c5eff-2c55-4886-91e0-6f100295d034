"""Add customer_id to stock_movements

Revision ID: 4b961301b40a
Revises: 3a2f4163507e
Create Date: 2025-07-14 04:09:37.685247

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4b961301b40a'
down_revision = '3a2f4163507e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('stock_movements', schema=None) as batch_op:
        batch_op.add_column(sa.Column('customer_id', sa.Integer(), nullable=True))
        batch_op.alter_column('user_id',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.create_foreign_key('fk_stock_movements_customer_id', 'customer_users', ['customer_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('stock_movements', schema=None) as batch_op:
        batch_op.drop_constraint('fk_stock_movements_customer_id', type_='foreignkey')
        batch_op.alter_column('user_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.drop_column('customer_id')

    # ### end Alembic commands ###
