#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import random
from datetime import datetime, timedelta, date
from faker import Faker
from decimal import Decimal

# Ajouter le répertoire parent au chemin pour pouvoir importer les modules de l'application
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import create_app, db
from config import Config
from app.modules.auth.models import User, UserRole
from app.modules.inventory.models_product import Product, ProductCategory
from app.modules.inventory.models_ingredient import Ingredient, IngredientCategory
from app.modules.inventory.models_recipe import Recipe, RecipeItem
from app.modules.inventory.models_supplier import Supplier
from app.modules.customers.models import Customer
from app.modules.tables.models_table import Table, TableStatus
from app.modules.cash_register.models_cash_register import CashRegister, CashOperation, PaymentMethod, CashRegisterOperationType
from app.modules.pos.models_sale import Sale, SaleItem, Payment, SaleStatus
from app.modules.expenses.models_expense import ExpenseCategory, Expense, Budget

fake = Faker('fr_FR')

def create_demo_users(owner_id):
    """Crée des utilisateurs de démonstration avec différents rôles"""
    print("Création des utilisateurs de démonstration...")
    
    # L'utilisateur system_admin existe déjà, créé dans run.py
    system_admin = User.query.filter_by(role=UserRole.SYSTEM_ADMIN).first()
    print(f"System admin trouvé: {system_admin.username} (ID: {system_admin.id})")
    
    # Créer un propriétaire (si pas déjà créé)
    owner = User.query.filter_by(role=UserRole.OWNER, owner_id=owner_id).first()
    if not owner:
        owner = User(
            username='owner',
            email='<EMAIL>',
            role=UserRole.OWNER,
            created_by_id=system_admin.id,
            owner_id=owner_id
        )
        owner.set_password('owner123')
        db.session.add(owner)
        db.session.commit()
        print('Utilisateur OWNER créé avec succès!')
        print('Email: <EMAIL>')
        print('Mot de passe: owner123')
    else:
        print(f"Propriétaire existant trouvé: {owner.username} (ID: {owner.id})")
    
    print(f"ID du propriétaire : {owner.id}")
    
    # Fonction pour créer un utilisateur s'il n'existe pas déjà
    def create_user_if_not_exists(username, email, role, password):
        existing_user = User.query.filter_by(email=email).first()
        if not existing_user:
            user = User(
                username=username,
                email=email,
                role=role,
                created_by_id=owner.id,
                owner_id=owner.id  # Utilisez l'ID du propriétaire, pas l'owner_id du system_admin
            )
            user.set_password(password)
            db.session.add(user)
            return True, user
        else:
            # S'assurer que l'utilisateur existant est bien lié au propriétaire correct
            existing_user.owner_id = owner.id
            existing_user.created_by_id = owner.id
            print(f"Mise à jour des liens pour l'utilisateur {existing_user.username}")
            return False, existing_user
    
    # Créer un administrateur
    created, admin = create_user_if_not_exists('admin_demo', '<EMAIL>', UserRole.ADMIN, 'admin_demo123')
    if created:
        print(f'Utilisateur {UserRole.ADMIN} créé avec succès!')
    
    # Créer un manager
    created, manager = create_user_if_not_exists('manager', '<EMAIL>', UserRole.MANAGER, 'manager123')
    if created:
        print(f'Utilisateur {UserRole.MANAGER} créé avec succès!')
    
    # Créer un caissier
    created, cashier = create_user_if_not_exists('caissier', '<EMAIL>', UserRole.CASHIER, 'caissier123')
    if created:
        print(f'Utilisateur {UserRole.CASHIER} créé avec succès!')
    
    # Créer un employé de cuisine
    created, kitchen = create_user_if_not_exists('cuisine', '<EMAIL>', UserRole.KITCHEN, 'cuisine123')
    if created:
        print(f'Utilisateur {UserRole.KITCHEN} créé avec succès!')
    
    # Créer un serveur
    created, waiter = create_user_if_not_exists('serveur', '<EMAIL>', UserRole.WAITER, 'serveur123')
    if created:
        print(f'Utilisateur {UserRole.WAITER} créé avec succès!')
    
    # Créer un comptable
    created, accountant = create_user_if_not_exists('comptable', '<EMAIL>', UserRole.ACCOUNTANT, 'comptable123')
    if created:
        print(f'Utilisateur {UserRole.ACCOUNTANT} créé avec succès!')
    
    db.session.commit()
    print("Utilisateurs de démonstration créés ou vérifiés avec succès!")
    
    return owner.id

def create_product_categories(owner_id):
    """Crée des catégories de produits"""
    print("Création des catégories de produits...")
    print(f"Owner ID utilisé: {owner_id}")
    
    # Vérifier les catégories existantes
    existing_categories = ProductCategory.query.filter_by(owner_id=owner_id).all()
    if existing_categories:
        print(f"Trouvé {len(existing_categories)} catégories de produits existantes pour owner_id={owner_id}")
    
    categories = [
        {"name": "Entrées", "description": "Entrées et apéritifs", "color": "#FF5733"},
        {"name": "Plats principaux", "description": "Plats principaux", "color": "#33FF57"},
        {"name": "Desserts", "description": "Desserts et pâtisseries", "color": "#3357FF"},
        {"name": "Boissons", "description": "Boissons non alcoolisées", "color": "#F3FF33"},
        {"name": "Alcools", "description": "Boissons alcoolisées", "color": "#FF33F3"},
        {"name": "Plats à emporter", "description": "Plats à emporter", "color": "#33FFF3"},
    ]
    
    created_categories = []
    for cat in categories:
        # Vérifier si la catégorie existe déjà
        existing_category = ProductCategory.query.filter_by(name=cat["name"], owner_id=owner_id).first()
        if not existing_category:
            category = ProductCategory(
                name=cat["name"],
                description=cat["description"],
                color=cat["color"],
                owner_id=owner_id
            )
            db.session.add(category)
            db.session.flush()  # Flush pour obtenir l'ID de la catégorie
            created_categories.append(category)
            print(f"Catégorie '{cat['name']}' créée avec ID: {category.id}, owner_id: {category.owner_id}")
        else:
            # Mettre à jour l'owner_id si nécessaire
            if existing_category.owner_id != owner_id:
                existing_category.owner_id = owner_id
                print(f"Mise à jour de l'owner_id pour la catégorie '{existing_category.name}'")
            created_categories.append(existing_category)
            print(f"Catégorie '{cat['name']}' existe déjà avec ID: {existing_category.id}, owner_id: {existing_category.owner_id}")
    
    db.session.commit()
    print(f"{len(created_categories)} catégories de produits disponibles.")
    return created_categories

def create_ingredient_categories(owner_id):
    """Crée des catégories d'ingrédients"""
    print("Création des catégories d'ingrédients...")
    
    categories = [
        {"name": "Viandes", "description": "Viandes et volailles"},
        {"name": "Poissons", "description": "Poissons et fruits de mer"},
        {"name": "Légumes", "description": "Légumes frais"},
        {"name": "Fruits", "description": "Fruits frais"},
        {"name": "Épices", "description": "Épices et condiments"},
        {"name": "Produits laitiers", "description": "Produits laitiers"},
        {"name": "Boissons", "description": "Boissons et sirops"},
        {"name": "Céréales", "description": "Céréales et farines"},
    ]
    
    created_categories = []
    for cat in categories:
        # Vérifier si la catégorie existe déjà
        existing_category = IngredientCategory.query.filter_by(name=cat["name"], owner_id=owner_id).first()
        if not existing_category:
            category = IngredientCategory(
                name=cat["name"],
                description=cat["description"],
                owner_id=owner_id
            )
            db.session.add(category)
            created_categories.append(category)
            print(f"Catégorie d'ingrédients '{cat['name']}' créée.")
        else:
            created_categories.append(existing_category)
            print(f"Catégorie d'ingrédients '{cat['name']}' existe déjà.")
    
    db.session.commit()
    print(f"{len(created_categories)} catégories d'ingrédients disponibles.")
    return created_categories

def create_ingredients(owner_id, categories):
    """Crée des ingrédients"""
    print("Création des ingrédients...")
    
    ingredients_data = [
        # Viandes
        {"name": "Poulet", "category": "Viandes", "unit": "kg", "price": 8.50, "stock": 25, "min_stock": 5},
        {"name": "Boeuf", "category": "Viandes", "unit": "kg", "price": 15.75, "stock": 20, "min_stock": 4},
        {"name": "Porc", "category": "Viandes", "unit": "kg", "price": 10.25, "stock": 18, "min_stock": 3},
        {"name": "Agneau", "category": "Viandes", "unit": "kg", "price": 18.99, "stock": 15, "min_stock": 3},
        
        # Poissons
        {"name": "Saumon", "category": "Poissons", "unit": "kg", "price": 22.50, "stock": 15, "min_stock": 3},
        {"name": "Thon", "category": "Poissons", "unit": "kg", "price": 25.00, "stock": 12, "min_stock": 2},
        {"name": "Crevettes", "category": "Poissons", "unit": "kg", "price": 18.75, "stock": 10, "min_stock": 2},
        
        # Légumes
        {"name": "Pommes de terre", "category": "Légumes", "unit": "kg", "price": 2.25, "stock": 50, "min_stock": 10},
        {"name": "Carottes", "category": "Légumes", "unit": "kg", "price": 1.75, "stock": 30, "min_stock": 8},
        {"name": "Oignons", "category": "Légumes", "unit": "kg", "price": 1.50, "stock": 40, "min_stock": 10},
        {"name": "Tomates", "category": "Légumes", "unit": "kg", "price": 3.25, "stock": 25, "min_stock": 5},
        {"name": "Salade", "category": "Légumes", "unit": "kg", "price": 2.50, "stock": 20, "min_stock": 4},
        
        # Fruits
        {"name": "Pommes", "category": "Fruits", "unit": "kg", "price": 2.99, "stock": 25, "min_stock": 5},
        {"name": "Oranges", "category": "Fruits", "unit": "kg", "price": 3.49, "stock": 20, "min_stock": 5},
        {"name": "Fraises", "category": "Fruits", "unit": "kg", "price": 6.99, "stock": 15, "min_stock": 3},
        
        # Épices
        {"name": "Sel", "category": "Épices", "unit": "kg", "price": 1.25, "stock": 10, "min_stock": 2},
        {"name": "Poivre", "category": "Épices", "unit": "kg", "price": 15.00, "stock": 5, "min_stock": 1},
        {"name": "Paprika", "category": "Épices", "unit": "kg", "price": 18.50, "stock": 3, "min_stock": 1},
        
        # Produits laitiers
        {"name": "Lait", "category": "Produits laitiers", "unit": "l", "price": 1.20, "stock": 40, "min_stock": 10},
        {"name": "Beurre", "category": "Produits laitiers", "unit": "kg", "price": 8.50, "stock": 15, "min_stock": 3},
        {"name": "Crème fraîche", "category": "Produits laitiers", "unit": "l", "price": 4.75, "stock": 12, "min_stock": 3},
        {"name": "Fromage", "category": "Produits laitiers", "unit": "kg", "price": 12.50, "stock": 8, "min_stock": 2},
        
        # Boissons
        {"name": "Eau minérale", "category": "Boissons", "unit": "l", "price": 0.75, "stock": 100, "min_stock": 20},
        {"name": "Jus d'orange", "category": "Boissons", "unit": "l", "price": 2.50, "stock": 30, "min_stock": 8},
        {"name": "Sirop de grenadine", "category": "Boissons", "unit": "l", "price": 4.25, "stock": 10, "min_stock": 2},
        {"name": "Cola", "category": "Boissons", "unit": "l", "price": 1.50, "stock": 50, "min_stock": 10},
        
        # Céréales
        {"name": "Farine", "category": "Céréales", "unit": "kg", "price": 1.20, "stock": 30, "min_stock": 5},
        {"name": "Riz", "category": "Céréales", "unit": "kg", "price": 2.75, "stock": 25, "min_stock": 5},
        {"name": "Pâtes", "category": "Céréales", "unit": "kg", "price": 1.99, "stock": 40, "min_stock": 8},
    ]
    
    # Mapper les noms de catégories aux objets de catégorie
    category_map = {cat.name: cat for cat in categories}
    
    created_ingredients = []
    for ing_data in ingredients_data:
        category = category_map.get(ing_data["category"])
        if not category:
            continue
            
        # Vérifier si l'ingrédient existe déjà
        existing_ingredient = Ingredient.query.filter_by(name=ing_data["name"], owner_id=owner_id).first()
        if existing_ingredient:
            print(f"Ingrédient '{ing_data['name']}' existe déjà.")
            created_ingredients.append(existing_ingredient)
            continue
            
        # Créer un nouvel ingrédient
        ingredient = Ingredient(
            name=ing_data["name"],
            description=f"Description de {ing_data['name']}",
            category_id=category.id,
            unit=ing_data["unit"],
            price_per_unit=ing_data["price"],
            stock_quantity=ing_data["stock"],
            minimum_stock=ing_data["min_stock"],
            owner_id=owner_id
        )
        db.session.add(ingredient)
        created_ingredients.append(ingredient)
        print(f"Ingrédient '{ing_data['name']}' créé.")
    
    db.session.commit()
    print(f"{len(created_ingredients)} ingrédients disponibles.")
    return created_ingredients

def create_products_and_recipes(owner_id, product_categories, ingredients):
    """Crée des produits et des recettes"""
    print("Création des produits et des recettes...")
    print(f"Owner ID utilisé: {owner_id}")
    print(f"Nombre de catégories disponibles: {len(product_categories)}")
    print(f"Nombre d'ingrédients disponibles: {len(ingredients)}")
    
    # Vérifier les produits existants
    existing_products = Product.query.filter_by(owner_id=owner_id).all()
    if existing_products:
        print(f"Trouvé {len(existing_products)} produits existants pour owner_id={owner_id}")
        for prod in existing_products:
            print(f"  - Produit existant: {prod.name}, ID: {prod.id}, owner_id: {prod.owner_id}, has_recipe: {prod.has_recipe}")
    
    # Vérifier si les recettes existent
    existing_recipes = Recipe.query.join(Product).filter(Product.owner_id == owner_id).all()
    if existing_recipes:
        print(f"Trouvé {len(existing_recipes)} recettes existantes pour produits avec owner_id={owner_id}")
        for recipe in existing_recipes:
            # Compter les ingrédients dans chaque recette
            ingredients_count = RecipeItem.query.filter_by(recipe_id=recipe.id).count()
            print(f"  - Recette pour produit ID {recipe.product_id}, avec {ingredients_count} ingrédients")
    
    # Si toutes les catégories n'ont pas d'ID valide, committons pour obtenir les IDs
    if any(not cat.id for cat in product_categories):
        print("Commit pour obtenir les IDs des catégories")
        db.session.commit()
    
    # Mapper les noms de catégories aux objets de catégorie
    category_map = {cat.name: cat for cat in product_categories}
    print(f"Catégories disponibles: {', '.join(category_map.keys())}")
    
    # Mapper les noms d'ingrédients aux objets d'ingrédient
    if ingredients:
        ingredient_map = {ing.name: ing for ing in ingredients}
        print(f"Ingrédients disponibles: {', '.join(ingredient_map.keys())}")
    else:
        print("ATTENTION: Aucun ingrédient disponible!")
        ingredient_map = {}
    
    # Liste complète des produits à créer
    all_products = [
        # Entrées
        {
            "name": "Salade César",
            "description": "Salade avec laitue romaine, croûtons, parmesan et sauce César",
            "price": 8.50,
            "category": "Entrées",
            "recipe": [
                {"ingredient": "Salade", "quantity": 0.25},
                {"ingredient": "Fromage", "quantity": 0.05},
                {"ingredient": "Poulet", "quantity": 0.1}
            ]
        },
        {
            "name": "Carpaccio de boeuf",
            "description": "Fines tranches de boeuf cru, huile d'olive, parmesan et roquette",
            "price": 12.75,
            "category": "Entrées",
            "recipe": [
                {"ingredient": "Boeuf", "quantity": 0.15}
            ]
        },
        {
            "name": "Soupe à l'oignon",
            "description": "Soupe à l'oignon traditionnelle avec croûtons et fromage gratinés",
            "price": 7.50,
            "category": "Entrées",
            "recipe": [
                {"ingredient": "Oignons", "quantity": 0.3},
                {"ingredient": "Fromage", "quantity": 0.05}
            ]
        },
        # Plats principaux
        {
            "name": "Steak frites",
            "description": "Steak de boeuf grillé accompagné de frites maison",
            "price": 18.50,
            "category": "Plats principaux",
            "recipe": [
                {"ingredient": "Boeuf", "quantity": 0.3},
                {"ingredient": "Pommes de terre", "quantity": 0.2}
            ]
        },
        {
            "name": "Poulet rôti",
            "description": "Poulet rôti aux herbes avec légumes de saison",
            "price": 16.75,
            "category": "Plats principaux",
            "recipe": [
                {"ingredient": "Poulet", "quantity": 0.4},
                {"ingredient": "Carottes", "quantity": 0.1},
                {"ingredient": "Pommes de terre", "quantity": 0.2}
            ]
        },
        {
            "name": "Saumon à l'oseille",
            "description": "Filet de saumon poché avec sauce à l'oseille",
            "price": 22.50,
            "category": "Plats principaux",
            "recipe": [
                {"ingredient": "Saumon", "quantity": 0.25},
                {"ingredient": "Crème fraîche", "quantity": 0.1}
            ]
        },
        # Desserts
        {
            "name": "Mousse au chocolat",
            "description": "Mousse au chocolat légère et aérienne",
            "price": 7.50,
            "category": "Desserts",
            "recipe": [
                {"ingredient": "Lait", "quantity": 0.1}
            ]
        },
        {
            "name": "Tarte aux pommes",
            "description": "Tarte aux pommes maison avec crème anglaise",
            "price": 8.25,
            "category": "Desserts",
            "recipe": [
                {"ingredient": "Pommes", "quantity": 0.3},
                {"ingredient": "Farine", "quantity": 0.1},
                {"ingredient": "Beurre", "quantity": 0.05}
            ]
        },
        # Boissons
        {
            "name": "Eau minérale 50cl",
            "description": "Bouteille d'eau minérale 50cl",
            "price": 3.50,
            "category": "Boissons",
            "has_recipe": False
        },
        {
            "name": "Jus d'orange pressé",
            "description": "Jus d'orange frais pressé",
            "price": 5.50,
            "category": "Boissons",
            "recipe": [
                {"ingredient": "Oranges", "quantity": 0.4}
            ]
        },
        # Alcools
        {
            "name": "Verre de vin rouge",
            "description": "Verre de vin rouge de la maison",
            "price": 5.75,
            "category": "Alcools",
            "has_recipe": False
        },
        {
            "name": "Cocktail maison",
            "description": "Cocktail spécial du chef",
            "price": 8.95,
            "category": "Alcools",
            "has_recipe": True,
            "recipe": [
                {"ingredient": "Jus d'orange", "quantity": 0.1},
                {"ingredient": "Sirop de grenadine", "quantity": 0.02}
            ]
        }
    ]
    
    created_products = []
    
    # Forcer la création de nouveaux produits avec recettes
    force_create = True
    
    # S'il y a assez de produits et de recettes, désactiver la création forcée
    if len(existing_products) > 10 and len(existing_recipes) > 5:
        print("Il existe déjà suffisamment de produits et de recettes. Vérification uniquement...")
        force_create = False
    
    # Liste pour suivre les erreurs
    errors = []
    
    for product_data in all_products:
        category_name = product_data.get("category")
        category = category_map.get(category_name)
        
        if not category:
            print(f"ERREUR: Catégorie '{category_name}' non trouvée pour produit '{product_data['name']}'")
            errors.append(f"Catégorie manquante: {category_name}")
            continue
        
        has_recipe = product_data.get("has_recipe", True)
        
        try:
            # Vérifier si le produit existe déjà
            existing_product = Product.query.filter_by(name=product_data["name"], owner_id=owner_id).first()
            
            if existing_product and not force_create:
                print(f"Produit '{product_data['name']}' existe déjà avec ID: {existing_product.id}")
                
                # Vérifier si le produit a une recette si nécessaire
                if has_recipe:
                    existing_recipe = Recipe.query.filter_by(product_id=existing_product.id).first()
                    if not existing_recipe:
                        print(f"Le produit {existing_product.name} n'a pas de recette. Création d'une recette...")
                        # Créer la recette manquante
                        create_recipe_for_product(existing_product, product_data.get("recipe", []), ingredient_map)
                
                created_products.append(existing_product)
                continue
            
            # Si le produit existe et qu'on force la création, supprimer le produit et ses recettes
            if existing_product and force_create:
                # Supprimer les recettes existantes
                existing_recipe = Recipe.query.filter_by(product_id=existing_product.id).first()
                if existing_recipe:
                    # Supprimer les items de la recette
                    RecipeItem.query.filter_by(recipe_id=existing_recipe.id).delete()
                    db.session.delete(existing_recipe)
                
                # Supprimer le produit
                db.session.delete(existing_product)
                db.session.flush()
                print(f"Suppression du produit existant '{product_data['name']}' pour recréation")
            
            # Créer un nouveau produit
            product = Product(
                name=product_data["name"],
                description=product_data["description"],
                price=product_data["price"],
                cost_price=product_data["price"] / 3 if not has_recipe else None,  # Coût temporaire
                category_id=category.id,
                owner_id=owner_id,
                unit="unité",
                has_recipe=has_recipe,
                stock_quantity=50 if not has_recipe else 0  # Stock uniquement pour les produits sans recette
            )
            db.session.add(product)
            db.session.flush()  # Pour obtenir l'ID du produit
            print(f"Produit '{product_data['name']}' créé avec ID: {product.id}, owner_id: {product.owner_id}")
            
            # Créer la recette si nécessaire
            if has_recipe:
                create_recipe_for_product(product, product_data.get("recipe", []), ingredient_map)
            else:
                # Pour les produits sans recette
                product.sale_price = product.price
                product.cost_price = product.price * 0.4
            
            created_products.append(product)
            
        except Exception as e:
            print(f"ERREUR lors de la création du produit '{product_data['name']}': {str(e)}")
            errors.append(f"Erreur pour {product_data['name']}: {str(e)}")
            import traceback
            traceback.print_exc()
    
    # Commit après toutes les créations
    try:
        db.session.commit()
        print("Commit réussi pour tous les produits et recettes")
    except Exception as e:
        db.session.rollback()
        print(f"ERREUR lors du commit final: {str(e)}")
        errors.append(f"Erreur de commit: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # Vérification finale
    final_products = Product.query.filter_by(owner_id=owner_id).all()
    print(f"Nombre total de produits dans la base: {len(final_products)}")
    
    final_recipes = Recipe.query.join(Product).filter(Product.owner_id == owner_id).all()
    print(f"Nombre total de recettes dans la base: {len(final_recipes)}")
    
    # Afficher les erreurs s'il y en a
    if errors:
        print("\nERREURS lors de la création des produits et recettes:")
        for error in errors:
            print(f"- {error}")
    
    return created_products

def create_recipe_for_product(product, recipe_items_data, ingredient_map):
    """Crée une recette pour un produit existant"""
    
    # Créer la recette parent
    recipe = Recipe(
        product_id=product.id,
        description=f"Recette pour {product.name}",
        preparation_time=random.randint(5, 30)
    )
    db.session.add(recipe)
    db.session.flush()
    print(f"Recette créée pour '{product.name}' avec ID: {recipe.id}")
    
    total_cost = 0
    items_count = 0
    
    # Si aucun ingrédient n'est spécifié ou que la map est vide, utiliser des ingrédients aléatoires
    if not recipe_items_data or not ingredient_map:
        print("Aucun ingrédient spécifié ou map d'ingrédients vide, création d'une recette aléatoire")
        # Récupérer tous les ingrédients disponibles
        all_ingredients = list(ingredient_map.values()) if ingredient_map else Ingredient.query.all()
        
        if all_ingredients:
            # Sélectionner 1 à 3 ingrédients aléatoires
            num_ingredients = min(3, len(all_ingredients))
            selected_ingredients = random.sample(all_ingredients, num_ingredients)
            
            for ingredient in selected_ingredients:
                # Quantité aléatoire
                quantity = random.uniform(0.05, 0.5)
                
                recipe_item = RecipeItem(
                    recipe_id=recipe.id,
                    ingredient_id=ingredient.id,
                    quantity=quantity
                )
                db.session.add(recipe_item)
                
                ingredient_cost = ingredient.price_per_unit * quantity
                total_cost += ingredient_cost
                items_count += 1
                print(f"Ingrédient aléatoire '{ingredient.name}' ajouté à la recette, coût: {ingredient_cost}€")
        else:
            print("ATTENTION: Aucun ingrédient disponible pour créer une recette aléatoire")
    else:
        # Utiliser les ingrédients spécifiés
        for item_data in recipe_items_data:
            ingredient_name = item_data.get("ingredient")
            if not ingredient_name:
                continue
            
            ingredient = ingredient_map.get(ingredient_name)
            if not ingredient:
                print(f"ATTENTION: Ingrédient '{ingredient_name}' non trouvé")
                continue
            
            quantity = item_data.get("quantity", 0.1)
            
            recipe_item = RecipeItem(
                recipe_id=recipe.id,
                ingredient_id=ingredient.id,
                quantity=quantity
            )
            db.session.add(recipe_item)
            
            ingredient_cost = ingredient.price_per_unit * quantity
            total_cost += ingredient_cost
            items_count += 1
            print(f"Ingrédient '{ingredient.name}' ajouté à la recette, coût: {ingredient_cost}€")
    
    # Si aucun ingrédient n'a été ajouté, définir un coût arbitraire
    if items_count == 0:
        total_cost = product.price * 0.3
        print(f"Aucun ingrédient ajouté à la recette, coût arbitraire défini à {total_cost}€")
    
    # Mettre à jour le coût et le prix de vente du produit
    product.cost_price = total_cost
    product.sale_price = total_cost * 3
    print(f"Coût total de la recette: {total_cost}€, Prix de vente du produit: {product.sale_price}€")
    
    # Flush pour enregistrer les changements
    db.session.flush()
    
    return recipe

def create_suppliers(owner_id):
    """Crée des fournisseurs"""
    print("Création des fournisseurs...")
    
    suppliers_data = [
        {"name": "Fermier Local", "contact": "Jean Dupont", "email": "<EMAIL>", "phone": "0123456789"},
        {"name": "FruitExpress", "contact": "Marie Martin", "email": "<EMAIL>", "phone": "0234567890"},
        {"name": "Viandes Premium", "contact": "Pierre Durand", "email": "<EMAIL>", "phone": "0345678901"},
        {"name": "Boissons & Co", "contact": "Sophie Leroy", "email": "<EMAIL>", "phone": "0456789012"},
    ]
    
    created_suppliers = []
    for sup_data in suppliers_data:
        # Vérifier si le fournisseur existe déjà
        existing_supplier = Supplier.query.filter_by(email=sup_data["email"], owner_id=owner_id).first()
        if not existing_supplier:
            supplier = Supplier(
                name=sup_data["name"],
                contact_name=sup_data["contact"],
                email=sup_data["email"],
                phone=sup_data["phone"],
                owner_id=owner_id
            )
            db.session.add(supplier)
            created_suppliers.append(supplier)
            print(f"Fournisseur '{sup_data['name']}' créé.")
        else:
            created_suppliers.append(existing_supplier)
            print(f"Fournisseur '{sup_data['name']}' existe déjà.")
    
    db.session.commit()
    print(f"{len(created_suppliers)} fournisseurs disponibles.")
    return created_suppliers

def create_customers(owner_id, num_customers=20):
    """Crée des clients"""
    print("Création des clients...")
    
    created_customers = []
    for _ in range(num_customers):
        # Générer un e-mail unique pour le client
        email = fake.email()
        
        # Vérifier si le client existe déjà
        existing_customer = Customer.query.filter_by(email=email, owner_id=owner_id).first()
        if existing_customer:
            created_customers.append(existing_customer)
            continue
            
        # Créer un nouveau client
        customer = Customer(
            first_name=fake.first_name(),
            last_name=fake.last_name(),
            email=email,
            phone=fake.phone_number(),
            address=fake.address(),
            loyalty_points=random.randint(0, 200),
            owner_id=owner_id,
            last_visit=fake.date_time_between(start_date='-3m', end_date='now')
        )
        db.session.add(customer)
        created_customers.append(customer)
    
    db.session.commit()
    print(f"{len(created_customers)} clients disponibles.")
    return created_customers

def create_table_sections(owner_id):
    """Crée des sections de tables (en tant que simples valeurs de retour)"""
    print("Création des sections de tables...")
    
    sections = [
        {"name": "Terrasse", "description": "Tables en terrasse"},
        {"name": "Salle principale", "description": "Tables de la salle principale"},
        {"name": "Balcon", "description": "Tables en balcon"},
        {"name": "VIP", "description": "Espace VIP"},
    ]
    
    print(f"{len(sections)} sections de tables définies.")
    return sections

def create_tables(owner_id, sections):
    """Crée des tables"""
    print("Création des tables...")
    
    # Mapper les noms de sections
    section_names = [section["name"] for section in sections]
    
    tables_data = [
        # Terrasse
        {"number": "T1", "section": "Terrasse", "capacity": 2},
        {"number": "T2", "section": "Terrasse", "capacity": 2},
        {"number": "T3", "section": "Terrasse", "capacity": 4},
        {"number": "T4", "section": "Terrasse", "capacity": 4},
        {"number": "T5", "section": "Terrasse", "capacity": 6},
        
        # Salle principale
        {"number": "S1", "section": "Salle principale", "capacity": 2},
        {"number": "S2", "section": "Salle principale", "capacity": 2},
        {"number": "S3", "section": "Salle principale", "capacity": 4},
        {"number": "S4", "section": "Salle principale", "capacity": 4},
        {"number": "S5", "section": "Salle principale", "capacity": 6},
        {"number": "S6", "section": "Salle principale", "capacity": 8},
        
        # Balcon
        {"number": "B1", "section": "Balcon", "capacity": 2},
        {"number": "B2", "section": "Balcon", "capacity": 4},
        {"number": "B3", "section": "Balcon", "capacity": 4},
        
        # VIP
        {"number": "V1", "section": "VIP", "capacity": 4},
        {"number": "V2", "section": "VIP", "capacity": 8},
    ]
    
    created_tables = []
    for table_data in tables_data:
        section_name = table_data["section"]
        if section_name not in section_names:
            continue
        
        # Vérifier si la table existe déjà
        existing_table = Table.query.filter_by(number=table_data["number"], owner_id=owner_id).first()
        if existing_table:
            print(f"Table '{table_data['number']}' existe déjà.")
            created_tables.append(existing_table)
            continue
            
        # Créer une nouvelle table
        table = Table(
            number=table_data["number"],
            capacity=table_data["capacity"],
            status=TableStatus.AVAILABLE,
            location=section_name,
            owner_id=owner_id
        )
        db.session.add(table)
        created_tables.append(table)
        print(f"Table '{table_data['number']}' créée.")
    
    db.session.commit()
    print(f"{len(created_tables)} tables disponibles.")
    return created_tables

def create_cash_register(owner_id, cashier_id):
    """Crée une caisse"""
    print("Création de la caisse...")
    
    try:
        # Vérifier si une caisse existe déjà
        existing_register = CashRegister.query.filter_by(owner_id=owner_id).first()
        if existing_register:
            print("Une caisse existe déjà.")
            
            # S'assurer que les champs essentiels sont définis
            if existing_register.float_amount is None:
                existing_register.float_amount = 500.0
                print("Mise à jour du fond de caisse à 500.0")
                
            if existing_register.current_balance is None:
                existing_register.current_balance = 500.0
                print("Mise à jour du solde courant à 500.0")
                
            db.session.commit()
            return existing_register
        
        # Créer une nouvelle caisse avec tous les champs requis
        cash_register = CashRegister(
            owner_id=owner_id,
            current_balance=500.0,
            is_open=True,
            float_amount=500.0,  # S'assurer que float_amount est correctement défini
            last_opened_at=datetime.utcnow()
        )
        db.session.add(cash_register)
        db.session.flush()  # Flush pour obtenir l'ID de la caisse
        
        # Ajouter un mouvement d'ouverture de caisse
        cash_movement = CashOperation(
            register_id=cash_register.id,
            owner_id=owner_id,
            amount=500.0,
            initial_amount=500.0,  # S'assurer que initial_amount est également défini
            type=CashRegisterOperationType.OPENING,
            user_id=cashier_id,
            reason="OPENING",
            note="Ouverture initiale de la caisse"
        )
        db.session.add(cash_movement)
        
        db.session.commit()
        print("Caisse créée avec succès.")
        return cash_register
        
    except Exception as e:
        db.session.rollback()
        print(f"Erreur lors de la création de la caisse: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def create_sales(owner_id, products, customers, tables, cashier_id):
    """Crée des ventes"""
    print("Création des ventes...")
    
    payment_methods = [
        PaymentMethod.CASH,
        PaymentMethod.CARD,
        PaymentMethod.CHECK,
        PaymentMethod.OTHER
    ]
    
    sale_statuses = [
        SaleStatus.PAID,
        SaleStatus.COMPLETED,
        SaleStatus.PENDING
    ]
    
    # Créer 30 ventes sur les 3 derniers mois
    created_sales = []
    for i in range(30):
        sale_date = datetime.utcnow() - timedelta(days=random.randint(1, 90))
        
        # Sélectionner aléatoirement un client ou None
        customer = random.choice([None] + customers) if i % 3 != 0 else None
        
        # Sélectionner aléatoirement une table ou None
        table = random.choice([None] + tables) if i % 2 == 0 else None
        
        # Créer la vente
        sale = Sale(
            owner_id=owner_id,
            user_id=cashier_id,
            customer_id=customer.id if customer else None,
            table_number=table.number if table else None,
            status=random.choice(sale_statuses),
            tax_rate=20.0,  # 20% de TVA
            created_at=sale_date
        )
        db.session.add(sale)
        db.session.flush()
        
        # Ajouter des produits à la vente (entre 1 et 5 produits)
        num_products = random.randint(1, 5)
        selected_products = random.sample(products, num_products)
        
        for product in selected_products:
            # Ajouter l'article à la vente
            sale_item = SaleItem(
                sale_id=sale.id,
                product_id=product.id,
                quantity=random.randint(1, 3),
                price=product.price,
                total=product.price * random.randint(1, 3)
            )
            db.session.add(sale_item)
        
        # Calculer les totaux
        sale.calculate_totals()
        
        # Si la vente est payée, ajouter un paiement
        if sale.status in [SaleStatus.PAID, SaleStatus.COMPLETED]:
            payment = Payment(
                sale_id=sale.id,
                amount=sale.total,
                method=random.choice(payment_methods),
                created_at=sale_date
            )
            db.session.add(payment)
            sale.paid_at = sale_date
        
        created_sales.append(sale)
    
    db.session.commit()
    print(f"{len(created_sales)} ventes créées.")
    return created_sales

def create_expense_categories(owner_id):
    """Crée des catégories de dépenses"""
    print("Création des catégories de dépenses...")
    print(f"Owner ID utilisé: {owner_id}")
    
    # Vérifier si des catégories de dépenses existent déjà
    existing_categories = ExpenseCategory.query.filter_by(owner_id=owner_id).all()
    if existing_categories:
        print(f"Trouvé {len(existing_categories)} catégories de dépenses existantes pour owner_id={owner_id}")
        # Si l'utilisateur a déjà des catégories, on peut les retourner directement
        return existing_categories
    
    categories = [
        {"name": "Loyer", "description": "Loyer et charges du local"},
        {"name": "Salaires", "description": "Salaires des employés"},
        {"name": "Charges sociales", "description": "Charges sociales et taxes liées aux salaires"},
        {"name": "Approvisionnement", "description": "Achats de nourriture et boissons"},
        {"name": "Énergie", "description": "Électricité, eau, gaz"},
        {"name": "Marketing", "description": "Publicité et promotion"},
        {"name": "Maintenance", "description": "Entretien et réparations"},
        {"name": "Fournitures", "description": "Fournitures diverses (bureau, nettoyage, etc.)"},
        {"name": "Équipement", "description": "Achat et location d'équipement"},
        {"name": "Assurances", "description": "Assurances professionnelles"},
        {"name": "Services bancaires", "description": "Frais bancaires et de paiement"},
        {"name": "Impôts et taxes", "description": "Taxes professionnelles et impôts"},
        {"name": "Télécommunication", "description": "Téléphone, internet"},
        {"name": "Formation", "description": "Formation et développement professionnel"},
        {"name": "Divers", "description": "Autres dépenses non catégorisées"}
    ]
    
    created_categories = []
    for cat_data in categories:
        # Vérifier si la catégorie existe déjà
        existing_category = ExpenseCategory.query.filter_by(name=cat_data["name"], owner_id=owner_id).first()
        if not existing_category:
            category = ExpenseCategory(
                name=cat_data["name"],
                description=cat_data["description"],
                owner_id=owner_id
            )
            db.session.add(category)
            db.session.flush()  # Flush pour obtenir l'ID
            created_categories.append(category)
            print(f"Catégorie de dépense '{cat_data['name']}' créée avec ID: {category.id}")
        else:
            created_categories.append(existing_category)
            print(f"Catégorie de dépense '{cat_data['name']}' existe déjà avec ID: {existing_category.id}")
    
    db.session.commit()
    print(f"{len(created_categories)} catégories de dépenses disponibles.")
    return created_categories

def create_expenses(owner_id, categories, users):
    """Crée des dépenses fictives pour les derniers mois"""
    print("Création des dépenses...")
    print(f"Owner ID utilisé: {owner_id}")
    print(f"Nombre de catégories disponibles: {len(categories)}")
    print(f"Nombre d'utilisateurs disponibles: {len(users)}")
    
    if not categories:
        print("ERREUR: Aucune catégorie de dépenses disponible!")
        return []
    
    if not users:
        print("ERREUR: Aucun utilisateur disponible!")
        return []
    
    # Vérifier si des dépenses existent déjà pour cet owner
    existing_expenses = Expense.query.filter_by(owner_id=owner_id).all()
    if existing_expenses:
        print(f"Trouvé {len(existing_expenses)} dépenses existantes pour owner_id={owner_id}")
        # Si des dépenses existent déjà et sont nombreuses, on peut les retourner directement
        if len(existing_expenses) > 10:
            print("Suffisamment de dépenses existantes, pas besoin d'en créer de nouvelles.")
            return existing_expenses
    
    # Création forcée si aucune dépense n'existe
    force_create = len(existing_expenses) == 0
    
    # Date du jour
    today = date.today()
    payment_methods = ["Cash", "Carte", "Virement", "Chèque"]
    
    # Catégories par nom
    category_map = {cat.name: cat for cat in categories}
    
    # Créer des dépenses simples
    created_expenses = []
    
    # Liste des dépenses à créer
    expenses_to_create = [
        {"category": "Loyer", "amount": 1200, "day": 5, "description": "Loyer mensuel"},
        {"category": "Énergie", "amount": 350, "day": 15, "description": "Facture d'électricité et gaz"},
        {"category": "Approvisionnement", "amount": 800, "day": 10, "description": "Achat de provisions"},
        {"category": "Salaires", "amount": 3500, "day": 28, "description": "Salaires mensuels"},
        {"category": "Charges sociales", "amount": 1400, "day": 28, "description": "Charges sociales"}
    ]
    
    # Créer des dépenses pour le mois actuel et le mois précédent
    for month_offset in range(2):
        expense_month = today.month - month_offset
        expense_year = today.year
        
        # Ajuster l'année si nécessaire
        if expense_month <= 0:
            expense_month += 12
            expense_year -= 1
        
        for expense_data in expenses_to_create:
            category_name = expense_data.get("category")
            category = category_map.get(category_name)
            
            if not category:
                print(f"ERREUR: Catégorie '{category_name}' non trouvée")
                continue
            
            expense_day = min(expense_data.get("day", 1), 28)  # Jour du mois
            expense_date = date(expense_year, expense_month, expense_day)
            
            # Vérifier si la dépense existe déjà
            existing_expense = Expense.query.filter_by(
                owner_id=owner_id,
                category_id=category.id,
                date=expense_date,
                description=expense_data.get("description")
            ).first()
            
            if existing_expense and not force_create:
                print(f"Dépense '{expense_data.get('description')}' existe déjà pour {expense_date}")
                created_expenses.append(existing_expense)
                continue
            
            # Créer la dépense
            try:
                # Choisir un utilisateur aléatoire
                user = random.choice(users)
                
                expense = Expense(
                    category_id=category.id,
                    owner_id=owner_id,
                    user_id=user.id,
                    date=expense_date,
                    amount=expense_data.get("amount"),
                    description=expense_data.get("description"),
                    payment_method=random.choice(payment_methods),
                    is_recurring=True,
                    recurring_interval="monthly",
                    recurring_day=expense_day
                )
                db.session.add(expense)
                db.session.flush()  # Pour obtenir l'ID
                created_expenses.append(expense)
                print(f"Dépense '{expense_data.get('description')}' créée pour {expense_date} avec ID: {expense.id}")
            
            except Exception as e:
                print(f"ERREUR lors de la création de la dépense: {str(e)}")
                import traceback
                traceback.print_exc()
    
    db.session.commit()
    print(f"{len(created_expenses)} dépenses créées ou vérifiées.")
    
    # Vérification finale
    final_expenses = Expense.query.filter_by(owner_id=owner_id).all()
    print(f"Nombre total de dépenses dans la base: {len(final_expenses)}")
    
    return created_expenses

def main():
    app = create_app(Config)
    
    with app.app_context():
        try:
            print("Début de la génération des données de démonstration...")
            
            # Obtenir l'ID du system_admin existant
            system_admin = User.query.filter_by(role=UserRole.SYSTEM_ADMIN).first()
            if not system_admin:
                print("Erreur: L'utilisateur system_admin n'existe pas, veuillez exécuter run.py d'abord.")
                return
            
            owner_id = system_admin.id
            print(f"System admin ID: {owner_id}")
            
            try:
                # Créer les utilisateurs
                owner_id = create_demo_users(owner_id)
                print(f"Owner ID après création: {owner_id}")
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                print(f"Erreur lors de la création des utilisateurs: {str(e)}")
                import traceback
                traceback.print_exc()
            
            # Récupérer tous les utilisateurs pour les opérations
            try:
                all_users = User.query.filter_by(owner_id=owner_id).all()
                print(f"Nombre d'utilisateurs trouvés: {len(all_users)}")
                
                for user in all_users:
                    print(f"User: {user.username}, Role: {user.role}, Owner ID: {user.owner_id}")
                
                # Récupérer le caissier pour les opérations de caisse
                cashier = User.query.filter_by(username='caissier', owner_id=owner_id).first()
                if not cashier:
                    print("Caissier non trouvé, utilisation du premier utilisateur disponible")
                    cashier = all_users[0] if all_users else system_admin
            except Exception as e:
                print(f"Erreur lors de la récupération des utilisateurs: {str(e)}")
                import traceback
                traceback.print_exc()
                all_users = []
                cashier = None
            
            # Vérification de l'ID du propriétaire
            print(f"Vérification de l'ID du propriétaire actuel: {owner_id}")
            owner = User.query.filter_by(id=owner_id).first()
            if not owner:
                print(f"ERREUR: Aucun utilisateur trouvé avec ID={owner_id}")
                # Utiliser l'ID du system_admin comme fallback
                owner_id = system_admin.id
                print(f"Utilisation de l'ID du system_admin comme fallback: {owner_id}")
            else:
                print(f"Propriétaire trouvé: {owner.username}, Role: {owner.role}")
            
            # S'assurer que toutes les entités ont un owner_id valide
            def verify_owner_id(entity, message):
                if entity and entity.owner_id != owner_id:
                    print(f"{message} - Mise à jour de owner_id de {entity.owner_id} à {owner_id}")
                    entity.owner_id = owner_id
                    return True
                return False
            
            try:
                # Vérifier les entités existantes et corriger owner_id si nécessaire
                modified = False
                
                for product in Product.query.all():
                    if verify_owner_id(product, f"Produit {product.name} a un owner_id incorrect"):
                        modified = True
                
                for ingredient in Ingredient.query.all():
                    if verify_owner_id(ingredient, f"Ingrédient {ingredient.name} a un owner_id incorrect"):
                        modified = True
                
                for table in Table.query.all():
                    if verify_owner_id(table, f"Table {table.number} a un owner_id incorrect"):
                        modified = True
                
                for customer in Customer.query.all():
                    if verify_owner_id(customer, f"Client {customer.email} a un owner_id incorrect"):
                        modified = True
                
                for category in ProductCategory.query.all():
                    if verify_owner_id(category, f"Catégorie de produit {category.name} a un owner_id incorrect"):
                        modified = True
                
                for category in IngredientCategory.query.all():
                    if verify_owner_id(category, f"Catégorie d'ingrédient {category.name} a un owner_id incorrect"):
                        modified = True
                
                for supplier in Supplier.query.all():
                    if verify_owner_id(supplier, f"Fournisseur {supplier.name} a un owner_id incorrect"):
                        modified = True
                
                for expense_category in ExpenseCategory.query.all():
                    if verify_owner_id(expense_category, f"Catégorie de dépense {expense_category.name} a un owner_id incorrect"):
                        modified = True
                
                for expense in Expense.query.all():
                    if verify_owner_id(expense, f"Dépense ID {expense.id} a un owner_id incorrect"):
                        modified = True
                
                for register in CashRegister.query.all():
                    if verify_owner_id(register, f"Caisse ID {register.id} a un owner_id incorrect"):
                        modified = True
                
                if modified:
                    print("Des entités ont été mises à jour avec le bon owner_id")
                    db.session.commit()
            except Exception as e:
                db.session.rollback()
                print(f"Erreur lors de la vérification des owner_id: {str(e)}")
                import traceback
                traceback.print_exc()
            
            # Créer les catégories de produits
            try:
                product_categories = create_product_categories(owner_id)
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                print(f"Erreur lors de la création des catégories de produits: {str(e)}")
                import traceback
                traceback.print_exc()
                product_categories = []
            
            # Créer les catégories d'ingrédients
            try:
                ingredient_categories = create_ingredient_categories(owner_id)
                db.session.commit()
                
                ingredients = create_ingredients(owner_id, ingredient_categories)
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                print(f"Erreur lors de la création des ingrédients: {str(e)}")
                import traceback
                traceback.print_exc()
                ingredients = []
            
            # Créer les produits et recettes même si les catégories ou ingrédients sont vides
            try:
                products = create_products_and_recipes(owner_id, product_categories, ingredients)
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                print(f"Erreur lors de la création des produits et recettes: {str(e)}")
                import traceback
                traceback.print_exc()
                products = []
            
            # Créer les catégories de dépenses et les dépenses
            try:
                expense_categories = create_expense_categories(owner_id)
                db.session.commit()
                
                expenses = create_expenses(owner_id, expense_categories, all_users)
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                print(f"Erreur lors de la création des dépenses: {str(e)}")
                import traceback
                traceback.print_exc()
                expenses = []
            
            # Créer les fournisseurs
            try:
                suppliers = create_suppliers(owner_id)
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                print(f"Erreur lors de la création des fournisseurs: {str(e)}")
                import traceback
                traceback.print_exc()
            
            # Créer les clients
            try:
                customers = create_customers(owner_id)
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                print(f"Erreur lors de la création des clients: {str(e)}")
                import traceback
                traceback.print_exc()
            
            # Créer les sections de tables
            try:
                table_sections = create_table_sections(owner_id)
            except Exception as e:
                print(f"Erreur lors de la création des sections de tables: {str(e)}")
                import traceback
                traceback.print_exc()
            
            # Créer les tables
            try:
                tables = create_tables(owner_id, table_sections)
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                print(f"Erreur lors de la création des tables: {str(e)}")
                import traceback
                traceback.print_exc()
            
            # Créer la caisse
            try:
                cash_register = create_cash_register(owner_id, cashier.id)
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                print(f"Erreur lors de la création de la caisse: {str(e)}")
                import traceback
                traceback.print_exc()
            
            # Créer des ventes
            try:
                sales = create_sales(owner_id, products, customers, tables, cashier.id)
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                print(f"Erreur lors de la création des ventes: {str(e)}")
                import traceback
                traceback.print_exc()
            
            # Vérification finale des données
            print("\nVérification finale des données:")
            product_count = Product.query.filter_by(owner_id=owner_id).count()
            recipe_count = Recipe.query.join(Product).filter(Product.owner_id == owner_id).count()
            expense_count = Expense.query.filter_by(owner_id=owner_id).count()
            
            print(f"Nombre de produits pour owner_id={owner_id}: {product_count}")
            print(f"Nombre de recettes pour owner_id={owner_id}: {recipe_count}")
            print(f"Nombre de dépenses pour owner_id={owner_id}: {expense_count}")
            
            print("\nGénération des données de démonstration terminée!")
            
        except Exception as e:
            db.session.rollback()
            print(f"Erreur générale lors de la génération des données: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main() 