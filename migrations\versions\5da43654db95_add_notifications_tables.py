"""Add notifications tables

Revision ID: 5da43654db95
Revises: de811428c20d
Create Date: 2025-06-20 03:40:54.244204

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5da43654db95'
down_revision = 'de811428c20d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('notification_preferences',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('user_id', sa.Integer(), nullable=False),
    sa.Column('user_type', sa.String(length=50), nullable=False),
    sa.Column('email_enabled', sa.<PERSON>(), nullable=True),
    sa.<PERSON>umn('browser_enabled', sa.Boolean(), nullable=True),
    sa.Column('sound_enabled', sa.<PERSON>(), nullable=True),
    sa.Column('order_updates', sa.<PERSON>(), nullable=True),
    sa.Column('payment_updates', sa.<PERSON>(), nullable=True),
    sa.<PERSON>umn('promotional', sa.<PERSON>(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('notifications',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('type', sa.Enum('ORDER_RECEIVED', 'ORDER_CONFIRMED', 'ORDER_PREPARING', 'ORDER_READY', 'ORDER_OUT_FOR_DELIVERY', 'ORDER_DELIVERED', 'ORDER_CANCELLED', 'PAYMENT_RECEIVED', 'PAYMENT_FAILED', name='notificationtype'), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('recipient_type', sa.String(length=50), nullable=False),
    sa.Column('recipient_id', sa.Integer(), nullable=False),
    sa.Column('order_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'SENT', 'READ', 'FAILED', name='notificationstatus'), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('sent_at', sa.DateTime(), nullable=True),
    sa.Column('read_at', sa.DateTime(), nullable=True),
    sa.Column('data', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['order_id'], ['sales.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('notifications')
    op.drop_table('notification_preferences')
    # ### end Alembic commands ###
