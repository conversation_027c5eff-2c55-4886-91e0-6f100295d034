from app import db
from datetime import datetime, timedelta
from sqlalchemy import func

class ExpenseCategory(db.Model):
    __tablename__ = 'expense_categories'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    description = db.Column(db.Text)
    image_path = db.Column(db.String(255))
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    expenses = db.relationship('Expense', backref='category', lazy='dynamic')
    
    def get_total_expenses(self, start_date=None, end_date=None):
        query = self.expenses
        if start_date:
            query = query.filter(Expense.date >= start_date)
        if end_date:
            query = query.filter(Expense.date <= end_date)
        return sum(expense.amount for expense in query)
    
    def __repr__(self):
        return f'<ExpenseCategory {self.name}>'

class Expense(db.Model):
    __tablename__ = 'expenses'
    
    id = db.Column(db.Integer, primary_key=True)
    category_id = db.Column(db.Integer, db.ForeignKey('expense_categories.id'), nullable=False)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
    amount = db.Column(db.Float, nullable=False)
    description = db.Column(db.Text)
    reference = db.Column(db.String(50))
    payment_method = db.Column(db.String(20))
    is_recurring = db.Column(db.Boolean, default=False)
    recurring_interval = db.Column(db.String(20))  # monthly, yearly, etc.
    recurring_day = db.Column(db.Integer)
    image_path = db.Column(db.String(255))  # Pour les reçus/factures
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    @staticmethod
    def get_today_expenses(owner_id):
        today = datetime.utcnow().date()
        tomorrow = today + timedelta(days=1)
        return db.session.query(func.sum(Expense.amount)).filter(
            Expense.owner_id == owner_id,
            Expense.date >= today,
            Expense.date < tomorrow
        ).scalar() or 0

    def __repr__(self):
        return f'<Expense {self.amount} - {self.category.name}>'

class Budget(db.Model):
    __tablename__ = 'budgets'
    
    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    category_id = db.Column(db.Integer, db.ForeignKey('expense_categories.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    period = db.Column(db.String(20), nullable=False)  # monthly, yearly
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def get_expenses(self):
        query = Expense.query.filter_by(
            category_id=self.category_id,
            owner_id=self.owner_id
        )
        if self.start_date:
            query = query.filter(Expense.date >= self.start_date)
        if self.end_date:
            query = query.filter(Expense.date <= self.end_date)
        return query.all()
    
    def get_total_expenses(self):
        expenses = self.get_expenses()
        return sum(expense.amount for expense in expenses)
    
    def get_remaining_budget(self):
        return self.amount - self.get_total_expenses()
    
    def get_budget_status(self):
        total_expenses = self.get_total_expenses()
        if total_expenses > self.amount:
            return 'exceeded'
        elif total_expenses >= self.amount * 0.9:
            return 'warning'
        return 'good'
    
    def __repr__(self):
        return f'<Budget {self.amount} for {self.category.name}>'