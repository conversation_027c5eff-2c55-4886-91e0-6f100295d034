{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Modifier la Réservation</h2>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.customer_name.label(class="form-label") }}
                                {{ form.customer_name(class="form-control") }}
                                {% if form.customer_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.customer_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {{ form.customer_phone.label(class="form-label") }}
                                {{ form.customer_phone(class="form-control") }}
                                {% if form.customer_phone.errors %}
                                    <div class="text-danger">
                                        {% for error in form.customer_phone.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.table_id.label(class="form-label") }}
                                {{ form.table_id(class="form-control") }}
                                {% if form.table_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.table_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {{ form.number_of_guests.label(class="form-label") }}
                                {{ form.number_of_guests(class="form-control") }}
                                {% if form.number_of_guests.errors %}
                                    <div class="text-danger">
                                        {% for error in form.number_of_guests.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.reservation_date.label(class="form-label") }}
                                {{ form.reservation_date(class="form-control") }}
                                {% if form.reservation_date.errors %}
                                    <div class="text-danger">
                                        {% for error in form.reservation_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {{ form.duration_minutes.label(class="form-label") }}
                                {{ form.duration_minutes(class="form-control") }}
                                {% if form.duration_minutes.errors %}
                                    <div class="text-danger">
                                        {% for error in form.duration_minutes.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.notes.label(class="form-label") }}
                            {{ form.notes(class="form-control", rows="3") }}
                            {% if form.notes.errors %}
                                <div class="text-danger">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('tables.reservation_details', id=reservation.id) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer les modifications
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialiser les champs de date/heure
document.addEventListener('DOMContentLoaded', function() {
    // Si vous utilisez un datepicker, l'initialiser ici
    const dateField = document.getElementById('reservation_date');
    if (dateField) {
        // Configuration du champ date/heure
        dateField.setAttribute('type', 'datetime-local');
    }
});
</script>
{% endblock %}
