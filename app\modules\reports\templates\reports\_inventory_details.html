<!-- Item Information -->
<div class="row mb-4">
    <div class="col-md-6">
        <h6 class="font-weight-bold">Informations générales</h6>
        <table class="table table-sm">
            <tr>
                <th style="width: 150px">Nom</th>
                <td>{{ item.name }}</td>
            </tr>
            <tr>
                <th>Catégorie</th>
                <td>
                    <span class="badge" style="background-color: {{ item.category.color if item.category else '#6c757d' }}">
                        {{ item.category.name if item.category else "Sans catégorie" }}
                    </span>
                </td>
            </tr>
            <tr>
                <th>Prix unitaire</th>
                <td>{{ "%.2f"|format(item.price_per_unit if item_type == 'ingredient' else item.price) }} €</td>
            </tr>
            <tr>
                <th>Stock actuel</th>
                <td>
                    {{ item.stock_quantity }} {{ item.unit }}
                    {% if item.stock_quantity <= 0 %}
                    <span class="badge bg-danger ms-2">Rupture</span>
                    {% elif item.stock_quantity <= item.minimum_stock %}
                    <span class="badge bg-warning ms-2">Stock bas</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>Stock minimum</th>
                <td>{{ item.minimum_stock }} {{ item.unit }}</td>
            </tr>
            {% if item.description %}
            <tr>
                <th>Description</th>
                <td>{{ item.description }}</td>
            </tr>
            {% endif %}
        </table>
    </div>
    <div class="col-md-6">
        <h6 class="font-weight-bold">Performance</h6>
        <table class="table table-sm">
            <tr>
                <th style="width: 150px">Valeur du stock</th>
                <td>{{ "%.2f"|format(item.stock_quantity * (item.price_per_unit if item_type == 'ingredient' else item.price)) }} €</td>
            </tr>
            <tr>
                <th>Mouvements</th>
                <td>{{ stats.movements_count if stats.movements_count else 0 }} sur la période</td>
            </tr>
            <tr>
                <th>Rotation</th>
                <td>{{ "%.1f"|format(stats.turnover_rate if stats.turnover_rate else 0) }} par an</td>
            </tr>
            <tr>
                <th>Stock optimal</th>
                <td>{{ "%.1f"|format(stats.optimal_stock) }} {{ item.unit }}</td>
            </tr>
        </table>
    </div>
</div>

<!-- Quick Stock Update Buttons -->
<div class="row mb-4">
    <div class="col-12">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#quickStockInModal">
                <i class="fas fa-plus-circle me-1"></i> Entrée rapide
            </button>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#quickStockOutModal">
                <i class="fas fa-minus-circle me-1"></i> Sortie rapide
            </button>
        </div>
    </div>
</div>

<!-- Stock Level Trend -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="font-weight-bold mb-0">Évolution du stock</h6>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-primary {% if period == 'today' %}active{% endif %}" data-period="today">Aujourd'hui</button>
                <button type="button" class="btn btn-outline-primary {% if period == 'week' %}active{% endif %}" data-period="week">7 jours</button>
                <button type="button" class="btn btn-outline-primary {% if period == 'month' %}active{% endif %}" data-period="month">30 jours</button>
                <button type="button" class="btn btn-outline-primary {% if period == 'year' %}active{% endif %}" data-period="year">12 mois</button>
                <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#customPeriodModal">
                    <i class="fas fa-calendar-alt"></i>
                </button>
            </div>
        </div>
        <div class="chart-container" style="position: relative; height: 300px;">
            <canvas id="stockLevelChart" 
                    data-minimum-stock="{{ item.minimum_stock }}"
                    data-unit="{{ item.unit }}"></canvas>
        </div>
    </div>
</div>

<!-- Custom Period Modal -->
<div class="modal fade" id="customPeriodModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Période personnalisée</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="customPeriodForm" onsubmit="return false;">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Date de début</label>
                                <input type="date" name="start_date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Date de fin</label>
                                <input type="date" name="end_date" class="form-control" required>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="applyCustomPeriod()">Appliquer</button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden data for chart updates -->
<div id="chart-data" style="display: none;">
    <div id="dates-data">{{ dates|tojson|safe }}</div>
    <div id="stock-levels-data">{{ stock_levels|tojson|safe }}</div>
</div>

<!-- Stock Movements -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="font-weight-bold mb-0">Mouvements de stock</h6>
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#exportModal">
                    <i class="fas fa-file-export"></i> Exporter
                </button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Type</th>
                        <th>Motif</th>
                        <th>Quantité</th>
                        <th>Stock avant</th>
                        <th>Stock après</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    {% for movement in movements %}
                    <tr>
                        <td>{{ movement.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                        <td>
                            {% if movement.type == 'in' %}
                            <span class="badge bg-success">Entrée</span>
                            {% else %}
                            <span class="badge bg-danger">Sortie</span>
                            {% endif %}
                        </td>
                        <td>{{ movement.reason }}</td>
                        <td>
                            {% if movement.type == 'in' %}
                            <span class="text-success">+{{ movement.quantity }}</span>
                            {% else %}
                            <span class="text-danger">-{{ movement.quantity }}</span>
                            {% endif %}
                        </td>
                        <td>{{ movement.previous_quantity }}</td>
                        <td>{{ movement.new_quantity }}</td>
                        <td>{{ movement.notes or '' }}</td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">Aucun mouvement de stock enregistré</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Usage in Recipes -->
{% if recipes %}
<div class="row">
    <div class="col-12">
        <h6 class="font-weight-bold mb-3">Utilisation dans les recettes</h6>
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>Produit</th>
                        <th class="text-center">Quantité par unité</th>
                        <th class="text-end">Consommation totale</th>
                    </tr>
                </thead>
                <tbody>
                    {% for recipe in recipes %}
                    <tr>
                        <td>{{ recipe.recipe.product.name if recipe.recipe and recipe.recipe.product else 'N/A' }}</td>
                        <td class="text-center">{{ recipe.quantity }} {{ item.unit }}</td>
                        <td class="text-end">{{ "%.1f"|format(recipe.total_consumption) if recipe.total_consumption else '0.0' }} {{ item.unit }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<!-- Quick Stock In Modal -->
<div class="modal fade" id="quickStockInModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Entrée de stock</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('reports.quick_stock_update') }}" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <input type="hidden" name="item_id" value="{{ item.id }}">
                <input type="hidden" name="item_type" value="{{ item_type }}">
                <input type="hidden" name="operation" value="add">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="quantity" class="form-label">Quantité à ajouter</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="quantity" name="quantity" step="0.01" min="0" required>
                            <span class="input-group-text">{{ item.unit }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="reason" class="form-label">Motif</label>
                        <select class="form-select" id="reason" name="reason">
                            <option value="purchase">Achat</option>
                            <option value="adjustment">Ajustement</option>
                            <option value="other">Autre</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus-circle me-1"></i> Ajouter
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Quick Stock Out Modal -->
<div class="modal fade" id="quickStockOutModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Sortie de stock</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('reports.quick_stock_update') }}" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <input type="hidden" name="item_id" value="{{ item.id }}">
                <input type="hidden" name="item_type" value="{{ item_type }}">
                <input type="hidden" name="operation" value="subtract">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="quantity" class="form-label">Quantité à retirer</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="quantity" name="quantity" step="0.01" min="0" max="{{ item.stock_quantity }}" required>
                            <span class="input-group-text">{{ item.unit }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="reason" class="form-label">Motif</label>
                        <select class="form-select" id="reason" name="reason">
                            <option value="loss">Perte</option>
                            <option value="adjustment">Ajustement</option>
                            <option value="other">Autre</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-minus-circle me-1"></i> Retirer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportModalLabel">Exporter les mouvements de stock</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label for="start_date" class="form-label">Date de début</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" required>
                    </div>
                    <div class="mb-3">
                        <label for="end_date" class="form-label">Date de fin</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Format</label>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="format" id="csv" value="csv" checked>
                            <label class="btn btn-outline-primary" for="csv">CSV</label>
                            <input type="radio" class="btn-check" name="format" id="excel" value="excel">
                            <label class="btn btn-outline-primary" for="excel">Excel</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="exportMovements()">Exporter</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Chart.js library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- JavaScript for Charts -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing chart...');
    
    // Initialize chart when any modal is shown
    document.addEventListener('shown.bs.modal', function (event) {
        console.log('Modal shown, initializing chart...');
        const modal = event.target;
        if (modal.contains(document.getElementById('stockLevelChart'))) {
            console.log('Found chart in modal, initializing...');
            initializeChart();
        }
    });

    // Function to initialize the chart
    function initializeChart() {
        // Get the canvas element
        const ctx = document.getElementById('stockLevelChart');
        if (!ctx) {
            console.error('Cannot find chart canvas element #stockLevelChart');
            return;
        }
        console.log('Canvas element found');

        // Get the data
        const dates = {{ dates|tojson|safe }};
        const stockLevels = {{ stock_levels|tojson|safe }};
        const minimumStock = {{ item.minimum_stock }};
        const unit = "{{ item.unit }}";

        console.log('Chart data:', {
            dates: dates,
            stockLevels: stockLevels,
            minimumStock: minimumStock,
            unit: unit
        });

        // Destroy existing chart if it exists
        let existingChart = Chart.getChart(ctx);
        if (existingChart) {
            existingChart.destroy();
        }

        // Create the chart
        try {
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [{
                        label: 'Niveau de stock',
                        data: stockLevels,
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        cubicInterpolationMode: 'monotone'
                    }, {
                        label: 'Stock minimum',
                        data: Array(dates.length).fill(minimumStock),
                        borderColor: '#dc3545',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        fill: false,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y + ' ' + unit;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value + ' ' + unit;
                                }
                            }
                        }
                    }
                }
            });
            console.log('Chart created successfully');
        } catch (error) {
            console.error('Error creating chart:', error);
        }
    }

    // Initialize chart on page load
    initializeChart();

    // Handle period buttons
    document.querySelectorAll('[data-period]').forEach(button => {
        button.addEventListener('click', function() {
            // Update active state
            document.querySelectorAll('[data-period]').forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');

            // Get the selected period
            const period = this.dataset.period;
            const itemId = {{ item.id }};

            // Fetch new data
            fetch(`/reports/inventory/${itemId}/details?period=${period}`)
                .then(response => response.text())
                .then(html => {
                    // Parse the new data from the HTML
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newDates = JSON.parse(doc.getElementById('dates-data').textContent);
                    const newStockLevels = JSON.parse(doc.getElementById('stock-levels-data').textContent);

                    // Update chart data
                    const chart = Chart.getChart('stockLevelChart');
                    if (chart) {
                        chart.data.labels = newDates;
                        chart.data.datasets[0].data = newStockLevels;
                        chart.data.datasets[1].data = Array(newDates.length).fill({{ item.minimum_stock }});
                        chart.update();
                    }
                });
        });
    });

    // Set default dates for custom period
    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);

    const customPeriodForm = document.getElementById('customPeriodForm');
    if (customPeriodForm) {
        customPeriodForm.querySelector('[name="start_date"]').value = thirtyDaysAgo.toISOString().split('T')[0];
        customPeriodForm.querySelector('[name="end_date"]').value = today.toISOString().split('T')[0];
    }
});

// Function to apply custom period
function applyCustomPeriod() {
    const form = document.getElementById('customPeriodForm');
    const startDate = form.querySelector('[name="start_date"]').value;
    const endDate = form.querySelector('[name="end_date"]').value;
    const itemId = {{ item.id }};

    if (!startDate || !endDate) {
        alert('Veuillez sélectionner une période');
        return;
    }

    // Fetch new data with custom period
    fetch(`/reports/inventory/${itemId}/details?period=custom&start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.text())
        .then(html => {
            // Parse the new data from the HTML
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const newDates = JSON.parse(doc.getElementById('dates-data').textContent);
            const newStockLevels = JSON.parse(doc.getElementById('stock-levels-data').textContent);

            // Update chart data
            const chart = Chart.getChart('stockLevelChart');
            if (chart) {
                chart.data.labels = newDates;
                chart.data.datasets[0].data = newStockLevels;
                chart.data.datasets[1].data = Array(newDates.length).fill({{ item.minimum_stock }});
                chart.update();

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('customPeriodModal'));
                if (modal) modal.hide();
            }
        });
}

// Export functions
function exportMovements() {
    const form = document.getElementById('exportForm');
    const startDate = form.start_date.value;
    const endDate = form.end_date.value;
    const format = form.format.value;

    if (!startDate || !endDate) {
        alert('Veuillez sélectionner une période');
        return;
    }

    window.location.href = `{{ url_for('reports.export_movements', item_id=item.id) }}?start_date=${startDate}&end_date=${endDate}&format=${format}`;
}

// Set default dates
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);

    document.getElementById('start_date').value = thirtyDaysAgo.toISOString().split('T')[0];
    document.getElementById('end_date').value = today.toISOString().split('T')[0];
});
</script> 