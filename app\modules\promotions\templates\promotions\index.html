{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Gestion des Promotions</h1>
        <a href="{{ url_for('promotions.new') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Nouvelle Promotion
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Nom</th>
                            <th>Type</th>
                            <th>Valeur</th>
                            <th>Période</th>
                            <th>Statut</th>
                            <th>Utilisation</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for promotion in promotions %}
                        <tr>
                            <td>{{ promotion.name }}</td>
                            <td>
                                {% if promotion.promotion_type == 'percentage' %}
                                    Pourcentage
                                {% elif promotion.promotion_type == 'fixed' %}
                                    Montant fixe
                                {% else %}
                                    Achetez {{ promotion.buy_x }} obtenez {{ promotion.get_y }}
                                {% endif %}
                            </td>
                            <td>
                                {% if promotion.promotion_type == 'percentage' %}
                                    {{ promotion.value }}%
                                {% elif promotion.promotion_type == 'fixed' %}
                                    {{ "%.2f"|format(promotion.value) }} €
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                Du {{ promotion.start_date.strftime('%d/%m/%Y') }}
                                au {{ promotion.end_date.strftime('%d/%m/%Y') }}
                            </td>
                            <td>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" 
                                           {% if promotion.is_active %}checked{% endif %}
                                           onchange="togglePromotion({{ promotion.id }}, this)">
                                </div>
                            </td>
                            <td>
                                {% if promotion.usage_limit %}
                                    {{ promotion.current_usage }}/{{ promotion.usage_limit }}
                                {% else %}
                                    {{ promotion.current_usage }}
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('promotions.show', id=promotion.id) }}" 
                                       class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('promotions.edit', id=promotion.id) }}" 
                                       class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ url_for('promotions.delete', id=promotion.id) }}" method="POST" style="display: inline;">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette promotion ?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function togglePromotion(promotionId, checkbox) {
    fetch(`/promotions/${promotionId}/toggle`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            checkbox.checked = !checkbox.checked;
            alert('Erreur lors de la modification du statut');
        }
    })
    .catch(error => {
        checkbox.checked = !checkbox.checked;
        alert('Erreur lors de la modification du statut');
    });
}
</script>
{% endblock %} 