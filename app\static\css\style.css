/* Style général */
body {
    background-color: #f8f9fa;
}

/* Navbar */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,.05);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-5px);
}

/* Boutons */
.btn {
    border-radius: 5px;
}

/* Tables */
.table th {
    border-top: none;
    background-color: #f8f9fa;
}

/* Forms */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 5px;
}

/* Dashboard cards */
.dashboard-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,.05);
}

.dashboard-card .icon {
    font-size: 2rem;
    margin-bottom: 15px;
}

.dashboard-card .title {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.dashboard-card .value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.1rem;
    }
    
    .dashboard-card {
        margin-bottom: 15px;
    }
    
    .dashboard-card .icon {
        font-size: 1.5rem;
    }
    
    .dashboard-card .value {
        font-size: 1.2rem;
    }
}

.nav-item.dropdown:hover .dropdown-menu {
    display: block;
} 