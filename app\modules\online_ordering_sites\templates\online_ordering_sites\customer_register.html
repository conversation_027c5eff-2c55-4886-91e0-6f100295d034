{% extends "online_ordering_sites/base.html" %}

{% block title %}Inscription - {{ site.site_name or site.owner.username }}{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        Créer un compte
                    </h4>
                    <p class="mb-0 mt-2">Rejoignez {{ site.site_name or site.owner.username }}</p>
                </div>
                
                <div class="card-body p-4">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <!-- Informations personnelles -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.first_name.label(class="form-label") }}
                                    {{ form.first_name(class="form-control" + (" is-invalid" if form.first_name.errors else "")) }}
                                    {% if form.first_name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.first_name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.last_name.label(class="form-label") }}
                                    {{ form.last_name(class="form-control" + (" is-invalid" if form.last_name.errors else "")) }}
                                    {% if form.last_name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.last_name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Email et téléphone -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.email.label(class="form-label") }}
                                    {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                    {% if form.email.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.email.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.phone.label(class="form-label") }}
                                    {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                                    {% if form.phone.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.phone.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Mots de passe -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.password.label(class="form-label") }}
                                    {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                                    {% if form.password.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.password.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.password_confirm.label(class="form-label") }}
                                    {{ form.password_confirm(class="form-control" + (" is-invalid" if form.password_confirm.errors else "")) }}
                                    {% if form.password_confirm.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.password_confirm.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Adresse -->
                        <div class="form-group mb-3">
                            {{ form.address.label(class="form-label") }}
                            {{ form.address(class="form-control" + (" is-invalid" if form.address.errors else ""), rows="3") }}
                            {% if form.address.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.address.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Ville, Code postal, Pays -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.city.label(class="form-label") }}
                                    {{ form.city(class="form-control" + (" is-invalid" if form.city.errors else "")) }}
                                    {% if form.city.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.city.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.postal_code.label(class="form-label") }}
                                    {{ form.postal_code(class="form-control" + (" is-invalid" if form.postal_code.errors else "")) }}
                                    {% if form.postal_code.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.postal_code.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.country.label(class="form-label") }}
                                    {{ form.country(class="form-control" + (" is-invalid" if form.country.errors else "")) }}
                                    {% if form.country.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.country.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Préférences de notification -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-muted mb-3">Préférences de notification</h6>
                                <div class="form-check mb-2">
                                    {{ form.email_notifications(class="form-check-input") }}
                                    {{ form.email_notifications.label(class="form-check-label") }}
                                </div>
                                <div class="form-check">
                                    {{ form.sms_notifications(class="form-check-input") }}
                                    {{ form.sms_notifications.label(class="form-check-label") }}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Boutons -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>
                                Créer mon compte
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="card-footer text-center">
                    <p class="mb-0">
                        Vous avez déjà un compte ?
                        <a href="{{ url_for('online_ordering_sites.customer_login') }}" class="text-primary">
                            Connectez-vous ici
                        </a>
                    </p>
                </div>
            </div>
            
            <!-- Avantages de créer un compte -->
            <div class="card mt-4 border-0 bg-light">
                <div class="card-body text-center">
                    <h5 class="card-title text-primary">Pourquoi créer un compte ?</h5>
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <i class="fas fa-clock fa-2x text-primary mb-2"></i>
                            <p class="small">Commande plus rapide</p>
                        </div>
                        <div class="col-md-4">
                            <i class="fas fa-heart fa-2x text-primary mb-2"></i>
                            <p class="small">Sauvegardez vos favoris</p>
                        </div>
                        <div class="col-md-4">
                            <i class="fas fa-history fa-2x text-primary mb-2"></i>
                            <p class="small">Historique des commandes</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation en temps réel
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input[required]');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value.trim() === '') {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });
    
    // Validation des mots de passe
    const password = document.getElementById('password');
    const passwordConfirm = document.getElementById('password_confirm');
    
    function validatePasswords() {
        if (password.value !== passwordConfirm.value) {
            passwordConfirm.setCustomValidity('Les mots de passe ne correspondent pas');
        } else {
            passwordConfirm.setCustomValidity('');
        }
    }
    
    password.addEventListener('input', validatePasswords);
    passwordConfirm.addEventListener('input', validatePasswords);
});
</script>
{% endblock %}
