from datetime import datetime, date, time, timedelta
from app.extensions import db
from app.modules.auth.models import User
import enum
from sqlalchemy import func
from decimal import Decimal

class EmployeeStatus(str, enum.Enum):
    ACTIVE = 'ACTIVE'
    INACTIVE = 'INACTIVE'
    SUSPENDED = 'SUSPENDED'
    TERMINATED = 'TERMINATED'

class ContractType(str, enum.Enum):
    FULL_TIME = 'FULL_TIME'
    PART_TIME = 'PART_TIME'
    TEMPORARY = 'TEMPORARY'
    INTERNSHIP = 'INTERNSHIP'
    FREELANCE = 'FREELANCE'

class PaymentFrequency(str, enum.Enum):
    HOURLY = 'HOURLY'
    DAILY = 'DAILY'
    WEEKLY = 'WEEKLY'
    BIWEEKLY = 'BIWEEKLY'
    MONTHLY = 'MONTHLY'

class AttendanceStatus(str, enum.Enum):
    PRESENT = 'PRESENT'
    ABSENT = 'ABSENT'
    LATE = 'LATE'
    SICK_LEAVE = 'SICK_LEAVE'
    VACATION = 'VACATION'
    PERSONAL_LEAVE = 'PERSONAL_LEAVE'

class ShiftType(str, enum.Enum):
    MORNING = 'MORNING'
    AFTERNOON = 'AFTERNOON'
    EVENING = 'EVENING'
    NIGHT = 'NIGHT'
    FULL_DAY = 'FULL_DAY'

class Employee(db.Model):
    __tablename__ = 'employees'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.String(20), unique=True, nullable=False)  # ID employé unique
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # Lien avec User si applicable
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Informations personnelles
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    email = db.Column(db.String(120), nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    address = db.Column(db.Text, nullable=True)
    date_of_birth = db.Column(db.Date, nullable=True)
    national_id = db.Column(db.String(50), nullable=True)  # Numéro de sécurité sociale, etc.
    
    # Informations d'emploi
    hire_date = db.Column(db.Date, nullable=False, default=date.today)
    termination_date = db.Column(db.Date, nullable=True)
    status = db.Column(db.Enum(EmployeeStatus), default=EmployeeStatus.ACTIVE)
    contract_type = db.Column(db.Enum(ContractType), default=ContractType.FULL_TIME)
    department = db.Column(db.String(100), nullable=True)
    position = db.Column(db.String(100), nullable=False)
    
    # Informations salariales
    base_salary = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    hourly_rate = db.Column(db.Numeric(8, 2), nullable=True)
    payment_frequency = db.Column(db.Enum(PaymentFrequency), default=PaymentFrequency.MONTHLY)
    
    # Métadonnées
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # Relations
    user = db.relationship('User', backref='employee_profile', foreign_keys=[user_id])
    owner = db.relationship('User', backref='employees', foreign_keys=[owner_id])
    profile = db.relationship('EmployeeProfile', backref='employee', uselist=False, cascade='all, delete-orphan')
    schedules = db.relationship('WorkSchedule', backref='employee', lazy='dynamic', cascade='all, delete-orphan')
    attendances = db.relationship('Attendance', backref='employee', lazy='dynamic', cascade='all, delete-orphan')
    payrolls = db.relationship('Payroll', backref='employee', lazy='dynamic', cascade='all, delete-orphan')
    performances = db.relationship('Performance', backref='employee', lazy='dynamic', cascade='all, delete-orphan')
    documents = db.relationship('EmployeeDocument', backref='employee', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Employee {self.employee_id}: {self.first_name} {self.last_name}>'
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def is_active(self):
        return self.status == EmployeeStatus.ACTIVE
    
    def get_current_schedule(self):
        """Récupère le planning actuel de l'employé"""
        today = date.today()
        return self.schedules.filter(
            WorkSchedule.start_date <= today,
            WorkSchedule.end_date >= today
        ).first()
    
    def get_monthly_hours(self, year, month):
        """Calcule les heures travaillées pour un mois donné"""
        start_date = date(year, month, 1)
        if month == 12:
            end_date = date(year + 1, 1, 1)
        else:
            end_date = date(year, month + 1, 1)
        
        total_hours = db.session.query(func.sum(Attendance.hours_worked)).filter(
            Attendance.employee_id == self.id,
            Attendance.date >= start_date,
            Attendance.date < end_date,
            Attendance.status == AttendanceStatus.PRESENT
        ).scalar() or 0
        
        return float(total_hours)

class EmployeeProfile(db.Model):
    __tablename__ = 'employee_profiles'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    
    # Informations détaillées
    emergency_contact_name = db.Column(db.String(100), nullable=True)
    emergency_contact_phone = db.Column(db.String(20), nullable=True)
    emergency_contact_relationship = db.Column(db.String(50), nullable=True)
    
    # Informations bancaires
    bank_name = db.Column(db.String(100), nullable=True)
    account_number = db.Column(db.String(50), nullable=True)
    routing_number = db.Column(db.String(20), nullable=True)
    
    # Informations professionnelles
    skills = db.Column(db.Text, nullable=True)  # JSON ou texte séparé par virgules
    certifications = db.Column(db.Text, nullable=True)
    languages = db.Column(db.Text, nullable=True)
    
    # Préférences
    preferred_shifts = db.Column(db.Text, nullable=True)  # JSON des préférences d'horaires
    availability = db.Column(db.Text, nullable=True)  # JSON de la disponibilité
    
    # Métadonnées
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    def __repr__(self):
        return f'<EmployeeProfile {self.employee_id}>'

class WorkSchedule(db.Model):
    __tablename__ = 'work_schedules'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Période du planning
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    
    # Détails du shift
    day_of_week = db.Column(db.Integer, nullable=False)  # 0=Lundi, 6=Dimanche
    shift_type = db.Column(db.Enum(ShiftType), nullable=False)
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    
    # Informations supplémentaires
    break_duration = db.Column(db.Integer, default=30)  # en minutes
    notes = db.Column(db.Text, nullable=True)
    is_recurring = db.Column(db.Boolean, default=True)
    
    # Métadonnées
    created_at = db.Column(db.DateTime, default=datetime.now)
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Relations
    owner = db.relationship('User', backref='work_schedules', foreign_keys=[owner_id])
    created_by = db.relationship('User', foreign_keys=[created_by_id])
    
    def __repr__(self):
        return f'<WorkSchedule {self.employee_id}: {self.day_of_week} {self.start_time}-{self.end_time}>'
    
    @property
    def duration_hours(self):
        """Calcule la durée du shift en heures"""
        start_datetime = datetime.combine(date.today(), self.start_time)
        end_datetime = datetime.combine(date.today(), self.end_time)
        
        # Gérer le cas où le shift se termine le jour suivant
        if end_datetime < start_datetime:
            end_datetime = datetime.combine(date.today() + timedelta(days=1), self.end_time)
        
        duration = end_datetime - start_datetime
        return duration.total_seconds() / 3600 - (self.break_duration / 60)

class Attendance(db.Model):
    __tablename__ = 'attendances'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Informations de présence
    date = db.Column(db.Date, nullable=False)
    status = db.Column(db.Enum(AttendanceStatus), nullable=False)

    # Heures de travail
    clock_in_time = db.Column(db.DateTime, nullable=True)
    clock_out_time = db.Column(db.DateTime, nullable=True)
    break_start_time = db.Column(db.DateTime, nullable=True)
    break_end_time = db.Column(db.DateTime, nullable=True)

    # Calculs
    scheduled_hours = db.Column(db.Numeric(5, 2), default=0)
    hours_worked = db.Column(db.Numeric(5, 2), default=0)
    overtime_hours = db.Column(db.Numeric(5, 2), default=0)
    break_duration = db.Column(db.Integer, default=0)  # en minutes

    # Informations supplémentaires
    notes = db.Column(db.Text, nullable=True)
    approved_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    approved_at = db.Column(db.DateTime, nullable=True)

    # Métadonnées
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relations
    owner = db.relationship('User', backref='attendances', foreign_keys=[owner_id])
    approved_by = db.relationship('User', foreign_keys=[approved_by_id])

    def __repr__(self):
        return f'<Attendance {self.employee_id}: {self.date} - {self.status}>'

    def calculate_hours_worked(self):
        """Calcule automatiquement les heures travaillées"""
        if self.clock_in_time and self.clock_out_time:
            total_time = self.clock_out_time - self.clock_in_time
            total_hours = total_time.total_seconds() / 3600

            # Soustraire la pause
            break_hours = self.break_duration / 60 if self.break_duration else 0
            self.hours_worked = max(0, total_hours - break_hours)

            # Calculer les heures supplémentaires (plus de 8h par jour)
            if self.hours_worked > 8:
                self.overtime_hours = self.hours_worked - 8
            else:
                self.overtime_hours = 0

    @property
    def is_late(self):
        """Vérifie si l'employé est en retard"""
        if not self.clock_in_time:
            return False

        schedule = self.employee.get_current_schedule()
        if not schedule:
            return False

        scheduled_start = datetime.combine(self.date, schedule.start_time)
        return self.clock_in_time > scheduled_start

class Payroll(db.Model):
    __tablename__ = 'payrolls'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Période de paie
    pay_period_start = db.Column(db.Date, nullable=False)
    pay_period_end = db.Column(db.Date, nullable=False)
    pay_date = db.Column(db.Date, nullable=False)

    # Heures et salaire de base
    regular_hours = db.Column(db.Numeric(8, 2), default=0)
    overtime_hours = db.Column(db.Numeric(8, 2), default=0)
    regular_rate = db.Column(db.Numeric(8, 2), nullable=False)
    overtime_rate = db.Column(db.Numeric(8, 2), nullable=False)

    # Calculs salariaux
    gross_pay = db.Column(db.Numeric(10, 2), default=0)

    # Déductions
    tax_deduction = db.Column(db.Numeric(10, 2), default=0)
    social_security_deduction = db.Column(db.Numeric(10, 2), default=0)
    health_insurance_deduction = db.Column(db.Numeric(10, 2), default=0)
    other_deductions = db.Column(db.Numeric(10, 2), default=0)
    total_deductions = db.Column(db.Numeric(10, 2), default=0)

    # Bonus et ajustements
    bonus = db.Column(db.Numeric(10, 2), default=0)
    commission = db.Column(db.Numeric(10, 2), default=0)
    adjustments = db.Column(db.Numeric(10, 2), default=0)

    # Salaire net
    net_pay = db.Column(db.Numeric(10, 2), default=0)

    # Statut
    is_processed = db.Column(db.Boolean, default=False)
    processed_at = db.Column(db.DateTime, nullable=True)
    processed_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)

    # Notes
    notes = db.Column(db.Text, nullable=True)

    # Métadonnées
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relations
    owner = db.relationship('User', backref='payrolls', foreign_keys=[owner_id])
    processed_by = db.relationship('User', foreign_keys=[processed_by_id])

    def __repr__(self):
        return f'<Payroll {self.employee_id}: {self.pay_period_start} to {self.pay_period_end}>'

    def calculate_pay(self):
        """Calcule automatiquement la paie"""
        # Salaire de base
        regular_pay = self.regular_hours * self.regular_rate
        overtime_pay = self.overtime_hours * self.overtime_rate

        # Salaire brut
        self.gross_pay = regular_pay + overtime_pay + self.bonus + self.commission + self.adjustments

        # Total des déductions
        self.total_deductions = (
            self.tax_deduction +
            self.social_security_deduction +
            self.health_insurance_deduction +
            self.other_deductions
        )

        # Salaire net
        self.net_pay = self.gross_pay - self.total_deductions

class Performance(db.Model):
    __tablename__ = 'performances'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Période d'évaluation
    evaluation_period_start = db.Column(db.Date, nullable=False)
    evaluation_period_end = db.Column(db.Date, nullable=False)
    evaluation_date = db.Column(db.Date, nullable=False, default=date.today)

    # Évaluateur
    evaluator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Critères d'évaluation (sur 5)
    punctuality_score = db.Column(db.Integer, default=3)  # 1-5
    quality_of_work_score = db.Column(db.Integer, default=3)
    teamwork_score = db.Column(db.Integer, default=3)
    communication_score = db.Column(db.Integer, default=3)
    initiative_score = db.Column(db.Integer, default=3)
    customer_service_score = db.Column(db.Integer, default=3)

    # Score global
    overall_score = db.Column(db.Numeric(3, 2), default=3.0)

    # Commentaires
    strengths = db.Column(db.Text, nullable=True)
    areas_for_improvement = db.Column(db.Text, nullable=True)
    goals_next_period = db.Column(db.Text, nullable=True)
    evaluator_comments = db.Column(db.Text, nullable=True)
    employee_comments = db.Column(db.Text, nullable=True)

    # Statut
    is_finalized = db.Column(db.Boolean, default=False)
    employee_acknowledged = db.Column(db.Boolean, default=False)
    acknowledged_at = db.Column(db.DateTime, nullable=True)

    # Métadonnées
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relations
    owner = db.relationship('User', backref='performance_evaluations', foreign_keys=[owner_id])
    evaluator = db.relationship('User', foreign_keys=[evaluator_id])

    def __repr__(self):
        return f'<Performance {self.employee_id}: {self.evaluation_date}>'

    def calculate_overall_score(self):
        """Calcule le score global basé sur les critères individuels"""
        scores = [
            self.punctuality_score,
            self.quality_of_work_score,
            self.teamwork_score,
            self.communication_score,
            self.initiative_score,
            self.customer_service_score
        ]

        valid_scores = [score for score in scores if score is not None]
        if valid_scores:
            self.overall_score = sum(valid_scores) / len(valid_scores)
        else:
            self.overall_score = 0

    @property
    def performance_level(self):
        """Retourne le niveau de performance basé sur le score global"""
        if self.overall_score >= 4.5:
            return "Excellent"
        elif self.overall_score >= 3.5:
            return "Bon"
        elif self.overall_score >= 2.5:
            return "Satisfaisant"
        elif self.overall_score >= 1.5:
            return "Nécessite amélioration"
        else:
            return "Insatisfaisant"

class EmployeeDocument(db.Model):
    __tablename__ = 'employee_documents'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Informations du document
    document_name = db.Column(db.String(200), nullable=False)
    document_type = db.Column(db.String(50), nullable=False)  # CV, Contract, Certificate, etc.
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer, nullable=True)  # en bytes
    mime_type = db.Column(db.String(100), nullable=True)

    # Dates importantes
    upload_date = db.Column(db.DateTime, default=datetime.now)
    expiry_date = db.Column(db.Date, nullable=True)  # Pour les certificats, permis, etc.

    # Métadonnées
    description = db.Column(db.Text, nullable=True)
    is_confidential = db.Column(db.Boolean, default=False)
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Relations
    owner = db.relationship('User', backref='employee_documents', foreign_keys=[owner_id])
    uploaded_by = db.relationship('User', foreign_keys=[uploaded_by_id])

    def __repr__(self):
        return f'<EmployeeDocument {self.employee_id}: {self.document_name}>'

    @property
    def is_expired(self):
        """Vérifie si le document est expiré"""
        if not self.expiry_date:
            return False
        return self.expiry_date < date.today()

    @property
    def expires_soon(self, days=30):
        """Vérifie si le document expire bientôt"""
        if not self.expiry_date:
            return False
        return self.expiry_date <= date.today() + timedelta(days=days)
