{% extends "base.html" %}

{% block title %}Présences - {{ employee.full_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-clock me-2"></i>Présences - {{ employee.full_name }}
                </h1>
                <div>
                    <a href="{{ url_for('employees.detail', id=employee.id) }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Retour
                    </a>
                    <a href="{{ url_for('employees.new_attendance', id=employee.id) }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Nouvelle Présence
                    </a>
                </div>
            </div>

            <!-- Filtres par mois/année -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3 align-items-end">
                        <div class="col-md-3">
                            <label for="month" class="form-label">Mois</label>
                            <select class="form-select" id="month" name="month">
                                {% for i in range(1, 13) %}
                                    <option value="{{ i }}" {{ 'selected' if i == current_month }}>
                                        {{ ['', 'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 
                                             'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'][i] }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="year" class="form-label">Année</label>
                            <select class="form-select" id="year" name="year">
                                {% for year in range(2020, 2030) %}
                                    <option value="{{ year }}" {{ 'selected' if year == current_year }}>{{ year }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-filter me-1"></i>Filtrer
                            </button>
                        </div>
                        <div class="col-md-3 text-end">
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="fas fa-download me-1"></i>Exporter
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="exportAttendance('pdf')">
                                        <i class="fas fa-file-pdf me-2"></i>PDF
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportAttendance('excel')">
                                        <i class="fas fa-file-excel me-2"></i>Excel
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Statistiques du mois -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ attendances.items|selectattr('status', 'equalto', 'PRESENT')|list|length }}</h4>
                                    <p class="mb-0">Présent</p>
                                </div>
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ attendances.items|selectattr('status', 'equalto', 'ABSENT')|list|length }}</h4>
                                    <p class="mb-0">Absent</p>
                                </div>
                                <i class="fas fa-times-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ attendances.items|selectattr('status', 'equalto', 'LATE')|list|length }}</h4>
                                    <p class="mb-0">En retard</p>
                                </div>
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">
                                        {{ "%.1f"|format(attendances.items|sum(attribute='hours_worked') or 0) }}h
                                    </h4>
                                    <p class="mb-0">Heures totales</p>
                                </div>
                                <i class="fas fa-hourglass-half fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liste des présences -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        Présences - {{ ['', 'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 
                                        'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'][current_month] }} {{ current_year }}
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if attendances.items %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Statut</th>
                                    <th>Arrivée</th>
                                    <th>Départ</th>
                                    <th>Pause</th>
                                    <th>Heures travaillées</th>
                                    <th>Heures sup.</th>
                                    <th>Notes</th>
                                    <th class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for attendance in attendances.items %}
                                <tr>
                                    <td>
                                        <strong>{{ attendance.date.strftime('%d/%m/%Y') }}</strong><br>
                                        <small class="text-muted">{{ attendance.date.strftime('%A')|title }}</small>
                                    </td>
                                    <td>
                                        {% if attendance.status == 'PRESENT' %}
                                            <span class="badge bg-success">Présent</span>
                                        {% elif attendance.status == 'ABSENT' %}
                                            <span class="badge bg-danger">Absent</span>
                                        {% elif attendance.status == 'LATE' %}
                                            <span class="badge bg-warning">En retard</span>
                                        {% elif attendance.status == 'SICK_LEAVE' %}
                                            <span class="badge bg-info">Congé maladie</span>
                                        {% elif attendance.status == 'VACATION' %}
                                            <span class="badge bg-primary">Congé</span>
                                        {% elif attendance.status == 'PERSONAL_LEAVE' %}
                                            <span class="badge bg-secondary">Congé personnel</span>
                                        {% endif %}
                                        {% if attendance.is_late %}
                                            <i class="fas fa-exclamation-triangle text-warning ms-1" title="Retard"></i>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.clock_in_time %}
                                            {{ attendance.clock_in_time.strftime('%H:%M') }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.clock_out_time %}
                                            {{ attendance.clock_out_time.strftime('%H:%M') }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.break_duration %}
                                            {{ attendance.break_duration }} min
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.hours_worked %}
                                            <strong>{{ "%.2f"|format(attendance.hours_worked) }}h</strong>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.overtime_hours %}
                                            <span class="text-warning">{{ "%.2f"|format(attendance.overtime_hours) }}h</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.notes %}
                                            <span class="text-truncate" style="max-width: 150px;" title="{{ attendance.notes }}">
                                                {{ attendance.notes }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('employees.edit_attendance', attendance_id=attendance.id) }}" 
                                               class="btn btn-sm btn-outline-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDeleteAttendance({{ attendance.id }}, '{{ attendance.date.strftime('%d/%m/%Y') }}')" 
                                                    title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune présence enregistrée</h5>
                        <p class="text-muted">Commencez par enregistrer la première présence de cet employé.</p>
                        <a href="{{ url_for('employees.new_attendance', id=employee.id) }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Nouvelle Présence
                        </a>
                    </div>
                    {% endif %}
                </div>
                
                {% if attendances.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="Navigation des présences">
                        <ul class="pagination justify-content-center mb-0">
                            {% if attendances.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('employees.attendance', id=employee.id, page=attendances.prev_num, month=current_month, year=current_year) }}">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in attendances.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != attendances.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('employees.attendance', id=employee.id, page=page_num, month=current_month, year=current_year) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if attendances.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('employees.attendance', id=employee.id, page=attendances.next_num, month=current_month, year=current_year) }}">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteAttendanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la présence du <strong id="attendanceDate"></strong> ?</p>
                <p class="text-muted">Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteAttendanceForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDeleteAttendance(attendanceId, attendanceDate) {
    document.getElementById('attendanceDate').textContent = attendanceDate;
    document.getElementById('deleteAttendanceForm').action = '/employees/attendance/' + attendanceId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteAttendanceModal')).show();
}

function exportAttendance(format) {
    const month = document.getElementById('month').value;
    const year = document.getElementById('year').value;
    const url = `/employees/{{ employee.id }}/attendance/export?format=${format}&month=${month}&year=${year}`;
    window.open(url, '_blank');
}
</script>
{% endblock %}
