{% extends "base.html" %}

{% block title %}Approvisionnement de Stock{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête avec navigation -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-3">
                <h2 class="mb-0">
                    <i class="fas fa-truck-loading text-success"></i> 
                    Approvisionnement de Stock
                </h2>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.purchase_orders_list') }}" class="btn btn-outline-primary btn-sm me-2">
                        <i class="fas fa-list"></i> Commandes
                    </a>
                    <a href="{{ url_for('inventory.pending_orders') }}" class="btn btn-outline-warning btn-sm me-2">
                        <i class="fas fa-clock"></i> Pending Marchandise
                    </a>
                    <a href="{{ url_for('inventory.bank_management_index') }}" class="btn btn-outline-info btn-sm me-2">
                        <i class="fas fa-university"></i> Gestion Bancaire
                    </a>
                    <a href="{{ url_for('inventory.quick_stock_adjustment') }}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-adjust"></i> Ajustement Rapide
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="container-fluid mb-4">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card bg-warning">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ stats.pending_orders }}</h3>
                        <p>Commandes en attente</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-danger">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ stats.unpaid_invoices }}</h3>
                        <p>Factures impayées</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-info">
                    <div class="stat-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ stats.low_stock_items }}</h3>
                        <p>Articles en stock bas</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-success">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3>100%</h3>
                        <p>Système opérationnel</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sélecteur de mode principal -->
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="mode-selector-card">
                    <div class="card-header text-center">
                        <h4><i class="fas fa-cogs"></i> Choisir le Mode d'Approvisionnement</h4>
                        <p class="text-muted">Sélectionnez le mode qui convient le mieux à votre situation</p>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Mode Formulaire -->
                            <div class="col-md-6">
                                <div class="mode-option" onclick="selectMode('form')">
                                    <div class="mode-icon">
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <h5>Mode Formulaire</h5>
                                    <p>Saisie détaillée et traditionnelle pour un contrôle précis de chaque article</p>
                                    <ul class="mode-features">
                                        <li><i class="fas fa-check text-success"></i> Saisie détaillée</li>
                                        <li><i class="fas fa-check text-success"></i> Validation complète</li>
                                        <li><i class="fas fa-check text-success"></i> Gestion des lots</li>
                                        <li><i class="fas fa-check text-success"></i> Dates d'expiration</li>
                                    </ul>
                                    <div class="mode-action">
                                        <a href="{{ url_for('inventory.stock_replenishment_form_mode') }}" 
                                           class="btn btn-primary btn-lg">
                                            <i class="fas fa-arrow-right"></i> Utiliser ce Mode
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Mode POS -->
                            <div class="col-md-6">
                                <div class="mode-option" onclick="selectMode('pos')">
                                    <div class="mode-icon">
                                        <i class="fas fa-cash-register"></i>
                                    </div>
                                    <h5>Mode POS</h5>
                                    <p>Interface rapide similaire au POS pour un approvisionnement efficace</p>
                                    <ul class="mode-features">
                                        <li><i class="fas fa-check text-success"></i> Saisie ultra-rapide</li>
                                        <li><i class="fas fa-check text-success"></i> Interface tactile</li>
                                        <li><i class="fas fa-check text-success"></i> Numpad intégré</li>
                                        <li><i class="fas fa-check text-success"></i> Ticket en temps réel</li>
                                    </ul>
                                    <div class="mode-action">
                                        <a href="{{ url_for('inventory.stock_replenishment_pos_mode') }}" 
                                           class="btn btn-success btn-lg">
                                            <i class="fas fa-arrow-right"></i> Utiliser ce Mode
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-4">
                <div class="quick-action-card">
                    <h6><i class="fas fa-search"></i> Recherche Rapide</h6>
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Rechercher un article...">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="quick-action-card">
                    <h6><i class="fas fa-truck"></i> Fournisseurs</h6>
                    <a href="{{ url_for('inventory.suppliers') }}" class="btn btn-outline-primary btn-sm w-100">
                        Gérer les Fournisseurs
                    </a>
                </div>
            </div>
            <div class="col-md-4">
                <div class="quick-action-card">
                    <h6><i class="fas fa-chart-bar"></i> Rapports</h6>
                    <a href="#" class="btn btn-outline-info btn-sm w-100">
                        Rapports d'Approvisionnement
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function selectMode(mode) {
    // Ajouter une animation de sélection
    const modeOptions = document.querySelectorAll('.mode-option');
    modeOptions.forEach(option => option.classList.remove('selected'));
    
    if (mode === 'form') {
        modeOptions[0].classList.add('selected');
    } else {
        modeOptions[1].classList.add('selected');
    }
}

// Animation au chargement
document.addEventListener('DOMContentLoaded', function() {
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
