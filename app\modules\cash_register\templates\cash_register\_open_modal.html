<div class="modal fade" id="openRegisterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{{ url_for('cash_register.open_register') }}" id="openRegisterForm">
                {{ register_form.csrf_token }}
                <div class="modal-header">
                    <h5 class="modal-title">Ouverture de caisse</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            Veuillez saisir le montant initial de la caisse.
                            {% if settings.minimum_amount_required %}
                            <br>Le montant minimum requis est de {{ format_currency(settings.minimum_amount) }}
                            {% else %}
                            <br>Le montant peut être positif, négatif ou nul.
                            {% endif %}
                            {% if last_closing_amount is not none %}
                            <br>Le dernier montant de fermeture était de {{ format_currency(last_closing_amount) }}
                            {% endif %}
                        </div>
                        
                        {% if settings.use_last_closing_amount and last_closing_amount is not none %}
                        <div class="mb-3">
                            <div class="form-check form-switch custom-switch">
                                {{ register_form.use_last_amount(class="form-check-input", type="checkbox", role="switch", id="useLastAmountSwitch") }}
                                <label class="form-check-label" for="useLastAmountSwitch">
                                    {{ register_form.use_last_amount.label.text }}
                                </label>
                            </div>
                        </div>
                        {% endif %}
                        
                        <div class="mb-3" id="initial_amount_group">
                            <label class="form-label">{{ register_form.initial_amount.label }}</label>
                            <div class="input-group">
                                <span class="input-group-text">€</span>
                                {{ register_form.initial_amount(class="form-control", type="number", step="0.01") }}
                            </div>
                        </div>

                        <div class="mb-3" id="reason_group" style="display: none;">
                            <label class="form-label">{{ register_form.reason.label }}</label>
                            <small class="form-text text-muted d-block">{{ register_form.reason.description }}</small>
                            {{ register_form.reason(class="form-control") }}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">Ouvrir la caisse</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.custom-switch {
    padding-left: 3.25rem;
}
.custom-switch .form-check-input {
    width: 3rem;
    height: 1.5rem;
    margin-left: -3.25rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
    background-position: left center;
    border-radius: 2rem;
    transition: background-position .15s ease-in-out;
}
.custom-switch .form-check-input:checked {
    background-position: right center;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const useLastAmountSwitch = document.querySelector('#useLastAmountSwitch');
    const initialAmountGroup = document.querySelector('#initial_amount_group');
    const initialAmountInput = document.querySelector('#initial_amount');
    const reasonGroup = document.querySelector('#reason_group');
    const lastClosingAmount = {{ last_closing_amount|default(0) }};
    const useLastClosingAmount = {{ 'true' if settings.use_last_closing_amount else 'false' }};
    
    if (useLastAmountSwitch) {
        useLastAmountSwitch.addEventListener('change', function() {
            if (this.checked) {
                initialAmountGroup.style.display = 'none';
                reasonGroup.style.display = 'none';
                initialAmountInput.value = lastClosingAmount;
            } else {
                initialAmountGroup.style.display = 'block';
                checkAmountDifference();
            }
        });
    }
    
    function checkAmountDifference() {
        if (useLastClosingAmount && Math.abs(parseFloat(initialAmountInput.value || 0) - lastClosingAmount) > 0.01) {
            reasonGroup.style.display = 'block';
        } else {
            reasonGroup.style.display = 'none';
        }
    }
    
    initialAmountInput.addEventListener('input', checkAmountDifference);
});
</script>
