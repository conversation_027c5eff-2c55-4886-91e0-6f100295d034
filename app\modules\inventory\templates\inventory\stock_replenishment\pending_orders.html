{% extends "base.html" %}

{% block title %}Commandes en Attente - Pending Marchandise{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
<script src="{{ url_for('inventory.static', filename='js/pending_orders.js') }}"></script>
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-3">
                <h2 class="mb-0">
                    <i class="fas fa-clock text-warning"></i> 
                    Commandes en Attente - Pending Marchandise
                </h2>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-plus"></i> Nouvelle Commande
                    </a>
                    <a href="{{ url_for('inventory.purchase_orders_list') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-list"></i> Toutes les Commandes
                    </a>
                    <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Statistiques rapides -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card bg-warning">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ pending_orders|length }}</h3>
                        <p>Commandes en attente</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-info">
                    <div class="stat-icon">
                        <i class="fas fa-euro-sign"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ "%.2f"|format(total_pending_amount) }} €</h3>
                        <p>Valeur totale</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-primary">
                    <div class="stat-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ total_pending_items }}</h3>
                        <p>Articles en attente</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-success">
                    <div class="stat-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ unique_suppliers }}</h3>
                        <p>Fournisseurs concernés</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtres -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">Fournisseur</label>
                        <select class="form-select" id="supplierFilter">
                            <option value="">Tous les fournisseurs</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Date de commande</label>
                        <input type="date" class="form-control" id="dateFilter">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Statut</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">Tous les statuts</option>
                            <option value="pending">En attente</option>
                            <option value="partial_received">Partiellement reçue</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Actions</label>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary" onclick="applyFilters()">
                                <i class="fas fa-filter"></i> Filtrer
                            </button>
                            <button class="btn btn-outline-secondary" onclick="clearFilters()">
                                <i class="fas fa-times"></i> Effacer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Liste des commandes en attente -->
        <div class="row">
            {% if pending_orders %}
                {% for order in pending_orders %}
                <div class="col-lg-6 mb-4 order-card" 
                     data-supplier="{{ order.supplier_id or 0 }}" 
                     data-date="{{ order.order_date.strftime('%Y-%m-%d') }}"
                     data-status="{{ order.status.value }}">
                    <div class="card border-warning">
                        <div class="card-header bg-warning bg-opacity-10">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-file-alt"></i> 
                                    Commande {{ order.reference }}
                                </h6>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" 
                                               href="{{ url_for('inventory.purchase_order_details', order_id=order.id) }}">
                                                <i class="fas fa-eye"></i> Voir détails
                                            </a>
                                        </li>
                                        <li>
                                            <button class="dropdown-item" onclick="markAsReceived({{ order.id }})">
                                                <i class="fas fa-check"></i> Marquer comme reçue
                                            </button>
                                        </li>
                                        <li>
                                            <button class="dropdown-item" onclick="editOrder({{ order.id }})">
                                                <i class="fas fa-edit"></i> Modifier
                                            </button>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <button class="dropdown-item text-danger" onclick="cancelOrder({{ order.id }})">
                                                <i class="fas fa-times"></i> Annuler
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Informations de base -->
                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>Fournisseur:</strong><br>
                                    <span class="text-primary">{{ order.supplier_name }}</span>
                                </div>
                                <div class="col-6">
                                    <strong>Date de commande:</strong><br>
                                    {{ order.order_date.strftime('%d/%m/%Y') }}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>Montant total:</strong><br>
                                    <span class="h6 text-success">{{ "%.2f"|format(order.total_amount) }} €</span>
                                </div>
                                <div class="col-6">
                                    <strong>Statut:</strong><br>
                                    {% if order.status.value == 'pending' %}
                                        <span class="badge bg-warning">En attente</span>
                                    {% elif order.status.value == 'partial_received' %}
                                        <span class="badge bg-info">Partiellement reçue</span>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Articles de la commande -->
                            <div class="mb-3">
                                <strong>Articles ({{ order.items.count() }}):</strong>
                                <div class="mt-2">
                                    {% for item in order.items[:3] %}
                                    <div class="d-flex justify-content-between align-items-center border-bottom py-1">
                                        <span class="small">{{ item.item_name }}</span>
                                        <span class="small text-muted">{{ item.quantity }} × {{ "%.2f"|format(item.unit_price) }}€</span>
                                    </div>
                                    {% endfor %}
                                    {% if order.items.count() > 3 %}
                                    <div class="text-center mt-2">
                                        <small class="text-muted">... et {{ order.items.count() - 3 }} autre(s) article(s)</small>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Date de livraison prévue -->
                            {% if order.expected_delivery_date %}
                            <div class="mb-3">
                                <strong>Livraison prévue:</strong><br>
                                <span class="text-info">{{ order.expected_delivery_date.strftime('%d/%m/%Y') }}</span>
                                {% if order.expected_delivery_date < now.date() %}
                                    <span class="badge bg-danger ms-2">En retard</span>
                                {% endif %}
                            </div>
                            {% endif %}

                            <!-- Notes -->
                            {% if order.notes %}
                            <div class="mb-3">
                                <strong>Notes:</strong><br>
                                <small class="text-muted">{{ order.notes[:100] }}{% if order.notes|length > 100 %}...{% endif %}</small>
                            </div>
                            {% endif %}

                            <!-- Actions -->
                            <div class="d-flex gap-2">
                                <button class="btn btn-success btn-sm" onclick="markAsReceived({{ order.id }})">
                                    <i class="fas fa-check"></i> Marquer reçue
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="partialReceive({{ order.id }})">
                                    <i class="fas fa-boxes"></i> Réception partielle
                                </button>
                                <a href="{{ url_for('inventory.purchase_order_details', order_id=order.id) }}" 
                                   class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-eye"></i> Détails
                                </a>
                            </div>
                        </div>
                        <div class="card-footer text-muted">
                            <small>
                                Créée le {{ order.created_at.strftime('%d/%m/%Y à %H:%M') }}
                                {% if order.updated_at != order.created_at %}
                                    • Modifiée le {{ order.updated_at.strftime('%d/%m/%Y à %H:%M') }}
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-clipboard-check fa-4x mb-3"></i>
                        <h4>Aucune commande en attente</h4>
                        <p>Toutes vos commandes ont été traitées ou vous n'avez pas encore créé de commandes.</p>
                        <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Créer une nouvelle commande
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal de réception partielle -->
<div class="modal fade" id="partialReceiveModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-boxes"></i> Réception Partielle
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="partialReceiveContent">
                    <!-- Contenu sera chargé dynamiquement -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    Annuler
                </button>
                <button type="button" class="btn btn-success" id="confirmPartialReceiveBtn">
                    <i class="fas fa-check"></i> Confirmer la réception
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Variables globales
let currentOrderId = null;

// Fonctions de filtrage
function applyFilters() {
    const supplierFilter = document.getElementById('supplierFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    const orderCards = document.querySelectorAll('.order-card');
    
    orderCards.forEach(card => {
        let show = true;
        
        if (supplierFilter && card.dataset.supplier !== supplierFilter) {
            show = false;
        }
        
        if (dateFilter && card.dataset.date !== dateFilter) {
            show = false;
        }
        
        if (statusFilter && card.dataset.status !== statusFilter) {
            show = false;
        }
        
        card.style.display = show ? 'block' : 'none';
    });
}

function clearFilters() {
    document.getElementById('supplierFilter').value = '';
    document.getElementById('dateFilter').value = '';
    document.getElementById('statusFilter').value = '';
    
    document.querySelectorAll('.order-card').forEach(card => {
        card.style.display = 'block';
    });
}

// Fonctions d'action sur les commandes
function markAsReceived(orderId) {
    if (confirm('Êtes-vous sûr de vouloir marquer cette commande comme entièrement reçue ?')) {
        fetch(`/inventory/stock-replenishment/orders/${orderId}/receive`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Commande marquée comme reçue et stock mis à jour');
                location.reload();
            } else {
                alert('Erreur: ' + (data.error || 'Erreur inconnue'));
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur de connexion');
        });
    }
}

function partialReceive(orderId) {
    currentOrderId = orderId;
    
    // Charger les détails de la commande pour la réception partielle
    fetch(`/inventory/api/purchase-order/${orderId}/items`)
        .then(response => response.json())
        .then(data => {
            const content = document.getElementById('partialReceiveContent');
            content.innerHTML = `
                <div class="alert alert-info">
                    <h6>Commande ${data.reference}</h6>
                    <p>Sélectionnez les quantités reçues pour chaque article :</p>
                </div>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Article</th>
                                <th>Commandé</th>
                                <th>Déjà reçu</th>
                                <th>Recevoir maintenant</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.items.map(item => `
                                <tr>
                                    <td>${item.name}</td>
                                    <td>${item.quantity}</td>
                                    <td>${item.received_quantity || 0}</td>
                                    <td>
                                        <input type="number" class="form-control receive-quantity" 
                                               data-item-id="${item.id}"
                                               min="0" 
                                               max="${item.quantity - (item.received_quantity || 0)}"
                                               step="0.01"
                                               value="${item.quantity - (item.received_quantity || 0)}">
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            const modal = new bootstrap.Modal(document.getElementById('partialReceiveModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors du chargement des détails');
        });
}

function cancelOrder(orderId) {
    if (confirm('Êtes-vous sûr de vouloir annuler cette commande ?')) {
        fetch(`/inventory/stock-replenishment/orders/${orderId}/cancel`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Commande annulée');
                location.reload();
            } else {
                alert('Erreur: ' + (data.error || 'Erreur inconnue'));
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur de connexion');
        });
    }
}

// Confirmation de réception partielle
document.getElementById('confirmPartialReceiveBtn').addEventListener('click', function() {
    const quantities = {};
    document.querySelectorAll('.receive-quantity').forEach(input => {
        const itemId = input.dataset.itemId;
        const quantity = parseFloat(input.value) || 0;
        if (quantity > 0) {
            quantities[itemId] = quantity;
        }
    });
    
    if (Object.keys(quantities).length === 0) {
        alert('Veuillez saisir au moins une quantité à recevoir');
        return;
    }
    
    fetch(`/inventory/stock-replenishment/orders/${currentOrderId}/partial-receive`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ quantities: quantities })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Réception partielle enregistrée et stock mis à jour');
            location.reload();
        } else {
            alert('Erreur: ' + (data.error || 'Erreur inconnue'));
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur de connexion');
    });
});
</script>
{% endblock %}
