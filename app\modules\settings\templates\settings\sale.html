{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-cash-register"></i>
            Paramètres de vente
        </h1>
        <button type="submit" form="saleForm" class="btn btn-primary">
            <i class="fas fa-save"></i>
            Enregistrer
        </button>
    </div>

    <!-- Settings Form -->
    <form id="saleForm" method="POST">
        {{ form.hidden_tag() }}

        <div class="row">
            <!-- Sale Settings -->
            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-cog"></i>
                            Options de vente
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.allow_decimal_quantity(class="form-check-input") }}
                                {{ form.allow_decimal_quantity.label(class="form-check-label") }}
                            </div>
                            {% for error in form.allow_decimal_quantity.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                            <small class="text-muted">Autoriser les quantités décimales lors de la vente</small>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.allow_price_change(class="form-check-input") }}
                                {{ form.allow_price_change.label(class="form-check-label") }}
                            </div>
                            {% for error in form.allow_price_change.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                            <small class="text-muted">Permettre la modification des prix lors de la vente</small>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.allow_discount(class="form-check-input") }}
                                {{ form.allow_discount.label(class="form-check-label") }}
                            </div>
                            {% for error in form.allow_discount.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                            <small class="text-muted">Autoriser les remises sur les ventes</small>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.require_customer_info(class="form-check-input") }}
                                {{ form.require_customer_info.label(class="form-check-label") }}
                            </div>
                            {% for error in form.require_customer_info.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                            <small class="text-muted">Exiger les informations client pour chaque vente</small>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.require_table_selection(class="form-check-input") }}
                                {{ form.require_table_selection.label(class="form-check-label") }}
                            </div>
                            {% for error in form.require_table_selection.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                            <small class="text-muted">Exiger la sélection d'une table pour chaque vente</small>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.enable_kitchen_print(class="form-check-input") }}
                                {{ form.enable_kitchen_print.label(class="form-check-label") }}
                            </div>
                            {% for error in form.enable_kitchen_print.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                            <small class="text-muted">Activer l'impression des commandes en cuisine</small>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.enable_low_stock_alert(class="form-check-input") }}
                                {{ form.enable_low_stock_alert.label(class="form-check-label") }}
                            </div>
                            {% for error in form.enable_low_stock_alert.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                            <small class="text-muted">Afficher une alerte lorsque le stock est bas</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %} 