from flask import render_template, flash, redirect, url_for, request
from flask_login import login_required, current_user
from app import db
from app.modules.auth.models import User, UserRole
from app.modules.settings.models_settings import Settings
from app.utils.decorators import system_admin_required
from . import bp
from flask_wtf import FlaskForm

@bp.route('/system/users')
@login_required
@system_admin_required
def system_users():
    """Liste des utilisateurs (vue admin système)"""
    form = FlaskForm()  # Formulaire vide pour le CSRF
    owners = User.query.filter_by(role=UserRole.OWNER).all()
    system_users = User.query.filter_by(role=UserRole.SYSTEM_ADMIN).all()
    
    # Statistiques
    total_owners = len(owners)
    active_owners = len([u for u in owners if u.is_active])
    total_system_users = len(system_users)
    
    # Pour chaque owner, récupérer ses utilisateurs
    owners_with_users = []
    for owner in owners:
        owner_users = User.query.filter_by(created_by_id=owner.id).all()
        owners_with_users.append({
            'owner': owner,
            'users': owner_users,
            'total_users': len(owner_users),
            'active_users': len([u for u in owner_users if u.is_active])
        })
    
    return render_template('admin/system/users.html',
                         title='Gestion des Utilisateurs Système',
                         owners_with_users=owners_with_users,
                         system_users=system_users,
                         total_owners=total_owners,
                         active_owners=active_owners,
                         total_system_users=total_system_users,
                         roles=UserRole,
                         form=form)

@bp.route('/system/user/create', methods=['POST'])
@login_required
@system_admin_required
def create_system_user():
    """Créer un nouvel utilisateur système"""
    username = request.form.get('username')
    email = request.form.get('email')
    password = request.form.get('password')
    role = request.form.get('role')
    
    if role not in [UserRole.SYSTEM_ADMIN.value, UserRole.OWNER.value]:
        flash('Rôle non autorisé.', 'error')
        return redirect(url_for('admin.system_users'))
    
    if User.query.filter_by(username=username).first():
        flash('Ce nom d\'utilisateur est déjà utilisé.', 'error')
        return redirect(url_for('admin.system_users'))
    
    if User.query.filter_by(email=email).first():
        flash('Cette adresse email est déjà utilisée.', 'error')
        return redirect(url_for('admin.system_users'))
    
    user = User(
        username=username,
        email=email,
        role=UserRole(role),
        is_active=True,
        created_by_id=current_user.id
    )
    user.set_password(password)
    
    db.session.add(user)
    db.session.commit()
    
    # Si c'est un owner, créer ses paramètres par défaut
    if role == UserRole.OWNER.value:
        settings = Settings(owner_id=user.id)
        db.session.add(settings)
        db.session.commit()
    
    flash('Utilisateur créé avec succès.', 'success')
    return redirect(url_for('admin.system_users'))

@bp.route('/system/user/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@system_admin_required
def edit_system_user(id):
    """Modifier un utilisateur système"""
    user = User.query.get_or_404(id)
    if user.role not in [UserRole.SYSTEM_ADMIN, UserRole.OWNER]:
        flash('Utilisateur non autorisé.', 'error')
        return redirect(url_for('admin.system_users'))
    
    form = FlaskForm()  # Formulaire pour le CSRF
    
    if request.method == 'POST':
        if form.validate_on_submit():
            username = request.form.get('username')
            email = request.form.get('email')
            password = request.form.get('password')
            role = request.form.get('role')
            is_active = request.form.get('is_active') == 'on'
            
            if role not in [UserRole.SYSTEM_ADMIN.value, UserRole.OWNER.value]:
                flash('Rôle non autorisé.', 'error')
                return redirect(url_for('admin.edit_system_user', id=id))
            
            # Vérifier si le nom d'utilisateur est déjà utilisé par un autre utilisateur
            existing_user = User.query.filter_by(username=username).first()
            if existing_user and existing_user.id != id:
                flash('Ce nom d\'utilisateur est déjà utilisé.', 'error')
                return redirect(url_for('admin.edit_system_user', id=id))
            
            # Vérifier si l'email est déjà utilisé par un autre utilisateur
            existing_user = User.query.filter_by(email=email).first()
            if existing_user and existing_user.id != id:
                flash('Cette adresse email est déjà utilisée.', 'error')
                return redirect(url_for('admin.edit_system_user', id=id))
            
            user.username = username
            user.email = email
            if password:  # Ne changer le mot de passe que s'il est fourni
                user.set_password(password)
            
            # Si on change le rôle de OWNER à SYSTEM_ADMIN, vérifier qu'il n'y a pas de données liées
            if user.role == UserRole.OWNER and role == UserRole.SYSTEM_ADMIN.value:
                # TODO: Implémenter la vérification des données liées
                pass
            
            user.role = UserRole(role)
            user.is_active = is_active
            
            db.session.commit()
            
            flash('Utilisateur modifié avec succès.', 'success')
            return redirect(url_for('admin.system_users'))
    
    return render_template('admin/system/edit_user.html',
                         title='Modifier l\'utilisateur système',
                         user=user,
                         roles=[UserRole.SYSTEM_ADMIN, UserRole.OWNER],
                         form=form)

@bp.route('/system/user/<int:id>/delete', methods=['POST'])
@login_required
@system_admin_required
def delete_system_user(id):
    """Supprimer un utilisateur système"""
    user = User.query.get_or_404(id)
    if user.role not in [UserRole.SYSTEM_ADMIN, UserRole.OWNER]:
        flash('Utilisateur non autorisé.', 'error')
        return redirect(url_for('admin.system_users'))
    
    # Prevent deleting the last system admin
    if user.role == UserRole.SYSTEM_ADMIN and User.query.filter_by(role=UserRole.SYSTEM_ADMIN).count() <= 1:
        flash('Impossible de supprimer le dernier administrateur système.', 'error')
        return redirect(url_for('admin.system_users'))
    
    # Prevent self-deletion
    if user.id == current_user.id:
        flash('Vous ne pouvez pas supprimer votre propre compte.', 'error')
        return redirect(url_for('admin.system_users'))
    
    # Si c'est un owner, supprimer d'abord ses paramètres
    if user.role == UserRole.OWNER:
        settings = Settings.query.filter_by(owner_id=user.id).first()
        if settings:
            db.session.delete(settings)
    
    db.session.delete(user)
    db.session.commit()
    
    flash('Utilisateur supprimé avec succès.', 'success')
    return redirect(url_for('admin.system_users'))
