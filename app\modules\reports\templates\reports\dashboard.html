{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-line"></i>
            Tableau de bord des rapports
        </h1>
        <div>
            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#exportModal">
                <i class="fas fa-file-export"></i>
                Exporter
            </button>
            <div class="btn-group ms-2">
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-calendar"></i>
                    {{ period }}
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="{{ url_for('reports.dashboard', period='today') }}">Aujourd'hui</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('reports.dashboard', period='week') }}">Cette semaine</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('reports.dashboard', period='month') }}">Ce mois</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('reports.dashboard', period='year') }}">Cette année</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#customPeriodModal">
                            Période personnalisée
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row">
        <!-- Revenue Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Chiffre d'affaires
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ "%.2f"|format(total_revenue) }} €
                            </div>
                            <div class="text-xs text-muted mt-2">
                                {% if revenue_change > 0 %}
                                <i class="fas fa-arrow-up text-success"></i>
                                <span class="text-success">+{{ "%.1f"|format(revenue_change) }}%</span>
                                {% elif revenue_change < 0 %}
                                <i class="fas fa-arrow-down text-danger"></i>
                                <span class="text-danger">{{ "%.1f"|format(revenue_change) }}%</span>
                                {% else %}
                                <i class="fas fa-equals text-muted"></i>
                                <span>0%</span>
                                {% endif %}
                                vs période précédente
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-euro-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Orders Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Commandes
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_orders }}
                            </div>
                            <div class="text-xs text-muted mt-2">
                                Panier moyen: {{ "%.2f"|format(average_order) }} €
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profit Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Marge brute
                            </div>
                            <div class="row no-gutters align-items-center">
                                <div class="col-auto">
                                    <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800">
                                        {{ "%.2f"|format(gross_profit) }} €
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="progress progress-sm mr-2">
                                        <div class="progress-bar bg-info" role="progressbar" 
                                             style="width: {{ profit_margin }}%" 
                                             aria-valuenow="{{ profit_margin }}" 
                                             aria-valuemin="0" 
                                             aria-valuemax="100">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-xs text-muted mt-2">
                                Taux de marge: {{ "%.1f"|format(profit_margin) }}%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percent fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Expenses Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Dépenses
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ "%.2f"|format(total_expenses) }} €
                            </div>
                            <div class="text-xs text-muted mt-2">
                                {{ expenses_count }} transaction(s)
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-wallet fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <!-- Revenue Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Évolution du chiffre d'affaires</h6>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary active" data-chart-view="daily">Jour</button>
                        <button type="button" class="btn btn-outline-primary" data-chart-view="weekly">Semaine</button>
                        <button type="button" class="btn btn-outline-primary" data-chart-view="monthly">Mois</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
            </div>
            <!-- Commandes récentes POS -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Commandes récentes POS</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Date</th>
                                    <th>Produits</th>
                                    <th>Total</th>
                                    <th>Statut</th>
                                    <th>Type</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_pos_orders %}
                                <tr>
                                    <td>#{{ order.id }}</td>
                                    <td>{{ order.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                                    <td>
                                        {% for item in order.items %}
                                        {{ item.quantity }}x {{ item.product.name }}{% if not loop.last %}, {% endif %}
                                        {% endfor %}
                                    </td>
                                    <td>{{ "%.2f"|format(order.total) }} €</td>
                                    <td>
                                        {% if order.status.value == 'PAID' or order.status.value == 'COMPLETED' %}
                                        <span class="badge bg-success" data-bs-toggle="tooltip" title="Terminée">Terminée</span>
                                        {% elif order.status.value == 'PENDING' %}
                                        <span class="badge bg-warning text-dark" data-bs-toggle="tooltip" title="En cours">En cours</span>
                                        {% elif order.status.value == 'CANCELLED' %}
                                        <span class="badge bg-danger" data-bs-toggle="tooltip" title="Annulée">Annulée</span>
                                        {% else %}
                                        <span class="badge bg-primary" data-bs-toggle="tooltip" title="{{ order.status.value }}">{{ order.status.value }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info" data-bs-toggle="tooltip" title="POS"><i class="fas fa-desktop"></i> POS</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!-- Commandes récentes en ligne -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Commandes récentes en ligne</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Date</th>
                                    <th>Produits</th>
                                    <th>Total</th>
                                    <th>Statut</th>
                                    <th>Type</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_online_orders %}
                                <tr>
                                    <td>#{{ order.id }}</td>
                                    <td>{{ order.ordered_at.strftime('%d/%m/%Y %H:%M') }}</td>
                                    <td>
                                        {% for item in order.items %}
                                        {{ item.quantity }}x {{ item.product.name }}{% if not loop.last %}, {% endif %}
                                        {% endfor %}
                                    </td>
                                    <td>{{ "%.2f"|format(order.total_amount) }} €</td>
                                    <td>
                                        {% if order.payment_status.value == 'PAID' %}
                                        <span class="badge bg-success" data-bs-toggle="tooltip" title="Payée">Payée</span>
                                        {% elif order.payment_status.value == 'PENDING' %}
                                        <span class="badge bg-warning text-dark" data-bs-toggle="tooltip" title="En attente">En attente</span>
                                        {% elif order.payment_status.value == 'CANCELLED' %}
                                        <span class="badge bg-danger" data-bs-toggle="tooltip" title="Annulée">Annulée</span>
                                        {% else %}
                                        <span class="badge bg-primary" data-bs-toggle="tooltip" title="{{ order.payment_status.value }}">{{ order.payment_status.value }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark" data-bs-toggle="tooltip" title="En ligne"><i class="fas fa-globe"></i> En ligne</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- Products Chart + Répartition POS/Online -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Produits</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie mb-4">
                        <canvas id="productsChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        {% for product in top_products %}
                        <span class="mr-2">
                            <i class="fas fa-circle" style="color: {{ product.color }}"></i>
                            {{ product.name }}
                        </span>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Répartition ventes POS vs Online</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie">
                        <canvas id="salesSourceChart"></canvas>
                    </div>
                    <div class="mt-3 text-center small">
                        <span class="badge bg-info"><i class="fas fa-desktop"></i> POS</span>
                        <span class="badge bg-warning text-dark ms-2"><i class="fas fa-globe"></i> Online</span>
                    </div>
                </div>
            </div>
            <!-- Low Stock -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Stock bas</h6>
                </div>
                <div class="card-body">
                    {% if low_stock_items %}
                    <div class="list-group list-group-flush">
                        {% for item in low_stock_items %}
                        <div class="list-group-item px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ item.name }}</h6>
                                    <small class="text-muted">
                                        Stock actuel: {{ item.get_available_quantity() }} {{ item.unit }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <div class="small text-danger">
                                        Min: {{ item.minimum_stock }} {{ item.unit }}
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                            data-bs-toggle="modal" 
                                            data-bs-target="#quickStockModal"
                                            data-item-id="{{ item.id }}"
                                            data-item-name="{{ item.name }}"
                                            data-item-unit="{{ item.unit }}">
                                        <i class="fas fa-plus"></i>
                                        Réappro
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <p>Tous les stocks sont à un niveau satisfaisant.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div class="modal fade" id="exportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Exporter les rapports</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">                <form id="exportForm" method="GET">
                    <input type="hidden" name="start_date" value="{{ start_date }}">
                    <input type="hidden" name="end_date" value="{{ end_date }}">
                    
                    <div class="mb-3">
                        <label class="form-label">Type de rapport</label>
                        <select name="report_type" class="form-select" id="reportType">
                            <option value="sales">Rapport des ventes</option>
                            <option value="profit_loss">Rapport profits et pertes</option>
                            <option value="inventory">Rapport d'inventaire</option>
                            <option value="products">Rapport des produits</option>
                            <option value="expenses">Rapport des dépenses</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Format</label>
                        <select name="format" class="form-select">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Sections à inclure</label>
                        <div class="form-check">
                            <input type="checkbox" name="sections" value="summary" class="form-check-input" checked>
                            <label class="form-check-label">Résumé</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" name="sections" value="sales" class="form-check-input" checked>
                            <label class="form-check-label">Ventes détaillées</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" name="sections" value="products" class="form-check-input" checked>
                            <label class="form-check-label">Analyse produits</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" name="sections" value="expenses" class="form-check-input" checked>
                            <label class="form-check-label">Dépenses</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="submit" form="exportForm" class="btn btn-primary">
                    <i class="fas fa-file-export"></i>
                    Exporter
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Custom Period Modal -->
<div class="modal fade" id="customPeriodModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Période personnalisée</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="periodForm" method="GET">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Date de début</label>
                                <input type="date" name="start_date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Date de fin</label>
                                <input type="date" name="end_date" class="form-control" required>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="submit" form="periodForm" class="btn btn-primary">Appliquer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    const revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: {{ revenue_dates|tojson }},
            datasets: [{
                label: 'Chiffre d\'affaires',
                data: {{ revenue_data|tojson }},
                borderColor: 'rgb(78, 115, 223)',
                backgroundColor: 'rgba(78, 115, 223, 0.05)',
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value + ' €';
                        }
                    }
                }
            }
        }
    });

    // Products Chart
    const productsCtx = document.getElementById('productsChart').getContext('2d');
    const productsChart = new Chart(productsCtx, {
        type: 'doughnut',
        data: {
            labels: {{ product_labels|tojson }},
            datasets: [{
                data: {{ product_data|tojson }},
                backgroundColor: {{ product_colors|tojson }},
                hoverOffset: 4
            }]
        },
        options: {
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Répartition ventes POS vs Online
    var ctx3 = document.getElementById('salesSourceChart').getContext('2d');
    new Chart(ctx3, {
        type: 'doughnut',
        data: {
            labels: ['POS', 'Online'],
            datasets: [{
                data: [{{ pos_revenue|default(0) }}, {{ online_revenue|default(0) }}],
                backgroundColor: ['rgba(78, 115, 223, 0.8)', 'rgba(246, 194, 62, 0.8)'],
                hoverOffset: 4
            }]
        },
        options: {
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let value = context.raw;
                            return value.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' });
                        }
                    }
                }
            }
        }
    });

    // Chart view buttons
    document.querySelectorAll('[data-chart-view]').forEach(button => {
        button.addEventListener('click', function() {
            const view = this.dataset.chartView;
            
            // Update active state
            document.querySelectorAll('[data-chart-view]').forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');

            // Update chart data
            fetch(`/reports/revenue-data?view=${view}&start_date={{ start_date }}&end_date={{ end_date }}`)
                .then(response => response.json())
                .then(data => {
                    revenueChart.data.labels = data.labels;
                    revenueChart.data.datasets[0].data = data.values;
                    revenueChart.update();
                });
        });
    });

    // Custom period form
    const periodForm = document.getElementById('periodForm');
    periodForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        const params = new URLSearchParams(formData);
        window.location.href = `{{ url_for('reports.dashboard') }}?${params.toString()}`;    });

    // Handle report type change
    const exportForm = document.getElementById('exportForm');
    const reportTypeSelect = document.getElementById('reportType');
    const formatSelect = document.querySelector('select[name="format"]');
    
    function updateExportAction() {
        const reportType = reportTypeSelect.value;
        const format = formatSelect.value;
        let baseUrl = '';
        
        switch(reportType) {
            case 'sales':
                baseUrl = "{{ url_for('reports.export_sales') }}";
                break;
            case 'profit_loss':
                baseUrl = "{{ url_for('reports.export_profit_loss') }}";
                break;
            case 'inventory':
                baseUrl = "{{ url_for('reports.export_inventory') }}";
                break;
            case 'products':
                baseUrl = "{{ url_for('reports.export_products') }}";
                break;
            case 'expenses':
                baseUrl = "{{ url_for('reports.export_expenses') }}";
                break;
        }

        // Ajouter le format comme paramètre d'URL
        exportForm.action = `${baseUrl}?format=${format}`;
    }

    // Mettre à jour l'action du formulaire quand le type de rapport change
    reportTypeSelect.addEventListener('change', updateExportAction);
    
    // Mettre à jour l'action du formulaire quand le format change
    formatSelect.addEventListener('change', updateExportAction);

    // Set initial form action
    updateExportAction();
});
</script>
{% endblock %}