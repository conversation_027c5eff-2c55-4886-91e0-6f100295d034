{% extends "online_ordering_sites/base.html" %}

{% block title %}Commande - {{ site.site_name or site.owner.username }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Résumé de la commande -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>
                        Votre commande
                    </h5>
                </div>
                <div class="card-body" id="checkout-cart-items">
                    <!-- Les articles du panier seront chargés ici via JavaScript -->
                    <div class="text-center py-4" id="loading-cart">
                        <i class="fas fa-spinner fa-spin fa-2x text-muted mb-3"></i>
                        <p class="text-muted">Chargement du panier...</p>
                    </div>
                </div>
            </div>

            <!-- Type de commande -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-truck me-2"></i>
                        Type de commande
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% set first_option = true %}
                        {% if site.allow_delivery %}
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="order_type" id="delivery" value="delivery" {% if first_option %}checked{% endif %}>
                                    <label class="form-check-label" for="delivery">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-truck text-primary me-2"></i>
                                            <div>
                                                <strong>Livraison à domicile</strong>
                                                <br>
                                                <small class="text-muted">
                                                    {% if site.delivery_fee > 0 %}
                                                        Frais: {{ "%.2f"|format(site.delivery_fee) }}€
                                                    {% else %}
                                                        Gratuit
                                                    {% endif %}
                                                </small>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                            {% set first_option = false %}
                        {% endif %}

                        {% if site.allow_pickup %}
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="order_type" id="pickup" value="pickup" {% if first_option %}checked{% endif %}>
                                    <label class="form-check-label" for="pickup">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-shopping-bag text-success me-2"></i>
                                            <div>
                                                <strong>À emporter</strong>
                                                <br>
                                                <small class="text-muted">Récupération sur place</small>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                            {% set first_option = false %}
                        {% endif %}

                        {% if site.allow_dine_in %}
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="order_type" id="dine_in" value="dine_in" {% if first_option %}checked{% endif %}>
                                    <label class="form-check-label" for="dine_in">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-chair text-info me-2"></i>
                                            <div>
                                                <strong>Sur place</strong>
                                                <br>
                                                <small class="text-muted">Service à table</small>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                            {% set first_option = false %}
                        {% endif %}

                        {% if site.allow_drive_through %}
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="order_type" id="drive_through" value="drive_through" {% if first_option %}checked{% endif %}>
                                    <label class="form-check-label" for="drive_through">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-car text-warning me-2"></i>
                                            <div>
                                                <strong>Drive</strong>
                                                <br>
                                                <small class="text-muted">Récupération au volant</small>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                            {% set first_option = false %}
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Adresse de livraison (si livraison) -->
            <div class="card shadow mb-4" id="delivery-address-section" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Adresse de livraison
                    </h5>
                </div>
                <div class="card-body">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="delivery_address" id="default_address" value="default" checked>
                        <label class="form-check-label" for="default_address">
                            <strong>Adresse principale</strong>
                            <br>
                            <small class="text-muted">
                                {{ customer.address }}<br>
                                {{ customer.postal_code }} {{ customer.city }}, {{ customer.country }}
                            </small>
                        </label>
                    </div>
                    
                    {% for address in customer.delivery_addresses %}
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="radio" name="delivery_address" id="address_{{ address.id }}" value="{{ address.id }}">
                            <label class="form-check-label" for="address_{{ address.id }}">
                                <strong>{{ address.name }}</strong>
                                <br>
                                <small class="text-muted">
                                    {{ address.address }}<br>
                                    {{ address.postal_code }} {{ address.city }}
                                    {% if address.instructions %}
                                        <br><em>{{ address.instructions }}</em>
                                    {% endif %}
                                </small>
                            </label>
                        </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Méthode de paiement -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>
                        Méthode de paiement
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="cash_on_delivery" value="cash_on_delivery" checked>
                                <label class="form-check-label" for="cash_on_delivery">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-money-bill text-success me-2"></i>
                                        <div>
                                            <strong>Paiement à la livraison</strong>
                                            <br>
                                            <small class="text-muted">Espèces ou carte à la réception</small>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="online_payment" value="online_payment">
                                <label class="form-check-label" for="online_payment">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-credit-card text-primary me-2"></i>
                                        <div>
                                            <strong>Paiement en ligne</strong>
                                            <br>
                                            <small class="text-muted">Carte bancaire sécurisée</small>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notes pour la commande -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-comment me-2"></i>
                        Notes (optionnel)
                    </h5>
                </div>
                <div class="card-body">
                    <textarea class="form-control" id="customer_notes" name="customer_notes" rows="3"
                              placeholder="Instructions spéciales pour votre commande..."></textarea>
                </div>
            </div>
        </div>

        <!-- Résumé des prix -->
        <div class="col-lg-4">
            <div class="card shadow sticky-top" style="top: 20px;">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        Résumé
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Sous-total:</span>
                        <span id="subtotal-amount">0.00€</span>
                    </div>

                    <div class="d-flex justify-content-between mb-2" id="delivery-fee-row" style="display: none;">
                        <span>Frais de livraison:</span>
                        <span id="delivery-fee-amount">{{ "%.2f"|format(delivery_fee) }}€</span>
                    </div>

                    <hr>

                    <div class="d-flex justify-content-between mb-3">
                        <strong>Total:</strong>
                        <strong id="total-amount">0.00€</strong>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success btn-lg" id="place-order-btn">
                            <i class="fas fa-credit-card me-2"></i>
                            Passer la commande
                        </button>
                        <a href="{{ url_for('online_ordering_sites.menu') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Retour au menu
                        </a>
                    </div>
                </div>
                
                <div class="card-footer text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        Paiement sécurisé
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation -->
<div class="modal fade" id="confirmOrderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la commande</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir passer cette commande ?</p>
                <div id="order-summary"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-success" id="confirm-order-btn">
                    <i class="fas fa-check me-1"></i>Confirmer
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Récupérer le panier depuis localStorage
    const cart = JSON.parse(localStorage.getItem('cart_{{ site.id }}') || '[]');

    // Vérifier que le panier n'est pas vide
    if (cart.length === 0) {
        alert('Votre panier est vide');
        window.location.href = '{{ url_for("online_ordering_sites.menu") }}';
        return;
    }

    const orderTypeRadios = document.querySelectorAll('input[name="order_type"]');
    const deliveryAddressSection = document.getElementById('delivery-address-section');
    const deliveryFeeRow = document.getElementById('delivery-fee-row');
    const deliveryFeeAmount = document.getElementById('delivery-fee-amount');
    const totalAmount = document.getElementById('total-amount');
    const subtotalAmount = document.getElementById('subtotal-amount');

    const deliveryFee = {{ delivery_fee }};
    let subtotal = 0;

    // Charger les articles du panier
    loadCartItems();

    function loadCartItems() {
        const cartContainer = document.getElementById('checkout-cart-items');
        const loadingDiv = document.getElementById('loading-cart');

        // Calculer le sous-total
        subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        // Afficher les articles
        let cartHTML = '';
        cart.forEach(item => {
            cartHTML += `
                <div class="row align-items-center border-bottom py-3">
                    <div class="col-md-2">
                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 60px;">
                            <i class="fas fa-utensils text-muted"></i>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-1">${item.productName}</h6>
                        <small class="text-muted">${item.price.toFixed(2)}€ l'unité</small>
                    </div>
                    <div class="col-md-2 text-center">
                        <span class="badge bg-secondary">${item.quantity}</span>
                    </div>
                    <div class="col-md-2 text-end">
                        <strong>${(item.price * item.quantity).toFixed(2)}€</strong>
                    </div>
                </div>
            `;
        });

        cartContainer.innerHTML = cartHTML;
        loadingDiv.style.display = 'none';

        // Mettre à jour les totaux
        updateTotals();
    }
    
    function updateTotals() {
        subtotalAmount.textContent = subtotal.toFixed(2) + '€';

        const checkedRadio = document.querySelector('input[name="order_type"]:checked');
        if (checkedRadio && checkedRadio.value === 'delivery') {
            totalAmount.textContent = (subtotal + deliveryFee).toFixed(2) + '€';
        } else {
            totalAmount.textContent = subtotal.toFixed(2) + '€';
        }
    }

    // Gestion du type de commande
    orderTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'delivery') {
                deliveryAddressSection.style.display = 'block';
                deliveryFeeRow.style.display = 'flex';
            } else {
                deliveryAddressSection.style.display = 'none';
                deliveryFeeRow.style.display = 'none';
            }
            updateTotals();
        });
    });

    // Initialiser l'affichage
    const checkedRadio = document.querySelector('input[name="order_type"]:checked');
    if (checkedRadio && checkedRadio.value === 'delivery') {
        deliveryAddressSection.style.display = 'block';
        deliveryFeeRow.style.display = 'flex';
    }
    
    // Passer la commande
    document.getElementById('place-order-btn').addEventListener('click', function() {
        const orderTypeRadio = document.querySelector('input[name="order_type"]:checked');
        const paymentMethodRadio = document.querySelector('input[name="payment_method"]:checked');

        if (!orderTypeRadio) {
            alert('Veuillez sélectionner un type de commande');
            return;
        }

        if (!paymentMethodRadio) {
            alert('Veuillez sélectionner une méthode de paiement');
            return;
        }

        const orderType = orderTypeRadio.value;
        const paymentMethod = paymentMethodRadio.value;
        const customerNotes = document.getElementById('customer_notes').value;
        let deliveryAddressId = null;

        if (orderType === 'delivery') {
            const addressRadio = document.querySelector('input[name="delivery_address"]:checked');
            if (addressRadio && addressRadio.value !== 'default') {
                deliveryAddressId = addressRadio.value;
            }
        }

        // Afficher le modal de confirmation
        const modal = new bootstrap.Modal(document.getElementById('confirmOrderModal'));

        // Mettre à jour le résumé
        const summary = document.getElementById('order-summary');
        summary.innerHTML = `
            <strong>Type:</strong> ${getOrderTypeLabel(orderType)}<br>
            <strong>Paiement:</strong> ${getPaymentMethodLabel(paymentMethod)}<br>
            <strong>Total:</strong> ${totalAmount.textContent}
        `;

        modal.show();

        // Confirmer la commande
        document.getElementById('confirm-order-btn').onclick = function() {
            placeOrder(orderType, paymentMethod, customerNotes, deliveryAddressId);
        };
    });
    
    function getOrderTypeLabel(type) {
        const labels = {
            'delivery': 'Livraison à domicile',
            'pickup': 'À emporter',
            'dine_in': 'Sur place',
            'drive_through': 'Drive'
        };
        return labels[type] || type;
    }

    function getPaymentMethodLabel(method) {
        const labels = {
            'cash_on_delivery': 'Paiement à la livraison',
            'online_payment': 'Paiement en ligne'
        };
        return labels[method] || method;
    }

    function placeOrder(orderType, paymentMethod, customerNotes, deliveryAddressId) {
        console.log('DEBUG: placeOrder called with:', {orderType, paymentMethod, customerNotes, deliveryAddressId});
        console.log('DEBUG: cart contents:', cart);

        const btn = document.getElementById('confirm-order-btn');
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Traitement...';

        const requestData = {
            order_type: orderType,
            payment_method: paymentMethod,
            customer_notes: customerNotes,
            delivery_address_id: deliveryAddressId,
            cart_items: cart
        };

        console.log('DEBUG: Sending request data:', requestData);

        console.log('DEBUG: URL for place_order:', '{{ url_for("online_ordering_sites.place_order") }}');
        fetch('{{ url_for("online_ordering_sites.place_order") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Vider le panier
                localStorage.removeItem('cart_{{ site.id }}');

                // Rediriger vers une page de confirmation
                window.location.href = '{{ url_for("online_ordering_sites.index") }}?order_success=' + data.order_number;
            } else {
                alert('Erreur: ' + data.message);
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-check me-1"></i>Confirmer';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Une erreur est survenue');
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-check me-1"></i>Confirmer';
        });
    }
});
</script>
{% endblock %}
