/**
 * Gestionnaire d'interface utilisateur pour le panier POS amélioré
 * Intégration avec le système POS existant
 */

// Extension du namespace POS existant
if (typeof POS !== 'undefined') {
    // Sauvegarder les fonctions originales
    POS.originalUpdateCartDisplay = POS.updateCartDisplay;
    POS.originalClearCart = POS.clearCart;

    // Remplacer par nos nouvelles fonctions
    POS.updateCartDisplay = function() {
        updateCartDisplay();
        // Appeler aussi l'ancienne fonction pour la compatibilité
        if (this.originalUpdateCartDisplay) {
            this.originalUpdateCartDisplay();
        }
    };

    POS.clearCart = function() {
        this.cart = [];
        clearCart();
        if (this.originalClearCart) {
            this.originalClearCart();
        }
    };
}

/**
 * Affiche la modal avec le détail du panier
 */
function showCartModal() {
    updateCartDetailModal();
    const modal = new bootstrap.Modal(document.getElementById('cartDetailModal'));
    modal.show();
}

/**
 * Met à jour le contenu de la modal du panier
 */
function updateCartDetailModal() {
    const cartDetailItems = document.getElementById('cartDetailItems');
    const modalCartItemsCount = document.getElementById('modalCartItemsCount');
    const modalCartTotal = document.getElementById('modalCartTotal');

    // Utiliser le panier POS existant
    const cart = POS ? POS.cart : [];
    const cartTotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);

    if (cart.length === 0) {
        cartDetailItems.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Panier vide</h5>
                <p class="text-muted">Ajoutez des produits pour commencer</p>
            </div>
        `;
    } else {
        cartDetailItems.innerHTML = cart.map((item, index) => `
            <div class="cart-detail-item" data-product-id="${item.id}">
                <img src="${item.image || '/static/img/default-product.png'}"
                     alt="${item.name}" class="cart-item-image">
                <div class="cart-item-info">
                    <div class="cart-item-name">${item.name}</div>
                    <div class="cart-item-price">${item.price.toFixed(2)} € × ${item.quantity}</div>
                    <div class="cart-item-subtotal">Sous-total: ${(item.price * item.quantity).toFixed(2)} €</div>
                </div>
                <div class="cart-item-controls">
                    <div class="quantity-controls">
                        <button class="quantity-btn" onclick="updateCartItemQuantity(${index}, ${item.quantity - 1})"
                                ${item.quantity <= 1 ? 'disabled' : ''}>
                            <i class="fas fa-minus"></i>
                        </button>
                        <span class="quantity-display">${item.quantity}</span>
                        <button class="quantity-btn" onclick="updateCartItemQuantity(${index}, ${item.quantity + 1})">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <button class="remove-item-btn" onclick="removeCartItem(${index})" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    modalCartItemsCount.textContent = cart.reduce((total, item) => total + item.quantity, 0);
    modalCartTotal.textContent = cartTotal.toFixed(2) + ' €';
}

/**
 * Met à jour l'affichage compact du panier
 */
function updateCartDisplay() {
    const cartItemsCount = document.getElementById('cartItemsCount');
    const cartTotalPreview = document.getElementById('cartTotalPreview');
    const cartItemsCompact = document.getElementById('cartItemsCompact');

    // Utiliser le panier POS existant
    const cart = POS ? POS.cart : [];
    const cartTotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);

    // Mettre à jour le compteur et le total
    const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
    if (cartItemsCount) cartItemsCount.textContent = totalItems;
    if (cartTotalPreview) cartTotalPreview.textContent = cartTotal.toFixed(2) + ' €';

    // Mettre à jour l'état des boutons
    const hasItems = cart.length > 0;
    const paymentBtn = document.getElementById('paymentBtn');
    const kitchenBtn = document.getElementById('kitchenBtn');
    const holdBtn = document.getElementById('holdBtn');

    if (paymentBtn) paymentBtn.disabled = !hasItems;
    if (kitchenBtn) kitchenBtn.disabled = !hasItems;
    if (holdBtn) holdBtn.disabled = !hasItems;

    // Afficher le contenu compact du panier
    if (cartItemsCompact) {
        if (cart.length === 0) {
            cartItemsCompact.innerHTML = `
                <div class="empty-cart-message">
                    <i class="fas fa-shopping-cart"></i>
                    <p>Panier vide</p>
                    <small>Cliquez sur un produit pour l'ajouter</small>
                </div>
            `;
        } else {
            // Afficher un aperçu compact des premiers articles
            const previewItems = cart.slice(0, 3);
            cartItemsCompact.innerHTML = `
                <div class="cart-preview">
                    ${previewItems.map(item => `
                        <div class="cart-preview-item">
                            <span class="item-name">${item.name}</span>
                            <span class="item-quantity">×${item.quantity}</span>
                        </div>
                    `).join('')}
                    ${cart.length > 3 ? `<div class="more-items">+${cart.length - 3} autre(s)...</div>` : ''}
                </div>
            `;
        }
    }
}

/**
 * Met à jour la quantité d'un article dans le panier
 */
function updateCartItemQuantity(index, newQuantity) {
    if (!POS || !POS.cart[index]) return;

    if (newQuantity <= 0) {
        removeCartItem(index);
        return;
    }

    POS.cart[index].quantity = newQuantity;
    POS.cart[index].total = POS.cart[index].quantity * POS.cart[index].price;
    updateCartDisplay();
    updateCartDetailModal();
}

/**
 * Supprime un article du panier
 */
function removeCartItem(index) {
    if (!POS) return;

    POS.cart.splice(index, 1);
    updateCartDisplay();
    updateCartDetailModal();
}

/**
 * Vide le panier
 */
function clearCart() {
    updateCartDisplay();
}

/**
 * Affiche une animation d'ajout au panier
 */
function showAddToCartAnimation(productName) {
    // Créer une notification temporaire
    const notification = document.createElement('div');
    notification.className = 'cart-notification';
    notification.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <span>${productName} ajouté au panier</span>
    `;

    document.body.appendChild(notification);

    // Animation d'apparition
    setTimeout(() => notification.classList.add('show'), 100);

    // Suppression après 2 secondes
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 2000);
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    // Attendre que POS soit initialisé
    setTimeout(() => {
        updateCartDisplay();

        // Ajouter un observateur pour les changements du panier POS
        if (POS) {
            // Sauvegarder la fonction originale d'ajout au panier
            const originalAddToCart = POS.cart;

            // Intercepter les modifications du panier pour mettre à jour l'affichage
            const observer = new MutationObserver(() => {
                updateCartDisplay();
            });

            // Observer les changements dans le panier (si c'est un élément DOM)
            // Sinon, on se fie aux appels de updateCartDisplay dans POS
        }
    }, 100);
});
