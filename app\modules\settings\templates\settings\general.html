{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-cog"></i>
            Paramètres généraux
        </h1>
        <button type="submit" form="settingsForm" class="btn btn-primary">
            <i class="fas fa-save"></i>
            Enregistrer
        </button>
    </div>

    <!-- Settings Form -->
    <form id="settingsForm" method="POST" enctype="multipart/form-data">
        {{ form.hidden_tag() }}

        <div class="row">
            <!-- Business Information -->
            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-building"></i>
                            Informations de l'entreprise
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            {{ form.business_name.label(class="form-label") }}
                            {{ form.business_name(class="form-control" + (" is-invalid" if form.business_name.errors else "")) }}
                            {% for error in form.business_name.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.business_legal_name.label(class="form-label") }}
                            {{ form.business_legal_name(class="form-control" + (" is-invalid" if form.business_legal_name.errors else "")) }}
                            {% for error in form.business_legal_name.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.business_type.label(class="form-label") }}
                            {{ form.business_type(class="form-select" + (" is-invalid" if form.business_type.errors else "")) }}
                            {% for error in form.business_type.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.tax_id.label(class="form-label") }}
                            {{ form.tax_id(class="form-control" + (" is-invalid" if form.tax_id.errors else "")) }}
                            {% for error in form.tax_id.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.phone.label(class="form-label") }}
                            {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                            {% for error in form.phone.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            {% for error in form.email.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.website.label(class="form-label") }}
                            {{ form.website(class="form-control" + (" is-invalid" if form.website.errors else "")) }}
                            {% for error in form.website.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.address.label(class="form-label") }}
                            {{ form.address(class="form-control" + (" is-invalid" if form.address.errors else "")) }}
                            {% for error in form.address.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                {{ form.postal_code.label(class="form-label") }}
                                {{ form.postal_code(class="form-control" + (" is-invalid" if form.postal_code.errors else "")) }}
                                {% for error in form.postal_code.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="col-md-4 mb-3">
                                {{ form.city.label(class="form-label") }}
                                {{ form.city(class="form-control" + (" is-invalid" if form.city.errors else "")) }}
                                {% for error in form.city.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="col-md-4 mb-3">
                                {{ form.country.label(class="form-label") }}
                                {{ form.country(class="form-select" + (" is-invalid" if form.country.errors else "")) }}
                                {% for error in form.country.errors %}
                                    <div class="invalid-feedback">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.logo.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.logo(class="form-control" + (" is-invalid" if form.logo.errors else ""), accept="image/*") }}
                                {% if settings.logo_path %}
                                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteLogoModal">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                            <small class="text-muted">Formats acceptés: JPG, PNG, GIF</small>
                            {% for error in form.logo.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                            
                            {% if settings.logo_path %}
                            <div class="mt-2">
                                <img src="{{ url_for('static', filename=settings.logo_path) }}" 
                                     class="img-thumbnail" 
                                     alt="{{ settings.business_name }}"
                                     style="max-height: 100px;">
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Settings -->
            <div class="col-lg-6">
                <!-- Localization -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-globe"></i>
                            Localisation
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            {{ form.language.label(class="form-label") }}
                            {{ form.language(class="form-select" + (" is-invalid" if form.language.errors else "")) }}
                            {% for error in form.language.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.timezone.label(class="form-label") }}
                            {{ form.timezone(class="form-select" + (" is-invalid" if form.timezone.errors else "")) }}
                            {% for error in form.timezone.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.currency.label(class="form-label") }}
                            {{ form.currency(class="form-select" + (" is-invalid" if form.currency.errors else "")) }}
                            {% for error in form.currency.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.date_format.label(class="form-label") }}
                            {{ form.date_format(class="form-select" + (" is-invalid" if form.date_format.errors else "")) }}
                            {% for error in form.date_format.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                            <small class="text-muted">
                                Exemple: {{ now.strftime(form.date_format.data) }}
                            </small>
                        </div>

                        <div class="mb-3">
                            {{ form.time_format.label(class="form-label") }}
                            {{ form.time_format(class="form-select" + (" is-invalid" if form.time_format.errors else "")) }}
                            {% for error in form.time_format.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                            <small class="text-muted">
                                Exemple: {{ now.strftime(form.time_format.data) }}
                            </small>
                        </div>

                        <div class="mb-3">
                            {{ form.first_day_of_week.label(class="form-label") }}
                            {{ form.first_day_of_week(class="form-select" + (" is-invalid" if form.first_day_of_week.errors else "")) }}
                            {% for error in form.first_day_of_week.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- System -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-sliders-h"></i>
                            Système
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            {{ form.default_tax_rate.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.default_tax_rate(class="form-control" + (" is-invalid" if form.default_tax_rate.errors else "")) }}
                                <span class="input-group-text">%</span>
                            </div>
                            {% for error in form.default_tax_rate.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.low_stock_threshold.label(class="form-label") }}
                            {{ form.low_stock_threshold(class="form-control" + (" is-invalid" if form.low_stock_threshold.errors else "")) }}
                            {% for error in form.low_stock_threshold.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.session_timeout.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.session_timeout(class="form-control" + (" is-invalid" if form.session_timeout.errors else "")) }}
                                <span class="input-group-text">minutes</span>
                            </div>
                            {% for error in form.session_timeout.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.backup_enabled.label(class="form-label") }}
                            <div>
                                <div class="form-check form-switch">
                                    {{ form.backup_enabled(class="form-check-input") }}
                                    <label class="form-check-label" for="backup_enabled">
                                        Activer les sauvegardes automatiques
                                    </label>
                                </div>
                            </div>
                            {% for error in form.backup_enabled.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.backup_frequency.label(class="form-label") }}
                            {{ form.backup_frequency(class="form-select" + (" is-invalid" if form.backup_frequency.errors else "")) }}
                            {% for error in form.backup_frequency.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <div class="mb-3">
                            {{ form.backup_retention.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.backup_retention(class="form-control" + (" is-invalid" if form.backup_retention.errors else "")) }}
                                <span class="input-group-text">jours</span>
                            </div>
                            {% for error in form.backup_retention.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Delete Logo Modal -->
{% if settings.logo_path %}
<div class="modal fade" id="deleteLogoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le logo ?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form action="{{ url_for('settings.delete_logo') }}" method="POST">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i>
                        Supprimer le logo
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Logo preview
    const logoInput = document.querySelector('input[type="file"]');
    logoInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.createElement('img');
                preview.src = e.target.result;
                preview.className = 'img-thumbnail mt-2';
                preview.style.maxHeight = '100px';
                
                const existingPreview = logoInput.parentElement.nextElementSibling.nextElementSibling;
                if (existingPreview && existingPreview.tagName === 'DIV') {
                    existingPreview.replaceWith(preview);
                } else {
                    logoInput.parentElement.nextElementSibling.after(preview);
                }
            }
            reader.readAsDataURL(this.files[0]);
        }
    });

    // Toggle backup settings
    const backupEnabled = document.getElementById('backup_enabled');
    const backupFrequency = document.getElementById('backup_frequency');
    const backupRetention = document.getElementById('backup_retention');

    function toggleBackupSettings() {
        backupFrequency.disabled = !backupEnabled.checked;
        backupRetention.disabled = !backupEnabled.checked;
    }

    backupEnabled.addEventListener('change', toggleBackupSettings);
    toggleBackupSettings();
});
</script>
{% endblock %}
{% endblock %} 