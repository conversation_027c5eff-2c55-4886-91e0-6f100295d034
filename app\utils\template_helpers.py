from flask import current_app
from flask_login import current_user
from app.modules.cash_register.models_cash_register import CashRegister

def format_currency(amount, currency='€'):
    """Format un montant en devise"""
    if amount is None:
        return f"0,00 {currency}"
    return f"{amount:,.2f} {currency}".replace(",", " ").replace(".", ",")

def get_cash_register():
    """Récupère la caisse de l'utilisateur courant"""
    if current_user.is_authenticated:
        return CashRegister.query.filter_by(owner_id=current_user.id).first()
    return None

def can_access_cash_register():
    """Vérifie si l'utilisateur peut accéder à la caisse"""
    return current_user.is_authenticated and current_user.has_permission('can_access_cash_register')

def can_manage_cash_register():
    """Vérifie si l'utilisateur peut gérer la caisse"""
    return current_user.is_authenticated and current_user.has_permission('can_manage_cash_register')
