{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
.product-form-container {
    max-width: 1000px;
    margin: 0 auto;
}

.image-upload-container {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-upload-container:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.current-image {
    margin-bottom: 15px;
}

.current-image img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    margin-bottom: 20px;
}

.form-section-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.custom-file-input {
    display: none;
}

.upload-btn {
    display: inline-block;
    padding: 8px 16px;
    background-color: #007bff;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-btn:hover {
    background-color: #0056b3;
}

.upload-text {
    margin-top: 10px;
    color: #666;
}
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="product-form-container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.csrf_token }}
                    
                    <div class="row">
                        <!-- Section Image -->
                        <div class="col-md-4">
                            <div class="form-section">
                                <div class="form-section-title">Image du produit</div>
                                <div class="image-upload-container" onclick="document.getElementById('image').click()">
                                    <div class="current-image">
                                        {% if product and product.image_path %}
                                        <img src="{{ url_for('static', filename=product.image_path) }}" 
                                             alt="{{ product.name }}"
                                             id="preview-image">
                                        {% else %}
                                        <img src="{{ url_for('static', filename='img/default-product.png') }}"
                                             alt="Image par défaut"
                                             id="preview-image">
                                        {% endif %}
                                    </div>
                                    <label class="upload-btn">
                                        <i class="fas fa-upload"></i> Choisir une image
                                        {{ form.image(class="custom-file-input", accept="image/*") }}
                                    </label>
                                    <div class="upload-text">
                                        Formats acceptés: PNG, JPG, JPEG, GIF<br>
                                        Taille maximale: 5 MB
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Informations -->
                        <div class="col-md-8">
                            <div class="form-section">
                                <div class="form-section-title">Informations générales</div>
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            {{ form.name.label(class="form-label") }}
                                            {{ form.name(class="form-control", placeholder="Nom du produit") }}
                                            {% if form.name.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.name.errors %}
                                                <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form.category_id.label(class="form-label") }}
                                            {{ form.category_id(class="form-select") }}
                                            {% if form.category_id.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.category_id.errors %}
                                                <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.price.label(class="form-label") }}
                                            <div class="input-group">
                                                {{ form.price(class="form-control", placeholder="0.00") }}
                                                <span class="input-group-text">€</span>
                                            </div>
                                            {% if form.price.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.price.errors %}
                                                <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.cost_price.label(class="form-label") }}
                                            <div class="input-group">
                                                {{ form.cost_price(class="form-control", placeholder="0.00") }}
                                                <span class="input-group-text">€</span>
                                            </div>
                                            {% if form.cost_price.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.cost_price.errors %}
                                                <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <div class="form-section-title">Gestion du stock</div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form.stock_quantity.label(class="form-label") }}
                                            {{ form.stock_quantity(class="form-control", placeholder="0") }}
                                            {% if form.stock_quantity.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.stock_quantity.errors %}
                                                <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form.minimum_stock.label(class="form-label") }}
                                            {{ form.minimum_stock(class="form-control", placeholder="0") }}
                                            {% if form.minimum_stock.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.minimum_stock.errors %}
                                                <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form.unit.label(class="form-label") }}
                                            {{ form.unit(class="form-control") }}
                                            {% if form.unit.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.unit.errors %}
                                                <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        {{ form.has_recipe(class="form-check-input") }}
                                        {{ form.has_recipe.label(class="form-check-label") }}
                                    </div>
                                    {% if form.has_recipe.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.has_recipe.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="form-section">
                                <div class="form-section-title">Description</div>
                                <div class="mb-3">
                                    {{ form.description(class="form-control", rows=3, placeholder="Description du produit...") }}
                                    {% if form.description.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.description.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-end mt-3">
                        <a href="{{ url_for('inventory.products') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Annuler
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Prévisualisation de l'image
    const imageInput = document.getElementById('image');
    const previewImage = document.getElementById('preview-image');
    
    imageInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            // Vérifier la taille du fichier (5MB max)
            if (file.size > 5 * 1024 * 1024) {
                alert('Le fichier est trop volumineux. La taille maximale est de 5 MB.');
                this.value = '';
                return;
            }
            
            // Vérifier le type de fichier
            const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!validTypes.includes(file.type)) {
                alert('Type de fichier non valide. Utilisez PNG, JPG, JPEG ou GIF.');
                this.value = '';
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
            }
            reader.readAsDataURL(file);
        }
    });

    // Gestion du champ stock_quantity en fonction de has_recipe
    const hasRecipeCheckbox = document.getElementById('has_recipe');
    const stockQuantityInput = document.getElementById('stock_quantity');
    
    function updateStockQuantityState() {
        stockQuantityInput.disabled = hasRecipeCheckbox.checked;
        if (hasRecipeCheckbox.checked) {
            stockQuantityInput.value = '0';
        }
    }
    
    hasRecipeCheckbox.addEventListener('change', updateStockQuantityState);
    updateStockQuantityState();
});
</script>
{% endblock %} 