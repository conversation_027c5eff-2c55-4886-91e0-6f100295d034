{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-file-invoice-dollar"></i>
            Liste des dépenses
        </h1>
        <div>
            <button type="button" class="btn btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#exportModal">
                <i class="fas fa-file-export"></i>
                Exporter
            </button>
            <a href="{{ url_for('expenses.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Nouvelle dépense
            </a>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row">
        <!-- Total Expenses -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total des dépenses
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ "%.2f"|format(total_amount) }} €
                            </div>
                            <div class="text-xs text-muted mt-2">
                                {{ expenses|length }} transaction(s)
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-euro-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ url_for('expenses.index') }}" class="row align-items-end">
                <div class="col-md-3 mb-3">
                    {{ form.category_id.label(class="form-label") }}
                    {{ form.category_id(class="form-select") }}
                </div>
                <div class="col-md-2 mb-3">
                    {{ form.date_range.label(class="form-label") }}
                    {{ form.date_range(class="form-select") }}
                </div>
                <div id="customDateFields" class="row" style="display: {% if form.date_range.data == 'custom' %}flex{% else %}none{% endif %};">
                    <div class="col-md-6 mb-3">
                        {{ form.start_date.label(class="form-label") }}
                        {{ form.start_date(class="form-control", type="date") }}
                    </div>
                    <div class="col-md-6 mb-3">
                        {{ form.end_date.label(class="form-label") }}
                        {{ form.end_date(class="form-control", type="date") }}
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    {{ form.payment_method.label(class="form-label") }}
                    {{ form.payment_method(class="form-select") }}
                </div>
                <div class="col-md-2 mb-3">
                    {{ form.min_amount.label(class="form-label") }}
                    {{ form.min_amount(class="form-control", type="number", step="0.01") }}
                </div>
                <div class="col-md-2 mb-3">
                    {{ form.max_amount.label(class="form-label") }}
                    {{ form.max_amount(class="form-control", type="number", step="0.01") }}
                </div>
                <div class="col-md-1 mb-3">
                    {{ form.submit(class="btn btn-primary w-100") }}
                </div>
            </form>
        </div>
    </div>

    <!-- Expenses Table -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <a href="{{ url_for('expenses.index', 
                                                   category_id=request.args.get('category_id'),
                                                   date_range=request.args.get('date_range'),
                                                   payment_method=request.args.get('payment_method'),
                                                   min_amount=request.args.get('min_amount'),
                                                   max_amount=request.args.get('max_amount'),
                                                   sort_by='date', 
                                                   sort_order='desc' if sort_by == 'date' and sort_order == 'asc' else 'asc') }}">
                                        Date
                                        {% if sort_by == 'date' %}
                                            <i class="fas fa-sort-{{ 'up' if sort_order == 'asc' else 'down' }}"></i>
                                        {% endif %}
                                    </a>
                                </th>
                                <th>
                                    <a href="{{ url_for('expenses.index', 
                                                       category_id=request.args.get('category_id'),
                                                       date_range=request.args.get('date_range'),
                                                       payment_method=request.args.get('payment_method'),
                                                       min_amount=request.args.get('min_amount'),
                                                       max_amount=request.args.get('max_amount'),
                                                       sort_by='category', 
                                                       sort_order='desc' if sort_by == 'category' and sort_order == 'asc' else 'asc') }}">
                                        Catégorie
                                        {% if sort_by == 'category' %}
                                            <i class="fas fa-sort-{{ 'up' if sort_order == 'asc' else 'down' }}"></i>
                                        {% endif %}
                                    </a>
                                </th>
                                <th>Description</th>
                                <th>Référence</th>
                                <th class="text-end">
                                    <a href="{{ url_for('expenses.index', 
                                                       category_id=request.args.get('category_id'),
                                                       date_range=request.args.get('date_range'),
                                                       payment_method=request.args.get('payment_method'),
                                                       min_amount=request.args.get('min_amount'),
                                                       max_amount=request.args.get('max_amount'),
                                                       sort_by='amount', 
                                                       sort_order='desc' if sort_by == 'amount' and sort_order == 'asc' else 'asc') }}">
                                        Montant
                                        {% if sort_by == 'amount' %}
                                            <i class="fas fa-sort-{{ 'up' if sort_order == 'asc' else 'down' }}"></i>
                                        {% endif %}
                                    </a>
                                </th>
                                <th>Mode de paiement</th>
                                <th>Récurrent</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for expense in expenses %}
                            <tr>
                                <td>{{ expense.date.strftime('%d/%m/%Y') }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if expense.category.image_path %}
                                        <img src="{{ url_for('static', filename=expense.category.image_path) }}" 
                                             class="rounded-circle me-2" 
                                             style="width: 24px; height: 24px; object-fit: cover;"
                                             alt="{{ expense.category.name }}">
                                        {% endif %}
                                        {{ expense.category.name }}
                                    </div>
                                </td>
                                <td>{{ expense.description }}</td>
                                <td>{{ expense.reference or '-' }}</td>
                                <td class="text-end">{{ "%.2f"|format(expense.amount) }} €</td>
                                <td>
                                    {% if expense.payment_method == 'cash' %}
                                    <i class="fas fa-money-bill text-success"></i> Espèces
                                    {% elif expense.payment_method == 'card' %}
                                    <i class="fas fa-credit-card text-primary"></i> Carte
                                    {% elif expense.payment_method == 'transfer' %}
                                    <i class="fas fa-exchange-alt text-info"></i> Virement
                                    {% elif expense.payment_method == 'check' %}
                                    <i class="fas fa-money-check text-warning"></i> Chèque
                                    {% else %}
                                    <i class="fas fa-question-circle text-muted"></i> Autre
                                    {% endif %}
                                </td>
                                <td>
                                    {% if expense.is_recurring %}
                                    <span class="badge bg-info">
                                        {% if expense.recurring_interval == 'monthly' %}
                                        Mensuel (J{{ expense.recurring_day }})
                                        {% elif expense.recurring_interval == 'quarterly' %}
                                        Trimestriel (J{{ expense.recurring_day }})
                                        {% else %}
                                        Annuel (J{{ expense.recurring_day }})
                                        {% endif %}
                                    </span>
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        {% if expense.image_path %}
                                        <a href="{{ url_for('static', filename=expense.image_path) }}" 
                                           class="btn btn-outline-info"
                                           target="_blank"
                                           title="Voir le justificatif">
                                            <i class="fas fa-file"></i>
                                        </a>
                                        {% endif %}
                                        <a href="{{ url_for('expenses.edit', id=expense.id) }}" 
                                           class="btn btn-outline-secondary"
                                           title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ url_for('expenses.delete', id=expense.id) }}" 
                                              method="POST"
                                              class="d-inline"
                                              onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette dépense ?');">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                            <button type="submit" class="btn btn-outline-danger" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        Aucune dépense trouvée
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination Controls -->
                {% if pagination.pages > 1 %}
                <div class="d-flex justify-content-center mt-4">
                    <nav aria-label="Navigation des pages">
                        <ul class="pagination">
                            <!-- Previous Page -->
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('expenses.index', 
                                                                    page=pagination.prev_num,
                                                                    category_id=request.args.get('category_id'),
                                                                    date_range=request.args.get('date_range'),
                                                                    payment_method=request.args.get('payment_method'),
                                                                    min_amount=request.args.get('min_amount'),
                                                                    max_amount=request.args.get('max_amount'),
                                                                    sort_by=sort_by,
                                                                    sort_order=sort_order) }}" aria-label="Précédent">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Précédent">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}
                            
                            <!-- Page Numbers -->
                            {% for page_num in pagination.iter_pages(left_edge=2, right_edge=2, left_current=1, right_current=2) %}
                                {% if page_num %}
                                    {% if page_num == pagination.page %}
                                    <li class="page-item active">
                                        <a class="page-link" href="#">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('expenses.index', 
                                                                            page=page_num,
                                                                            category_id=request.args.get('category_id'),
                                                                            date_range=request.args.get('date_range'),
                                                                            payment_method=request.args.get('payment_method'),
                                                                            min_amount=request.args.get('min_amount'),
                                                                            max_amount=request.args.get('max_amount'),
                                                                            sort_by=sort_by,
                                                                            sort_order=sort_order) }}">{{ page_num }}</a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            <!-- Next Page -->
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('expenses.index', 
                                                                    page=pagination.next_num,
                                                                    category_id=request.args.get('category_id'),
                                                                    date_range=request.args.get('date_range'),
                                                                    payment_method=request.args.get('payment_method'),
                                                                    min_amount=request.args.get('min_amount'),
                                                                    max_amount=request.args.get('max_amount'),
                                                                    sort_by=sort_by,
                                                                    sort_order=sort_order) }}" aria-label="Suivant">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Suivant">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                <div class="text-center text-muted mt-2">
                    Page {{ pagination.page }} sur {{ pagination.pages }} ({{ pagination.total }} dépenses au total)
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Exporter les dépenses</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form action="{{ url_for('expenses.export') }}" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label class="form-label">Format</label>
                        <select name="format" class="form-select">
                            <option value="csv">CSV</option>
                            <option value="excel">Excel</option>
                            <option value="pdf">PDF</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" name="include_images" class="form-check-input" id="include_images">
                            <label class="form-check-label" for="include_images">Inclure les justificatifs</label>
                        </div>
                    </div>

                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-file-export"></i>
                            Exporter
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dateRangeSelect = document.querySelector('select[name="date_range"]');
    const customDateFields = document.getElementById('customDateFields');
    
    // Gestion de l'affichage des champs de dates personnalisées
    if (dateRangeSelect) {
        dateRangeSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                customDateFields.style.display = 'flex';
            } else {
                customDateFields.style.display = 'none';
            }
        });
        
        // Initialisation au chargement de la page
        if (dateRangeSelect.value === 'custom') {
            customDateFields.style.display = 'flex';
        }
    }
    
    // Soumettre le formulaire lorsqu'un tri est sélectionné
    const sortLinks = document.querySelectorAll('th a');
    sortLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Empêcher la navigation par défaut
            e.preventDefault();
            
            // Récupérer l'URL avec les paramètres de tri
            const url = new URL(this.href);
            
            // Mettre à jour les champs cachés pour le tri
            const sortByInput = document.createElement('input');
            sortByInput.type = 'hidden';
            sortByInput.name = 'sort_by';
            sortByInput.value = url.searchParams.get('sort_by');
            
            const sortOrderInput = document.createElement('input');
            sortOrderInput.type = 'hidden';
            sortOrderInput.name = 'sort_order';
            sortOrderInput.value = url.searchParams.get('sort_order');
            
            // Ajouter les champs au formulaire
            const form = document.querySelector('form');
            form.appendChild(sortByInput);
            form.appendChild(sortOrderInput);
            
            // Soumettre le formulaire
            form.submit();
        });
    });
});
</script>
{% endblock %} 