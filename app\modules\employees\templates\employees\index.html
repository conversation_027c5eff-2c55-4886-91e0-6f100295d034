{% extends "employees/base_hr.html" %}

{% block title %}Gestion des Employés{% endblock %}

{% block hr_content %}
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-users me-2"></i>Gestion des Employés
                </h1>
                <div>
                    <a href="{{ url_for('employees.dashboard') }}" class="btn btn-info me-2">
                        <i class="fas fa-chart-dashboard me-1"></i>Tableau de bord
                    </a>
                    <a href="{{ url_for('employees.new') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Nouvel Employé
                    </a>
                </div>
            </div>

            <!-- Formulaire de recherche et filtres -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">Rechercher</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request.args.get('search', '') }}" 
                                   placeholder="Nom, prénom, ID, email...">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Statut</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">Tous</option>
                                <option value="ACTIVE" {{ 'selected' if request.args.get('status') == 'ACTIVE' }}>Actif</option>
                                <option value="INACTIVE" {{ 'selected' if request.args.get('status') == 'INACTIVE' }}>Inactif</option>
                                <option value="SUSPENDED" {{ 'selected' if request.args.get('status') == 'SUSPENDED' }}>Suspendu</option>
                                <option value="TERMINATED" {{ 'selected' if request.args.get('status') == 'TERMINATED' }}>Terminé</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="department" class="form-label">Département</label>
                            <input type="text" class="form-control" id="department" name="department" 
                                   value="{{ request.args.get('department', '') }}" 
                                   placeholder="Département">
                        </div>
                        <div class="col-md-2">
                            <label for="position" class="form-label">Poste</label>
                            <input type="text" class="form-control" id="position" name="position" 
                                   value="{{ request.args.get('position', '') }}" 
                                   placeholder="Poste">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>Rechercher
                            </button>
                            <a href="{{ url_for('employees.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Effacer
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Liste des employés -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        Liste des Employés 
                        <span class="badge bg-secondary">{{ employees.total }} employé(s)</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if employees.items %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Nom Complet</th>
                                    <th>Poste</th>
                                    <th>Département</th>
                                    <th>Statut</th>
                                    <th>Date d'embauche</th>
                                    <th>Contact</th>
                                    <th class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employees.items %}
                                <tr>
                                    <td>
                                        <strong>{{ employee.employee_id }}</strong>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                {{ employee.first_name[0] }}{{ employee.last_name[0] }}
                                            </div>
                                            <div>
                                                <strong>{{ employee.full_name }}</strong>
                                                {% if employee.user %}
                                                <br><small class="text-muted">Utilisateur système</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ employee.position }}</td>
                                    <td>{{ employee.department or '-' }}</td>
                                    <td>
                                        {% if employee.status == 'ACTIVE' %}
                                            <span class="badge bg-success">Actif</span>
                                        {% elif employee.status == 'INACTIVE' %}
                                            <span class="badge bg-secondary">Inactif</span>
                                        {% elif employee.status == 'SUSPENDED' %}
                                            <span class="badge bg-warning">Suspendu</span>
                                        {% elif employee.status == 'TERMINATED' %}
                                            <span class="badge bg-danger">Terminé</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ employee.hire_date.strftime('%d/%m/%Y') }}</td>
                                    <td>
                                        {% if employee.email %}
                                            <div><i class="fas fa-envelope me-1"></i>{{ employee.email }}</div>
                                        {% endif %}
                                        {% if employee.phone %}
                                            <div><i class="fas fa-phone me-1"></i>{{ employee.phone }}</div>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('employees.detail', id=employee.id) }}" 
                                               class="btn btn-sm btn-outline-primary" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('employees.edit', id=employee.id) }}" 
                                               class="btn btn-sm btn-outline-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if employee.is_active %}
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete({{ employee.id }}, '{{ employee.full_name }}')" 
                                                    title="Terminer">
                                                <i class="fas fa-user-times"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun employé trouvé</h5>
                        <p class="text-muted">Commencez par ajouter votre premier employé.</p>
                        <a href="{{ url_for('employees.new') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Ajouter un employé
                        </a>
                    </div>
                    {% endif %}
                </div>
                
                {% if employees.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="Navigation des employés">
                        <ul class="pagination justify-content-center mb-0">
                            {% if employees.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('employees.index', page=employees.prev_num, **request.args) }}">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in employees.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != employees.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('employees.index', page=page_num, **request.args) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if employees.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('employees.index', page=employees.next_num, **request.args) }}">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la terminaison</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir marquer l'employé <strong id="employeeName"></strong> comme terminé ?</p>
                <p class="text-muted">Cette action marquera l'employé comme inactif mais conservera toutes ses données.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Confirmer la terminaison</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
    font-weight: bold;
}
</style>

<script>
function confirmDelete(employeeId, employeeName) {
    document.getElementById('employeeName').textContent = employeeName;
    document.getElementById('deleteForm').action = '/employees/' + employeeId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
