"""Add AI Support tables

Revision ID: add_ai_support_001
Revises: 6185b869dea3
Create Date: 2025-06-29 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_ai_support_001'
down_revision = '6185b869dea3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Create support_tickets table
    op.create_table('support_tickets',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('ticket_number', sa.String(length=20), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('status', sa.Enum('OPEN', 'IN_PROGRESS', 'WAITING_AI', 'WAITING_HUMAN', 'RESOLVED', 'CLOSED', name='supportticketstatus'), nullable=True),
    sa.Column('priority', sa.Enum('LOW', 'MEDIUM', 'HIGH', 'URGENT', name='supportticketpriority'), nullable=True),
    sa.Column('category', sa.Enum('TECHNICAL', 'BILLING', 'GENERAL', 'FEATURE_REQUEST', 'BUG_REPORT', 'TRAINING', 'INTEGRATION', name='supportticketcategory'), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('assigned_agent_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('resolved_at', sa.DateTime(), nullable=True),
    sa.Column('closed_at', sa.DateTime(), nullable=True),
    sa.Column('satisfaction_rating', sa.Integer(), nullable=True),
    sa.Column('satisfaction_comment', sa.Text(), nullable=True),
    sa.Column('ai_handled', sa.Boolean(), nullable=True),
    sa.Column('escalated_to_human', sa.Boolean(), nullable=True),
    sa.Column('escalation_reason', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['assigned_agent_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('ticket_number')
    )
    
    # Create support_messages table
    op.create_table('support_messages',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('ticket_id', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('sender_type', sa.Enum('USER', 'AI', 'HUMAN_AGENT', 'SYSTEM', name='messagesender'), nullable=False),
    sa.Column('sender_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('is_internal', sa.Boolean(), nullable=True),
    sa.Column('ai_confidence', sa.Float(), nullable=True),
    sa.Column('ai_model_used', sa.String(length=50), nullable=True),
    sa.Column('processing_time', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['sender_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['ticket_id'], ['support_tickets.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    
    # Create support_conversations table
    op.create_table('support_conversations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('session_id', sa.String(length=100), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('ended_at', sa.DateTime(), nullable=True),
    sa.Column('total_messages', sa.Integer(), nullable=True),
    sa.Column('ai_responses', sa.Integer(), nullable=True),
    sa.Column('human_responses', sa.Integer(), nullable=True),
    sa.Column('user_satisfaction', sa.Integer(), nullable=True),
    sa.Column('feedback', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('session_id')
    )
    
    # Create support_chat_messages table
    op.create_table('support_chat_messages',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('conversation_id', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('sender_type', sa.Enum('USER', 'AI', 'HUMAN_AGENT', 'SYSTEM', name='messagesender'), nullable=False),
    sa.Column('sender_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('read_at', sa.DateTime(), nullable=True),
    sa.Column('ai_confidence', sa.Float(), nullable=True),
    sa.Column('ai_model_used', sa.String(length=50), nullable=True),
    sa.Column('processing_time', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['conversation_id'], ['support_conversations.id'], ),
    sa.ForeignKeyConstraint(['sender_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    
    # Create support_knowledge_base table
    op.create_table('support_knowledge_base',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('category', sa.Enum('TECHNICAL', 'BILLING', 'GENERAL', 'FEATURE_REQUEST', 'BUG_REPORT', 'TRAINING', 'INTEGRATION', name='supportticketcategory'), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('created_by_id', sa.Integer(), nullable=False),
    sa.Column('usage_count', sa.Integer(), nullable=True),
    sa.Column('last_used', sa.DateTime(), nullable=True),
    sa.Column('tags', sa.String(length=500), nullable=True),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    
    # Create support_analytics table
    op.create_table('support_analytics',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('date', sa.Date(), nullable=False),
    sa.Column('tickets_created', sa.Integer(), nullable=True),
    sa.Column('tickets_resolved', sa.Integer(), nullable=True),
    sa.Column('tickets_escalated', sa.Integer(), nullable=True),
    sa.Column('ai_responses', sa.Integer(), nullable=True),
    sa.Column('ai_success_rate', sa.Float(), nullable=True),
    sa.Column('average_ai_confidence', sa.Float(), nullable=True),
    sa.Column('average_response_time', sa.Float(), nullable=True),
    sa.Column('average_satisfaction', sa.Float(), nullable=True),
    sa.Column('total_ratings', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('support_analytics')
    op.drop_table('support_knowledge_base')
    op.drop_table('support_chat_messages')
    op.drop_table('support_conversations')
    op.drop_table('support_messages')
    op.drop_table('support_tickets')
    
    # Drop enums
    op.execute('DROP TYPE IF EXISTS supportticketstatus')
    op.execute('DROP TYPE IF EXISTS supportticketpriority')
    op.execute('DROP TYPE IF EXISTS supportticketcategory')
    op.execute('DROP TYPE IF EXISTS messagesender')
    
    # ### end Alembic commands ###
