from datetime import datetime, timedelta, date
from calendar import monthrange
import pytz
from flask import current_app, request

def get_current_time(timezone=None):
    if timezone is None:
        timezone = current_app.config.get('TIMEZONE', 'UTC')
    tz = pytz.timezone(timezone)
    return datetime.now(tz)

def format_date(date_obj, format_str=None):
    if format_str is None:
        format_str = current_app.config.get('DATE_FORMAT', '%d/%m/%Y')
    return date_obj.strftime(format_str)

def format_time(time_obj, format_str=None):
    if format_str is None:
        format_str = current_app.config.get('TIME_FORMAT', '%H:%M')
    return time_obj.strftime(format_str)

def format_datetime(datetime_obj, date_format=None, time_format=None):
    date_str = format_date(datetime_obj, date_format)
    time_str = format_time(datetime_obj, time_format)
    return f"{date_str} {time_str}"

def get_date_range(range_type):
    today = date.today()
    
    if range_type == 'today':
        return today, today
    
    elif range_type == 'yesterday':
        yesterday = today - timedelta(days=1)
        return yesterday, yesterday
    
    elif range_type == 'this_week':
        start = today - timedelta(days=today.weekday())
        end = start + timedelta(days=6)
        return start, end
    
    elif range_type == 'this_month':
        start = today.replace(day=1)
        end = today.replace(day=monthrange(today.year, today.month)[1])
        return start, end
    
    elif range_type == 'this_year':
        start = today.replace(month=1, day=1)
        end = today.replace(month=12, day=31)
        return start, end
    
    return None, None

def format_currency(amount, currency=None):
    if currency is None:
        currency = current_app.config.get('CURRENCY', 'EUR')
    
    if currency == 'EUR':
        return f"{amount:.2f} €"
    elif currency == 'USD':
        return f"${amount:.2f}"
    elif currency == 'GBP':
        return f"£{amount:.2f}"
    
    return f"{amount:.2f} {currency}"

def calculate_percentage(part, whole):
    if whole == 0:
        return 0
    return (part / whole) * 100

def generate_reference(prefix, number):
    return f"{prefix}{number:06d}"

def format_phone_number(phone):
    if not phone:
        return ""
    
    # Supprimer tous les caractères non numériques
    cleaned = ''.join(filter(str.isdigit, phone))
    
    # Format français
    if len(cleaned) == 10 and cleaned.startswith('0'):
        return f"{cleaned[:2]} {cleaned[2:4]} {cleaned[4:6]} {cleaned[6:8]} {cleaned[8:]}"

    return phone

def is_valid_email(email):
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def truncate_string(string, length=50, suffix='...'):
    if len(string) <= length:
        return string
    return string[:length].rsplit(' ', 1)[0] + suffix

def get_date_range(period):
    today = datetime.utcnow()
    if period == 'today':
        start_date = today.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = today.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif period == 'week':
        start_date = (today - timedelta(days=today.weekday())).replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = today.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif period == 'month':
        start_date = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        end_date = today.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif period == 'quarter':
        quarter = (today.month - 1) // 3 + 1
        start_date = datetime(today.year, 3 * quarter - 2, 1)
        end_date = datetime(today.year, 3 * quarter + 1, 1) - timedelta(days=1)
    elif period == 'semester':
        semester = 1 if today.month <= 6 else 2
        start_date = datetime(today.year, 1 if semester == 1 else 7, 1)
        end_date = datetime(today.year, 6 if semester == 1 else 12, 30)
    elif period == 'year':
        start_date = today.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
        end_date = today.replace(month=12, day=31, hour=23, minute=59, second=59, microsecond=999999)
    elif period == 'custom':
        start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d')
        end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d')
        end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif period == 'specific':
        specific_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d')
        start_date = specific_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = specific_date.replace(hour=23, minute=59, second=59, microsecond=999999)
    else:
        start_date = today.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = today.replace(hour=23, minute=59, second=59, microsecond=999999)
    return start_date, end_date