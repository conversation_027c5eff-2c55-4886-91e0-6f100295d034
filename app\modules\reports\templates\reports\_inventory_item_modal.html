<!-- Item Information -->
<div class="row mb-4">
    <div class="col-md-6">
        <h6 class="font-weight-bold">Informations générales</h6>
        <table class="table table-sm">
            <tr>
                <th style="width: 150px">Nom</th>
                <td>{{ item.name }}</td>
            </tr>
            <tr>
                <th>Catégorie</th>
                <td>
                    <span class="badge" style="background-color: {{ item.category.color if item.category else '#6c757d' }}">
                        {{ item.category.name if item.category else "Sans catégorie" }}
                    </span>
                </td>
            </tr>
            <tr>
                <th>Prix unitaire</th>
                <td>{{ "%.2f"|format(item.price_per_unit if item_type == 'ingredient' else item.price) }} €</td>
            </tr>
            <tr>
                <th>Stock actuel</th>
                <td>
                    {{ item.stock_quantity }} {{ item.unit }}
                    {% if item.stock_quantity <= 0 %}
                    <span class="badge bg-danger ms-2">Rupture</span>
                    {% elif item.stock_quantity <= item.minimum_stock %}
                    <span class="badge bg-warning ms-2">Stock bas</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>Stock minimum</th>
                <td>{{ item.minimum_stock }} {{ item.unit }}</td>
            </tr>
        </table>
    </div>
    <div class="col-md-6">
        <h6 class="font-weight-bold">Performance</h6>
        <table class="table table-sm">
            <tr>
                <th style="width: 150px">Valeur du stock</th>
                <td>{{ "%.2f"|format(item.stock_quantity * (item.price_per_unit if item_type == 'ingredient' else item.price)) }} €</td>
            </tr>
            <tr>
                <th>Mouvements</th>
                <td>{{ stats.movements_count if stats.movements_count else 0 }} sur la période</td>
            </tr>
            <tr>
                <th>Rotation</th>
                <td>{{ "%.1f"|format(stats.turnover_rate if stats.turnover_rate else 0) }} par an</td>
            </tr>
            <tr>
                <th>Stock optimal</th>
                <td>{{ "%.1f"|format(stats.optimal_stock) }} {{ item.unit }}</td>
            </tr>
        </table>
    </div>
</div>

<!-- Stock Level Trend -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="font-weight-bold mb-0">Évolution du stock</h6>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-primary {% if period == 'week' %}active{% endif %}" data-period="week">7 jours</button>
                <button type="button" class="btn btn-outline-primary {% if period == 'month' %}active{% endif %}" data-period="month">30 jours</button>
                <button type="button" class="btn btn-outline-primary {% if period == 'year' %}active{% endif %}" data-period="year">12 mois</button>
            </div>
        </div>
        <div class="chart-container" style="position: relative; height: 300px;">
            <canvas id="stockLevelChart"></canvas>
        </div>
    </div>
</div>

<!-- Hidden data for chart updates -->
<div id="chart-data" style="display: none;">
    <div id="dates-data">{{ dates|tojson|safe }}</div>
    <div id="stock-levels-data">{{ stock_levels|tojson|safe }}</div>
</div>

<!-- Stock Movements -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="font-weight-bold mb-0">Mouvements de stock</h6>
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#exportModal">
                    <i class="fas fa-file-export"></i> Exporter
                </button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Type</th>
                        <th>Motif</th>
                        <th>Quantité</th>
                        <th>Stock avant</th>
                        <th>Stock après</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    {% for movement in movements %}
                    <tr>
                        <td>{{ movement.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                        <td>
                            {% if movement.type == 'in' %}
                            <span class="badge bg-success">Entrée</span>
                            {% else %}
                            <span class="badge bg-danger">Sortie</span>
                            {% endif %}
                        </td>
                        <td>{{ movement.reason }}</td>
                        <td>
                            {% if movement.type == 'in' %}
                            <span class="text-success">+{{ movement.quantity }}</span>
                            {% else %}
                            <span class="text-danger">-{{ movement.quantity }}</span>
                            {% endif %}
                        </td>
                        <td>{{ movement.previous_quantity }}</td>
                        <td>{{ movement.new_quantity }}</td>
                        <td>{{ movement.notes or '' }}</td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">Aucun mouvement de stock enregistré</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Chart.js library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- JavaScript for Charts -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get the canvas element
    const ctx = document.getElementById('stockLevelChart');
    if (!ctx) {
        console.error('Cannot find chart canvas');
        return;
    }

    // Get the data
    const dates = {{ dates|tojson|safe }};
    const stockLevels = {{ stock_levels|tojson|safe }};
    const minimumStock = {{ item.minimum_stock }};
    const unit = "{{ item.unit }}";

    console.log('Chart data:', { dates, stockLevels, minimumStock, unit });

    // Create the chart
    const chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [{
                label: 'Niveau de stock',
                data: stockLevels,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }, {
                label: 'Stock minimum',
                data: Array(dates.length).fill(minimumStock),
                borderColor: '#dc3545',
                borderWidth: 2,
                borderDash: [5, 5],
                fill: false,
                pointRadius: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + context.parsed.y + ' ' + unit;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value + ' ' + unit;
                        }
                    }
                }
            }
        }
    });

    // Handle period buttons
    document.querySelectorAll('[data-period]').forEach(button => {
        button.addEventListener('click', function() {
            // Update active state
            document.querySelectorAll('[data-period]').forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');

            // Get the selected period
            const period = this.dataset.period;
            const itemId = {{ item.id }};

            // Fetch new data
            fetch(`/reports/inventory/${itemId}/details?period=${period}`)
                .then(response => response.text())
                .then(html => {
                    // Parse the new data from the HTML
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newDates = JSON.parse(doc.getElementById('dates-data').textContent);
                    const newStockLevels = JSON.parse(doc.getElementById('stock-levels-data').textContent);

                    // Update chart data
                    chart.data.labels = newDates;
                    chart.data.datasets[0].data = newStockLevels;
                    chart.data.datasets[1].data = Array(newDates.length).fill(minimumStock);
                    chart.update();
                });
        });
    });
});</script> 