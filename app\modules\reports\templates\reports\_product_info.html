<!-- Product Information -->
<div class="table-responsive">
    <table class="table table-sm">
        <tr>
            <th style="width: 150px">Nom</th>
            <td>{{ product.name }}</td>
        </tr>
        <tr>
            <th>Catégorie</th>
            <td>
                {% if product.category %}
                <span class="badge" style="background-color: {{ product.category.color if product.category.color else '#6c757d' }}">
                    {{ product.category.name }}
                </span>
                {% else %}
                <span class="text-muted">Sans catégorie</span>
                {% endif %}
            </td>
        </tr>
        <tr>
            <th>Prix</th>
            <td>{{ product.price|format_currency }}</td>
        </tr>
        {% if product.description %}
        <tr>
            <th>Description</th>
            <td>{{ product.description }}</td>
        </tr>
        {% endif %}
        <tr>
            <th>Stock actuel</th>
            <td>{{ product.get_available_quantity() }}</td>
        </tr>
        <tr>
            <th>Stock minimum</th>
            <td>{{ product.minimum_stock }}</td>
        </tr>
        <tr>
            <th>Statut</th>
            <td>
                {% if product.get_available_quantity() == 0 %}
                <span class="badge bg-danger">Rupture</span>
                {% elif product.get_available_quantity() <= product.minimum_stock %}
                <span class="badge bg-warning">Stock faible</span>
                {% else %}
                <span class="badge bg-success">En stock</span>
                {% endif %}
            </td>
        </tr>
    </table>
</div> 