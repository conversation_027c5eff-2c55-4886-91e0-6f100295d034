<div class="modal fade" id="cashInModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{{ url_for('cash_register.cash_in') }}">
                {{ cash_in_form.csrf_token }}
                <div class="modal-header">
                    <h5 class="modal-title">Entrée de caisse</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="amount" class="form-label">{{ cash_in_form.amount.label }}</label>
                        {{ cash_in_form.amount(class="form-control", type="number", step="0.01", id="amount") }}
                    </div>
                    <div class="mb-3">
                        <label for="source" class="form-label">{{ cash_in_form.source.label }}</label>
                        {{ cash_in_form.source(class="form-select", id="source") }}
                    </div>
                    <div class="mb-3">
                        <label for="note" class="form-label">{{ cash_in_form.note.label }}</label>
                        {{ cash_in_form.note(class="form-control", rows=3, id="note") }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                </div>
            </form>
        </div>
    </div>
</div>
