{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-tags"></i>
            Catégories de dépenses
        </h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#categoryModal">
            <i class="fas fa-plus"></i>
            Nouvelle catégorie
        </button>
    </div>

    <!-- Categories Grid -->
    <div class="row">
        {% for category in categories %}
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="d-flex align-items-center">
                            {% if category.image_path %}
                            <img src="{{ url_for('static', filename=category.image_path) }}" 
                                 class="rounded-circle me-2" 
                                 style="width: 32px; height: 32px; object-fit: cover;"
                                 alt="{{ category.name }}">
                            {% endif %}
                            <h5 class="mb-0">{{ category.name }}</h5>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-link btn-sm p-0 text-muted" 
                                    type="button" 
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <button type="button" 
                                            class="dropdown-item" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#categoryModal"
                                            data-category-id="{{ category.id }}"
                                            data-category-name="{{ category.name }}"
                                            data-category-description="{{ category.description }}">
                                        <i class="fas fa-edit"></i>
                                        Modifier
                                    </button>
                                </li>
                                <li>
                                    <button type="button" 
                                            class="dropdown-item text-danger delete-category" 
                                            data-category-id="{{ category.id }}">
                                        <i class="fas fa-trash"></i>
                                        Supprimer
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>

                    {% if category.description %}
                    <p class="text-muted small mb-3">{{ category.description }}</p>
                    {% endif %}

                    <div class="row text-center">
                        <div class="col">
                            <div class="h4 mb-0">{{ category.expenses.count() }}</div>
                            <div class="small text-muted">Dépenses</div>
                        </div>
                        <div class="col">
                            <div class="h4 mb-0">{{ "%.2f"|format(category.get_total_expenses()) }} €</div>
                            <div class="small text-muted">Total</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <div class="text-muted mb-3">
                        <i class="fas fa-tags fa-3x"></i>
                    </div>
                    <h5>Aucune catégorie</h5>
                    <p class="text-muted">
                        Créez des catégories pour organiser vos dépenses
                    </p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#categoryModal">
                        <i class="fas fa-plus"></i>
                        Nouvelle catégorie
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Catégorie de dépenses</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="categoryForm" method="POST" enctype="multipart/form-data">
                    {{ form.csrf_token }}
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                        {% for error in form.name.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                    </div>

                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="3") }}
                    </div>

                    <div class="mb-3">
                        {{ form.image.label(class="form-label") }}
                        {{ form.image(class="form-control" + (" is-invalid" if form.image.errors else "")) }}
                        {% for error in form.image.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                        <div class="form-text">Formats acceptés: JPG, PNG, JPEG, GIF</div>
                    </div>

                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const categoryModal = document.getElementById('categoryModal');
    const categoryForm = document.getElementById('categoryForm');
    const categoryNameInput = categoryForm.querySelector('input[name="name"]');
    const categoryDescriptionInput = categoryForm.querySelector('textarea[name="description"]');

    // Handle modal show
    categoryModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const isEdit = button.dataset.categoryId;
        
        if (isEdit) {
            // Edit mode
            categoryNameInput.value = button.dataset.categoryName;
            categoryDescriptionInput.value = button.dataset.categoryDescription || '';
            categoryForm.action = `/expenses/categories/${button.dataset.categoryId}/edit`;
        } else {
            // Create mode
            categoryForm.reset();
            categoryForm.action = '/expenses/categories';
        }
    });

    // Handle category deletion
    document.querySelectorAll('.delete-category').forEach(button => {
        button.addEventListener('click', function() {
            const categoryId = this.dataset.categoryId;
            if (confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ?')) {
                fetch(`/expenses/categories/${categoryId}/delete`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRFToken': '{{ csrf_token() }}'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Une erreur est survenue lors de la suppression.');
                });
            }
        });
    });
});
</script>
{% endblock %} 