<div class="numpad">
    <div class="numpad-display" id="numpadDisplay">0</div>
    <div class="row g-1">
        <div class="col-4"><button class="numpad-btn" data-value="7">7</button></div>
        <div class="col-4"><button class="numpad-btn" data-value="8">8</button></div>
        <div class="col-4"><button class="numpad-btn" data-value="9">9</button></div>
        <div class="col-4"><button class="numpad-btn" data-value="4">4</button></div>
        <div class="col-4"><button class="numpad-btn" data-value="5">5</button></div>
        <div class="col-4"><button class="numpad-btn" data-value="6">6</button></div>
        <div class="col-4"><button class="numpad-btn" data-value="1">1</button></div>
        <div class="col-4"><button class="numpad-btn" data-value="2">2</button></div>
        <div class="col-4"><button class="numpad-btn" data-value="3">3</button></div>
        <div class="col-4"><button class="numpad-btn" data-value="0">0</button></div>
        <div class="col-4"><button class="numpad-btn" data-value="00">00</button></div>
        <div class="col-4"><button class="numpad-btn" data-value=".">.</button></div>
        <div class="col-6"><button class="numpad-btn backspace" data-value="backspace"><i class="fas fa-backspace"></i></button></div>
        <div class="col-6"><button class="numpad-btn clear" data-value="clear">C</button></div>
    </div>
</div>

<style>
.numpad {
    background-color: var(--light-color);
    border-radius: var(--radius);
    padding: 10px;
    box-shadow: var(--shadow);
}

.numpad-display {
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 10px;
    text-align: right;
    font-size: 20px;
    font-weight: bold;
    min-height: 40px;
}

.numpad-btn {
    width: 100%;
    height: 45px;
    margin: 2px 0;
    font-size: 16px;
    font-weight: bold;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: white;
    transition: all 0.2s;
    cursor: pointer;
}

.numpad-btn:hover {
    background-color: #e9ecef;
    transform: translateY(-1px);
}

.numpad-btn.clear {
    background-color: var(--danger-color);
    color: white;
}

.numpad-btn.backspace {
    background-color: var(--warning-color);
}

@media (max-width: 1200px) {
    .numpad {
        margin-bottom: 20px;
    }
}
</style> 