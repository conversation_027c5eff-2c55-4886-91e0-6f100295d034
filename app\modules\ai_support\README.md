# Module Support AI - Système POS

## Vue d'ensemble

Le module Support AI intègre l'intelligence artificielle Gemini 2.0 Flash de Google pour fournir un support client et technique automatisé 24h/24 et 7j/7. Ce système permet aux utilisateurs d'obtenir une assistance immédiate via chat en temps réel ou tickets de support.

## Fonctionnalités principales

### 🤖 Chat en temps réel avec IA
- Interface de chat moderne et intuitive
- Réponses instantanées de l'IA Gemini 2.0 Flash
- Historique des conversations
- Escalade automatique vers agents humains si nécessaire
- Indicateurs de confiance de l'IA

### 🎫 Système de tickets de support
- Création et gestion de tickets
- Classification automatique des demandes
- Suivi du statut et de la priorité
- Historique complet des échanges
- Évaluation de satisfaction client

### 📚 Base de connaissances
- Articles d'aide et guides d'utilisation
- Recherche intelligente
- Catégorisation par domaine
- Statistiques d'utilisation

### 📊 Analytics et reporting
- Tableau de bord administrateur
- Métriques de performance de l'IA
- Statistiques de satisfaction client
- Rapports d'escalade

## Configuration

### Variables d'environnement

Créez un fichier `.env` basé sur `.env.example` :

```bash
# Configuration Gemini AI
GEMINI_API_KEY=your-gemini-api-key-here
GEMINI_MODEL=gemini-2.0-flash-exp
GEMINI_TEMPERATURE=0.7
GEMINI_MAX_TOKENS=2048

# Configuration du support AI
AI_SUPPORT_ENABLED=True
AI_CONFIDENCE_THRESHOLD=0.7
AI_MAX_CONVERSATION_LENGTH=20
AI_AUTO_ESCALATION=True
```

### Obtenir une clé API Gemini

1. Visitez [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Créez un nouveau projet ou sélectionnez un projet existant
3. Générez une clé API
4. Ajoutez la clé dans votre fichier `.env`

### Installation des dépendances

```bash
pip install google-generativeai tenacity
```

## Structure du module

```
app/modules/ai_support/
├── __init__.py              # Blueprint du module
├── models.py                # Modèles de données
├── routes.py                # Routes et API endpoints
├── forms.py                 # Formulaires WTForms
├── gemini_service.py        # Service d'intégration Gemini AI
├── templates/               # Templates HTML
│   └── ai_support/
│       ├── base.html        # Template de base
│       ├── index.html       # Page d'accueil
│       ├── chat.html        # Interface de chat
│       ├── tickets.html     # Liste des tickets
│       └── create_ticket.html # Création de ticket
└── README.md               # Cette documentation
```

## Modèles de données

### SupportTicket
- Gestion des tickets de support
- Statuts : OPEN, IN_PROGRESS, WAITING_AI, WAITING_HUMAN, RESOLVED, CLOSED
- Priorités : LOW, MEDIUM, HIGH, URGENT
- Catégories : TECHNICAL, BILLING, GENERAL, etc.

### SupportConversation
- Conversations de chat en temps réel
- Métadonnées de session
- Statistiques d'interaction

### SupportMessage / SupportChatMessage
- Messages dans les tickets et conversations
- Métadonnées IA (confiance, modèle utilisé, temps de traitement)
- Types d'expéditeur : USER, AI, HUMAN_AGENT, SYSTEM

### SupportKnowledgeBase
- Articles de la base de connaissances
- Système de tags et catégories
- Statistiques d'utilisation

## API Endpoints

### Chat en temps réel
- `POST /support/api/chat/send` - Envoyer un message
- `GET /support/api/chat/history/<conversation_id>` - Historique

### Gestion des tickets
- `POST /support/api/tickets/<id>/escalate` - Escalader un ticket

## Utilisation

### Pour les utilisateurs finaux

1. **Chat en direct** : Accédez à `/support/chat` pour une assistance immédiate
2. **Créer un ticket** : Utilisez `/support/tickets/create` pour des problèmes complexes
3. **Consulter la base de connaissances** : Visitez `/support/knowledge-base`

### Pour les administrateurs

1. **Tableau de bord** : `/support/admin` pour les statistiques
2. **Gestion des tickets escaladés** : Suivi des demandes nécessitant intervention humaine
3. **Configuration IA** : Ajustement des seuils de confiance et paramètres

## Fonctionnalités avancées

### Escalade automatique
L'IA escalade automatiquement vers un agent humain dans ces cas :
- Confiance moyenne < seuil configuré (défaut: 70%)
- Conversation trop longue sans résolution
- Détection de mots-clés de frustration
- Demande explicite d'escalade

### Classification intelligente
L'IA analyse automatiquement les demandes pour :
- Déterminer la catégorie appropriée
- Évaluer la priorité
- Extraire les mots-clés pertinents
- Calculer un score de confiance

### Personnalisation des réponses
Le système adapte ses réponses selon :
- Le contexte du système POS
- L'historique de l'utilisateur
- Le type de problème rencontré
- Les informations de la base de connaissances

## Sécurité et confidentialité

- Toutes les conversations sont chiffrées
- Accès basé sur les rôles utilisateur
- Logs d'audit pour les actions sensibles
- Respect des bonnes pratiques de sécurité IA

## Maintenance et monitoring

### Logs
Les logs sont disponibles dans les fichiers de log Flask standard :
- Erreurs de l'API Gemini
- Performances des réponses
- Escalades automatiques

### Métriques à surveiller
- Taux de satisfaction client
- Temps de réponse moyen de l'IA
- Taux d'escalade vers agents humains
- Utilisation de la base de connaissances

## Dépannage

### Problèmes courants

1. **L'IA ne répond pas**
   - Vérifiez la clé API Gemini
   - Contrôlez la connectivité internet
   - Consultez les logs d'erreur

2. **Réponses de mauvaise qualité**
   - Ajustez la température du modèle
   - Enrichissez la base de connaissances
   - Modifiez les prompts système

3. **Escalades trop fréquentes**
   - Réduisez le seuil de confiance
   - Augmentez la longueur max de conversation
   - Améliorez les prompts de contexte

## Contribution

Pour contribuer au module :
1. Respectez la structure existante
2. Ajoutez des tests pour les nouvelles fonctionnalités
3. Documentez les changements
4. Suivez les conventions de code Python/Flask

## Support

Pour obtenir de l'aide :
1. Consultez cette documentation
2. Vérifiez les logs d'erreur
3. Testez avec une clé API valide
4. Contactez l'équipe de développement
