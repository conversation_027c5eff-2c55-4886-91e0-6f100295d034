{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Import/Export</h1>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Import Card -->
        <div class="col-xl-6 col-md-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Importer des données</h6>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('advanced.import_data') }}" method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="importType" class="form-label">Type de données</label>
                            <select class="form-select" id="importType" name="type" required>
                                <option value="">Sélectionner...</option>
                                <option value="products">Produits</option>
                                <option value="ingredients">Ingrédients</option>
                                <option value="customers">Clients</option>
                                <option value="categories">Catégories</option>
                                <option value="recipes">Recettes</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="importFile" class="form-label">Fichier</label>
                            <input type="file" class="form-control" id="importFile" name="file" accept=".csv,.xlsx" required>
                            <div class="form-text">Formats acceptés : CSV, Excel</div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="updateExisting" name="update_existing">
                                <label class="form-check-label" for="updateExisting">
                                    Mettre à jour les données existantes
                                </label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload fa-sm"></i> Importer
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Export Card -->
        <div class="col-xl-6 col-md-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Exporter des données</h6>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('advanced.export_data') }}" method="POST">
                        <div class="mb-3">
                            <label for="exportType" class="form-label">Type de données</label>
                            <select class="form-select" id="exportType" name="type" required>
                                <option value="">Sélectionner...</option>
                                <option value="products">Produits</option>
                                <option value="ingredients">Ingrédients</option>
                                <option value="customers">Clients</option>
                                <option value="categories">Catégories</option>
                                <option value="recipes">Recettes</option>
                                <option value="sales">Ventes</option>
                                <option value="expenses">Dépenses</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="exportFormat" class="form-label">Format</label>
                            <select class="form-select" id="exportFormat" name="format" required>
                                <option value="csv">CSV</option>
                                <option value="xlsx">Excel</option>
                                <option value="json">JSON</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="dateRange" class="form-label">Période (optionnel)</label>
                            <div class="input-group">
                                <input type="date" class="form-control" id="startDate" name="start_date">
                                <span class="input-group-text">à</span>
                                <input type="date" class="form-control" id="endDate" name="end_date">
                            </div>
                            <div class="form-text">Pour les ventes et dépenses uniquement</div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-download fa-sm"></i> Exporter
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- History Card -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Historique des imports/exports</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Action</th>
                                    <th>Fichier</th>
                                    <th>Statut</th>
                                    <th>Détails</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="6" class="text-center">Aucun historique disponible</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Preview Modal -->
<div class="modal fade" id="importPreviewModal" tabindex="-1" aria-labelledby="importPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importPreviewModalLabel">Aperçu de l'import</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Ligne</th>
                                <th>Statut</th>
                                <th>Données</th>
                                <th>Erreurs</th>
                            </tr>
                        </thead>
                        <tbody id="previewTableBody">
                            <!-- Filled dynamically -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" id="confirmImport">Confirmer l'import</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide date range based on export type
    const exportType = document.getElementById('exportType');
    const dateRange = document.getElementById('dateRange');
    
    exportType.addEventListener('change', function() {
        if (this.value === 'sales' || this.value === 'expenses') {
            dateRange.style.display = 'block';
        } else {
            dateRange.style.display = 'none';
        }
    });

    // Handle file import preview
    const importFile = document.getElementById('importFile');
    importFile.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('type', document.getElementById('importType').value);
            
            fetch('/advanced/import/preview', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const tbody = document.getElementById('previewTableBody');
                tbody.innerHTML = '';
                
                data.preview.forEach((row, index) => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${index + 1}</td>
                        <td><span class="badge bg-${row.valid ? 'success' : 'danger'}">${row.valid ? 'Valide' : 'Erreur'}</span></td>
                        <td><code>${JSON.stringify(row.data)}</code></td>
                        <td>${row.errors ? `<ul class="mb-0"><li>${row.errors.join('</li><li>')}</li></ul>` : ''}</td>
                    `;
                    tbody.appendChild(tr);
                });
                
                const modal = new bootstrap.Modal(document.getElementById('importPreviewModal'));
                modal.show();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue lors de la prévisualisation du fichier.');
            });
        }
    });
});
</script>
{% endblock %} 