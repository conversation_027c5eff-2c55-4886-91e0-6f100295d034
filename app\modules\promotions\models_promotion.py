from datetime import datetime
from app import db

class PromotionType:
    PERCENTAGE = 'percentage'
    FIXED = 'fixed'
    BUY_X_GET_Y = 'buy_x_get_y'

class Promotion(db.Model):
    __tablename__ = 'promotions'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    promotion_type = db.Column(db.String(20), nullable=False)
    value = db.Column(db.Float, nullable=False)  # Pourcentage ou montant fixe
    buy_x = db.Column(db.Integer, nullable=True)  # Pour les promos "achetez X obtenez Y"
    get_y = db.Column(db.Integer, nullable=True)  # Pour les promos "achetez X obtenez Y"
    start_date = db.Column(db.DateTime, nullable=False)
    end_date = db.Column(db.DateTime, nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    min_purchase = db.Column(db.Float, nullable=True)  # Montant minimum d'achat
    max_discount = db.Column(db.Float, nullable=True)  # Montant maximum de réduction
    usage_limit = db.Column(db.Integer, nullable=True)  # Nombre maximum d'utilisations
    current_usage = db.Column(db.Integer, default=0)
    
    # Relations
    products = db.relationship('Product', secondary='promotion_products', backref='promotions')
    owner = db.relationship('User', backref='promotions')
    
    def __repr__(self):
        return f'<Promotion {self.name}>'
    
    def is_valid(self):
        now = datetime.utcnow()
        if not self.is_active:
            return False
        if now < self.start_date or now > self.end_date:
            return False
        if self.usage_limit and self.current_usage >= self.usage_limit:
            return False
        return True
    
    def calculate_discount(self, subtotal):
        if not self.is_valid():
            return 0
            
        if self.min_purchase and subtotal < self.min_purchase:
            return 0
            
        if self.promotion_type == PromotionType.PERCENTAGE:
            discount = subtotal * (self.value / 100)
        elif self.promotion_type == PromotionType.FIXED:
            discount = self.value
        else:
            return 0  # Pour BUY_X_GET_Y, la logique est gérée différemment
            
        if self.max_discount:
            discount = min(discount, self.max_discount)
            
        return discount
    
    def use_promotion(self):
        if self.is_valid():
            self.current_usage += 1
            db.session.commit()
            return True
        return False

# Table d'association pour les produits en promotion
promotion_products = db.Table('promotion_products',
    db.Column('promotion_id', db.Integer, db.ForeignKey('promotions.id'), primary_key=True),
    db.Column('product_id', db.Integer, db.ForeignKey('products.id'), primary_key=True)
) 