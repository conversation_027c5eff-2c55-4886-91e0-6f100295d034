{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user-tag"></i>
            Rôles et permissions
        </h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#roleModal">
            <i class="fas fa-plus"></i>
            Nouveau rôle
        </button>
    </div>

    <!-- Summary Cards -->
    <div class="row">
        <!-- Total Roles -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                R<PERSON>les
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ roles|length }}
                            </div>
                            <div class="text-xs text-muted mt-2">
                                {{ total_permissions }} permissions
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-tag fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users with Roles -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Utilisateurs
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_users }}
                            </div>
                            <div class="text-xs text-muted mt-2">
                                {{ "%.1f"|format(total_users / roles|length) }} par rôle en moyenne
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Most Used Role -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Rôle le plus utilisé
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ most_used_role.name }}
                            </div>
                            <div class="text-xs text-muted mt-2">
                                {{ most_used_role.users_count }} utilisateurs
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Average Permissions -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Permissions moyennes
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ "%.1f"|format(average_permissions) }}
                            </div>
                            <div class="text-xs text-muted mt-2">
                                par rôle
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-key fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Roles Table -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Rôle</th>
                            <th>Description</th>
                            <th>Permissions</th>
                            <th>Utilisateurs</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for role in roles %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle me-2" 
                                         style="width: 24px; height: 24px; background-color: {{ role.color }}">
                                    </div>
                                    <div>{{ role.name }}</div>
                                </div>
                            </td>
                            <td>{{ role.description }}</td>
                            <td>
                                {% for permission in role_permissions[role][:3] %}
                                <span class="badge bg-light text-dark">{{ permission.name }}</span>
                                {% endfor %}
                                {% if role_permissions[role]|length > 3 %}
                                <button type="button" 
                                        class="btn btn-link btn-sm p-0 ms-1" 
                                        data-bs-toggle="popover" 
                                        data-bs-trigger="focus"
                                        data-bs-html="true"
                                        data-bs-content="{% for permission in role_permissions[role][3:] %}{{ permission.name }}<br>{% endfor %}">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                                {% endif %}
                            </td>
                            <td>
                                {{ role.users_count }}
                                <div class="progress" style="height: 4px;">
                                    <div class="progress-bar" 
                                         role="progressbar" 
                                         style="width: {{ (role.users_count / max_users_count * 100)|round }}%; background-color: {{ role.color }}">
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-primary"
                                            data-bs-toggle="modal"
                                            data-bs-target="#roleModal"
                                            data-role-id="{{ role.id }}"
                                            data-role-name="{{ role.name }}"
                                            data-role-description="{{ role.description }}"
                                            data-role-color="{{ role.color }}"
                                            data-role-permissions="{{ role.permissions|map(attribute='id')|list|tojson }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    {% if not role.is_protected %}
                                    <button type="button" class="btn btn-outline-danger"
                                            data-bs-toggle="modal"
                                            data-bs-target="#deleteRoleModal"
                                            data-role-id="{{ role.id }}"
                                            data-role-name="{{ role.name }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Permissions Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-key"></i>
                Permissions disponibles
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Permission</th>
                            <th>Description</th>
                            <th>Module</th>
                            <th>Rôles</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for permission in permissions %}
                        <tr>
                            <td>{{ permission.name }}</td>
                            <td>{{ permission.description }}</td>
                            <td>
                                <span class="badge bg-light text-dark">
                                    {{ permission.module }}
                                </span>
                            </td>
                            <td>
                                {% for role in permission.roles %}
                                <span class="badge" style="background-color: {{ role.color }}">
                                    {{ role.name }}
                                </span>
                                {% endfor %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Role Modal -->
<div class="modal fade" id="roleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Rôle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="roleForm" method="POST">
                    <input type="hidden" name="role_id" id="role-id">

                    <div class="mb-3">
                        <label class="form-label">Nom</label>
                        <input type="text" name="name" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea name="description" class="form-control" rows="2"></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Couleur</label>
                        <div class="input-group">
                            <input type="color" name="color" class="form-control form-control-color" required>
                            <input type="text" name="color_hex" class="form-control" pattern="^#[0-9A-Fa-f]{6}$" required>
                        </div>
                        <small class="text-muted">Code hexadécimal (ex: #FF0000)</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Permissions</label>
                        <div class="row">
                            {% for module, module_permissions in permissions_by_module.items() %}
                            <div class="col-md-6 mb-3">
                                <h6 class="text-muted">{{ module }}</h6>
                                {% for permission in module_permissions %}
                                <div class="form-check">
                                    <input type="checkbox" 
                                           name="permissions[]" 
                                           value="{{ permission.id }}" 
                                           class="form-check-input" 
                                           id="permission-{{ permission.id }}">
                                    <label class="form-check-label" for="permission-{{ permission.id }}">
                                        {{ permission.name }}
                                        <i class="fas fa-info-circle text-muted" 
                                           data-bs-toggle="tooltip" 
                                           title="{{ permission.description }}">
                                        </i>
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="submit" form="roleForm" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Enregistrer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Role Modal -->
<div class="modal fade" id="deleteRoleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le rôle <strong id="delete-role-name"></strong> ?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    Cette action est irréversible. Les utilisateurs associés à ce rôle perdront les permissions correspondantes.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteRoleForm" method="POST">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i>
                        Supprimer
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize popovers and tooltips
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Role Modal
    const roleModal = document.getElementById('roleModal');
    const roleForm = document.getElementById('roleForm');
    const roleIdInput = document.getElementById('role-id');
    const roleNameInput = roleForm.querySelector('input[name="name"]');
    const roleDescriptionInput = roleForm.querySelector('textarea[name="description"]');
    const roleColorInput = roleForm.querySelector('input[name="color"]');
    const roleColorHexInput = roleForm.querySelector('input[name="color_hex"]');
    const rolePermissionInputs = roleForm.querySelectorAll('input[name="permissions[]"]');

    roleModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const roleId = button.dataset.roleId;
        
        if (roleId) {
            // Edit mode
            roleIdInput.value = roleId;
            roleNameInput.value = button.dataset.roleName;
            roleDescriptionInput.value = button.dataset.roleDescription;
            roleColorInput.value = button.dataset.roleColor;
            roleColorHexInput.value = button.dataset.roleColor;
            
            const rolePermissions = JSON.parse(button.dataset.rolePermissions);
            rolePermissionInputs.forEach(input => {
                input.checked = rolePermissions.includes(parseInt(input.value));
            });
            
            roleForm.action = `/admin/users/roles/${roleId}`;
        } else {
            // Create mode
            roleForm.reset();
            roleIdInput.value = '';
            roleForm.action = '/admin/users/roles';
        }
    });

    // Color input sync
    roleColorInput.addEventListener('input', function() {
        roleColorHexInput.value = this.value;
    });

    roleColorHexInput.addEventListener('input', function() {
        if (this.checkValidity()) {
            roleColorInput.value = this.value;
        }
    });

    // Delete Role Modal
    const deleteRoleModal = document.getElementById('deleteRoleModal');
    const deleteRoleForm = document.getElementById('deleteRoleForm');
    
    deleteRoleModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const roleId = button.dataset.roleId;
        const roleName = button.dataset.roleName;
        
        document.getElementById('delete-role-name').textContent = roleName;
        deleteRoleForm.action = `/admin/users/roles/${roleId}/delete`;
    });
});
</script>
{% endblock %}
{% endblock %} 