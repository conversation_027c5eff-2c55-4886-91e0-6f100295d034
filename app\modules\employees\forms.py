from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON>Field, FileAllowed
from wtforms import <PERSON><PERSON>ield, TextAreaField, SelectField, DateField, TimeField, DecimalField, IntegerField, BooleanField, SubmitField
from wtforms.validators import <PERSON>Required, Email, Optional, NumberRange, Length, ValidationError
from wtforms.widgets import TextArea
from datetime import date, time
from app.modules.employees.models import EmployeeStatus, ContractType, PaymentFrequency, AttendanceStatus, ShiftType

class EmployeeForm(FlaskForm):
    """Formulaire pour créer/modifier un employé"""
    
    # Informations personnelles
    employee_id = StringField('ID Employé', validators=[DataRequired(), Length(max=20)])
    first_name = StringField('Prénom', validators=[DataRequired(), Length(max=50)])
    last_name = StringField('Nom', validators=[DataRequired(), Length(max=50)])
    email = StringField('Email', validators=[Optional(), Email(), Length(max=120)])
    phone = StringField('Téléphone', validators=[Optional(), Length(max=20)])
    address = TextAreaField('Adresse', validators=[Optional()])
    date_of_birth = DateField('Date de naissance', validators=[Optional()])
    national_id = StringField('Numéro d\'identification nationale', validators=[Optional(), Length(max=50)])
    
    # Informations d'emploi
    hire_date = DateField('Date d\'embauche', validators=[DataRequired()], default=date.today)
    termination_date = DateField('Date de fin de contrat', validators=[Optional()])
    status = SelectField('Statut', 
                        choices=[(status.value, status.value.replace('_', ' ').title()) for status in EmployeeStatus],
                        validators=[DataRequired()])
    contract_type = SelectField('Type de contrat',
                               choices=[(contract.value, contract.value.replace('_', ' ').title()) for contract in ContractType],
                               validators=[DataRequired()])
    department = StringField('Département', validators=[Optional(), Length(max=100)])
    position = StringField('Poste', validators=[DataRequired(), Length(max=100)])
    
    # Informations salariales
    base_salary = DecimalField('Salaire de base', validators=[DataRequired(), NumberRange(min=0)], places=2)
    hourly_rate = DecimalField('Taux horaire', validators=[Optional(), NumberRange(min=0)], places=2)
    payment_frequency = SelectField('Fréquence de paiement',
                                   choices=[(freq.value, freq.value.replace('_', ' ').title()) for freq in PaymentFrequency],
                                   validators=[DataRequired()])
    
    submit = SubmitField('Enregistrer')
    
    def validate_employee_id(self, field):
        """Valide l'unicité de l'ID employé"""
        from app.modules.employees.models import Employee
        from flask_login import current_user
        
        employee = Employee.query.filter_by(
            employee_id=field.data,
            owner_id=current_user.get_owner_id
        ).first()
        
        # Si on modifie un employé existant, on ignore sa propre ID
        if hasattr(self, 'employee') and employee and employee.id != self.employee.id:
            raise ValidationError('Cet ID employé est déjà utilisé.')
        elif not hasattr(self, 'employee') and employee:
            raise ValidationError('Cet ID employé est déjà utilisé.')

class EmployeeProfileForm(FlaskForm):
    """Formulaire pour le profil détaillé d'un employé"""
    
    # Contact d'urgence
    emergency_contact_name = StringField('Nom du contact d\'urgence', validators=[Optional(), Length(max=100)])
    emergency_contact_phone = StringField('Téléphone du contact d\'urgence', validators=[Optional(), Length(max=20)])
    emergency_contact_relationship = StringField('Relation', validators=[Optional(), Length(max=50)])
    
    # Informations bancaires
    bank_name = StringField('Nom de la banque', validators=[Optional(), Length(max=100)])
    account_number = StringField('Numéro de compte', validators=[Optional(), Length(max=50)])
    routing_number = StringField('Code de routage', validators=[Optional(), Length(max=20)])
    
    # Compétences et qualifications
    skills = TextAreaField('Compétences', validators=[Optional()], widget=TextArea())
    certifications = TextAreaField('Certifications', validators=[Optional()], widget=TextArea())
    languages = TextAreaField('Langues parlées', validators=[Optional()], widget=TextArea())
    
    # Préférences
    preferred_shifts = TextAreaField('Préférences d\'horaires', validators=[Optional()], widget=TextArea())
    availability = TextAreaField('Disponibilité', validators=[Optional()], widget=TextArea())
    
    submit = SubmitField('Enregistrer le profil')

class WorkScheduleForm(FlaskForm):
    """Formulaire pour créer/modifier un planning de travail"""
    
    start_date = DateField('Date de début', validators=[DataRequired()])
    end_date = DateField('Date de fin', validators=[DataRequired()])
    day_of_week = SelectField('Jour de la semaine',
                             choices=[
                                 (0, 'Lundi'), (1, 'Mardi'), (2, 'Mercredi'), (3, 'Jeudi'),
                                 (4, 'Vendredi'), (5, 'Samedi'), (6, 'Dimanche')
                             ],
                             coerce=int, validators=[DataRequired()])
    shift_type = SelectField('Type de shift',
                            choices=[(shift.value, shift.value.replace('_', ' ').title()) for shift in ShiftType],
                            validators=[DataRequired()])
    start_time = TimeField('Heure de début', validators=[DataRequired()])
    end_time = TimeField('Heure de fin', validators=[DataRequired()])
    break_duration = IntegerField('Durée de pause (minutes)', validators=[Optional(), NumberRange(min=0, max=480)], default=30)
    notes = TextAreaField('Notes', validators=[Optional()])
    is_recurring = BooleanField('Planning récurrent', default=True)
    
    submit = SubmitField('Enregistrer le planning')
    
    def validate_end_date(self, field):
        """Valide que la date de fin est après la date de début"""
        if self.start_date.data and field.data and field.data < self.start_date.data:
            raise ValidationError('La date de fin doit être après la date de début.')
    
    def validate_end_time(self, field):
        """Valide que l'heure de fin est cohérente"""
        if self.start_time.data and field.data:
            # Pour les shifts qui se terminent le jour suivant, on accepte une heure de fin plus petite
            if field.data <= self.start_time.data:
                # Vérifier si c'est un shift de nuit (commence après 18h et finit avant 12h)
                if not (self.start_time.data >= time(18, 0) and field.data <= time(12, 0)):
                    raise ValidationError('L\'heure de fin doit être après l\'heure de début (sauf pour les shifts de nuit).')

class AttendanceForm(FlaskForm):
    """Formulaire pour enregistrer/modifier une présence"""
    
    date = DateField('Date', validators=[DataRequired()], default=date.today)
    status = SelectField('Statut',
                        choices=[(status.value, status.value.replace('_', ' ').title()) for status in AttendanceStatus],
                        validators=[DataRequired()])
    clock_in_time = TimeField('Heure d\'arrivée', validators=[Optional()])
    clock_out_time = TimeField('Heure de départ', validators=[Optional()])
    break_start_time = TimeField('Début de pause', validators=[Optional()])
    break_end_time = TimeField('Fin de pause', validators=[Optional()])
    break_duration = IntegerField('Durée de pause (minutes)', validators=[Optional(), NumberRange(min=0)], default=0)
    scheduled_hours = DecimalField('Heures prévues', validators=[Optional(), NumberRange(min=0)], places=2, default=0)
    hours_worked = DecimalField('Heures travaillées', validators=[Optional(), NumberRange(min=0, max=24)], places=2)
    notes = TextAreaField('Notes', validators=[Optional()])
    
    submit = SubmitField('Enregistrer la présence')
    
    def validate_clock_out_time(self, field):
        """Valide que l'heure de sortie est après l'heure d'entrée"""
        if self.clock_in_time.data and field.data and field.data <= self.clock_in_time.data:
            raise ValidationError('L\'heure de sortie doit être après l\'heure d\'entrée.')
    
    def validate_break_end_time(self, field):
        """Valide que la fin de pause est après le début de pause"""
        if self.break_start_time.data and field.data and field.data <= self.break_start_time.data:
            raise ValidationError('La fin de pause doit être après le début de pause.')

class PayrollForm(FlaskForm):
    """Formulaire pour créer/modifier une fiche de paie"""
    
    pay_period_start = DateField('Début de période', validators=[DataRequired()])
    pay_period_end = DateField('Fin de période', validators=[DataRequired()])
    pay_date = DateField('Date de paiement', validators=[DataRequired()])
    
    # Heures et taux
    regular_hours = DecimalField('Heures normales', validators=[DataRequired(), NumberRange(min=0)], places=2, default=0)
    overtime_hours = DecimalField('Heures supplémentaires', validators=[Optional(), NumberRange(min=0)], places=2, default=0)
    regular_rate = DecimalField('Taux normal', validators=[DataRequired(), NumberRange(min=0)], places=2)
    overtime_rate = DecimalField('Taux heures sup.', validators=[DataRequired(), NumberRange(min=0)], places=2)
    
    # Déductions
    tax_deductions = DecimalField('Déductions fiscales', validators=[Optional(), NumberRange(min=0)], places=2, default=0)
    social_security_deductions = DecimalField('Sécurité sociale', validators=[Optional(), NumberRange(min=0)], places=2, default=0)
    other_deductions = DecimalField('Autres déductions', validators=[Optional(), NumberRange(min=0)], places=2, default=0)
    total_deductions = DecimalField('Total déductions', validators=[Optional(), NumberRange(min=0)], places=2, default=0)

    # Calculs
    gross_pay = DecimalField('Salaire brut', validators=[Optional(), NumberRange(min=0)], places=2, default=0)
    net_pay = DecimalField('Salaire net', validators=[Optional(), NumberRange(min=0)], places=2, default=0)

    # Statut
    is_processed = BooleanField('Fiche traitée', default=False)

    notes = TextAreaField('Notes', validators=[Optional()])
    
    submit = SubmitField('Enregistrer la paie')
    
    def validate_pay_period_end(self, field):
        """Valide que la fin de période est après le début"""
        if self.pay_period_start.data and field.data and field.data < self.pay_period_start.data:
            raise ValidationError('La fin de période doit être après le début de période.')

class PerformanceForm(FlaskForm):
    """Formulaire pour l'évaluation des performances"""

    evaluation_date = DateField('Date d\'évaluation', validators=[DataRequired()], default=date.today)
    evaluation_period_start = DateField('Début de période d\'évaluation', validators=[DataRequired()])
    evaluation_period_end = DateField('Fin de période d\'évaluation', validators=[DataRequired()])

    # Critères d'évaluation (1-5)
    punctuality = IntegerField('Ponctualité', validators=[DataRequired(), NumberRange(min=1, max=5)], default=3)
    quality_of_work = IntegerField('Qualité du travail', validators=[DataRequired(), NumberRange(min=1, max=5)], default=3)
    teamwork = IntegerField('Travail d\'équipe', validators=[DataRequired(), NumberRange(min=1, max=5)], default=3)
    communication = IntegerField('Communication', validators=[DataRequired(), NumberRange(min=1, max=5)], default=3)
    initiative = IntegerField('Initiative', validators=[DataRequired(), NumberRange(min=1, max=5)], default=3)
    customer_service = IntegerField('Service client', validators=[DataRequired(), NumberRange(min=1, max=5)], default=3)
    overall_score = DecimalField('Score global', validators=[Optional(), NumberRange(min=1, max=5)], places=1, default=3.0)

    # Commentaires
    strengths = TextAreaField('Points forts', validators=[Optional()])
    areas_for_improvement = TextAreaField('Axes d\'amélioration', validators=[Optional()])
    goals = TextAreaField('Objectifs pour la prochaine période', validators=[Optional()])
    comments = TextAreaField('Commentaires généraux', validators=[Optional()])

    # Statut
    is_finalized = BooleanField('Évaluation finalisée', default=False)
    employee_acknowledged = BooleanField('Employé a pris connaissance', default=False)

    submit = SubmitField('Enregistrer l\'évaluation')

    def validate_evaluation_period_end(self, field):
        """Valide que la fin de période est après le début"""
        if self.evaluation_period_start.data and field.data and field.data < self.evaluation_period_start.data:
            raise ValidationError('La fin de période d\'évaluation doit être après le début.')

class EmployeeDocumentForm(FlaskForm):
    """Formulaire pour uploader des documents d'employé"""
    
    document_name = StringField('Nom du document', validators=[DataRequired(), Length(max=200)])
    document_type = SelectField('Type de document',
                               choices=[
                                   ('CV', 'CV'),
                                   ('Contract', 'Contrat'),
                                   ('Certificate', 'Certificat'),
                                   ('ID_Copy', 'Copie pièce d\'identité'),
                                   ('Diploma', 'Diplôme'),
                                   ('Medical', 'Document médical'),
                                   ('Other', 'Autre')
                               ],
                               validators=[DataRequired()])
    file = FileField('Fichier', validators=[
        DataRequired(),
        FileAllowed(['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'], 'Seuls les fichiers PDF, Word et images sont autorisés.')
    ])
    expiry_date = DateField('Date d\'expiration', validators=[Optional()])
    description = TextAreaField('Description', validators=[Optional()])
    is_confidential = BooleanField('Document confidentiel', default=False)
    
    submit = SubmitField('Uploader le document')

# Formulaires de recherche et filtrage
class EmployeeSearchForm(FlaskForm):
    """Formulaire de recherche d'employés"""
    
    search = StringField('Rechercher', validators=[Optional()])
    status = SelectField('Statut',
                        choices=[('', 'Tous')] + [(status.value, status.value.replace('_', ' ').title()) for status in EmployeeStatus],
                        validators=[Optional()])
    department = StringField('Département', validators=[Optional()])
    position = StringField('Poste', validators=[Optional()])
    
    submit = SubmitField('Rechercher')
