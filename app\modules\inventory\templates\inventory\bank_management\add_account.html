{% extends "base.html" %}

{% block title %}Nouveau Compte Bancaire{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-2">
                <h4 class="mb-0">
                    <i class="fas fa-plus-circle"></i> Nouveau Compte Bancaire
                </h4>
                <a href="{{ url_for('inventory.bank_management_index') }}" class="btn btn-light btn-sm">
                    <i class="fas fa-arrow-left"></i> Retour
                </a>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-university"></i> Informations du Compte</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            {{ form.hidden_tag() }}
                            
                            <!-- Informations de base -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.name.label }}</label>
                                    {{ form.name(class="form-control") }}
                                    {% if form.name.errors %}
                                        <div class="text-danger">
                                            {% for error in form.name.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.currency.label }}</label>
                                    {{ form.currency(class="form-select") }}
                                </div>
                            </div>

                            <!-- Informations bancaires -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.account_number.label }}</label>
                                    {{ form.account_number(class="form-control") }}
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.bank_name.label }}</label>
                                    {{ form.bank_name(class="form-control") }}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.iban.label }}</label>
                                    {{ form.iban(class="form-control", placeholder="FR76 1234 5678 9012 3456 7890 123") }}
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.bic.label }}</label>
                                    {{ form.bic(class="form-control", placeholder="BNPAFRPP") }}
                                </div>
                            </div>

                            <!-- Adresse de la banque -->
                            <div class="mb-3">
                                <label class="form-label">{{ form.bank_address.label }}</label>
                                {{ form.bank_address(class="form-control", rows="3") }}
                            </div>

                            <!-- Soldes et limites -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.initial_balance.label }}</label>
                                    <div class="input-group">
                                        {{ form.initial_balance(class="form-control", step="0.01") }}
                                        <span class="input-group-text">€</span>
                                    </div>
                                    <small class="form-text text-muted">
                                        Solde actuel du compte (sera enregistré comme opération d'ouverture)
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.overdraft_limit.label }}</label>
                                    <div class="input-group">
                                        {{ form.overdraft_limit(class="form-control", step="0.01") }}
                                        <span class="input-group-text">€</span>
                                    </div>
                                    <small class="form-text text-muted">
                                        Montant du découvert autorisé
                                    </small>
                                </div>
                            </div>

                            <!-- Options -->
                            <div class="mb-3">
                                <div class="form-check">
                                    {{ form.is_default(class="form-check-input") }}
                                    <label class="form-check-label" for="{{ form.is_default.id }}">
                                        {{ form.is_default.label.text }}
                                    </label>
                                    <small class="form-text text-muted d-block">
                                        Le compte par défaut sera utilisé automatiquement pour les paiements bancaires
                                    </small>
                                </div>
                            </div>

                            <!-- Description et notes -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.description.label }}</label>
                                    {{ form.description(class="form-control", rows="3") }}
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.notes.label }}</label>
                                    {{ form.notes(class="form-control", rows="3") }}
                                </div>
                            </div>

                            <!-- Boutons -->
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('inventory.bank_management_index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> Créer le compte
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Aide -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-info-circle"></i> Aide</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>IBAN</h6>
                                <p class="small text-muted">
                                    L'IBAN (International Bank Account Number) est un identifiant international 
                                    de compte bancaire. Format français : FR76 suivi de 23 chiffres.
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h6>BIC/SWIFT</h6>
                                <p class="small text-muted">
                                    Le BIC (Bank Identifier Code) ou code SWIFT identifie votre banque. 
                                    Il est composé de 8 ou 11 caractères alphanumériques.
                                </p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Solde initial</h6>
                                <p class="small text-muted">
                                    Indiquez le solde actuel de votre compte. Une opération d'ouverture 
                                    sera automatiquement créée si le montant est supérieur à 0.
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h6>Découvert autorisé</h6>
                                <p class="small text-muted">
                                    Montant maximum que vous pouvez dépenser au-delà de votre solde. 
                                    Laissez à 0 si aucun découvert n'est autorisé.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation IBAN en temps réel
    const ibanInput = document.getElementById('iban');
    if (ibanInput) {
        ibanInput.addEventListener('input', function() {
            let value = this.value.replace(/\s/g, '').toUpperCase();
            // Ajouter des espaces tous les 4 caractères
            value = value.replace(/(.{4})/g, '$1 ').trim();
            this.value = value;
        });
    }

    // Validation BIC en temps réel
    const bicInput = document.getElementById('bic');
    if (bicInput) {
        bicInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    }

    // Formatage automatique des montants
    const amountInputs = document.querySelectorAll('input[type="number"][step="0.01"]');
    amountInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value) {
                this.value = parseFloat(this.value).toFixed(2);
            }
        });
    });
});
</script>
{% endblock %}
