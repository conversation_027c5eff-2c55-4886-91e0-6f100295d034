import pytest
from flask import session
from app.modules.auth.models import User, UserRole
from app.extensions import db

def test_login_page(client):
    """
    GIVEN a Flask application
    WHEN the '/login' page is requested (GET)
    THEN check the response is valid
    """
    response = client.get('/auth/login')
    assert response.status_code == 200
    assert b'Connexion' in response.data

def test_successful_login(client, app):
    """
    GIVEN a Flask application
    WHEN the '/login' page is posted to with valid credentials (POST)
    THEN check the response redirects to the main page
    """
    with app.app_context():
        # Create test user
        user = User(username='test_user', email='<EMAIL>', role=UserRole.MANAGER)
        user.set_password('test_password')
        db.session.add(user)
        db.session.commit()

    response = client.post('/auth/login', data={
        'username': 'test_user',
        'password': 'test_password'
    }, follow_redirects=True)

    assert response.status_code == 200
    with client.session_transaction() as sess:
        assert '_user_id' in sess

def test_failed_login(client):
    """
    GIVEN a Flask application
    WHEN the '/login' page is posted to with invalid credentials (POST)
    THEN check an error message is returned
    """
    response = client.post('/auth/login', data={
        'username': 'wrong_user',
        'password': 'wrong_password'
    }, follow_redirects=True)

    assert response.status_code == 200
    assert b'Nom d&#39;utilisateur ou mot de passe invalide' in response.data

@pytest.fixture
def auth_client(client, app):
    """Create an authenticated client."""
    with app.app_context():
        user = User(username='auth_user', email='<EMAIL>', role=UserRole.MANAGER)
        user.set_password('auth_password')
        db.session.add(user)
        db.session.commit()

    client.post('/auth/login', data={
        'username': 'auth_user',
        'password': 'auth_password'
    })
    return client

def test_logout(auth_client):
    """
    GIVEN an authenticated user
    WHEN the logout route is accessed
    THEN check the user is logged out
    """
    response = auth_client.get('/auth/logout', follow_redirects=True)
    assert response.status_code == 200
    with auth_client.session_transaction() as sess:
        assert '_user_id' not in sess

def test_password_change(auth_client, app):
    """
    GIVEN an authenticated user
    WHEN the password is changed
    THEN check the new password works
    """
    with app.app_context():
        response = auth_client.post('/profile', data={
            'username': 'auth_user',
            'email': '<EMAIL>',
            'current_password': 'auth_password',
            'new_password': 'new_password',
            'confirm_password': 'new_password'
        }, follow_redirects=True)
        assert response.status_code == 200
        assert b'Profil mis \xc3\xa0 jour avec succ\xc3\xa8s' in response.data

        # Try logging in with the new password
        auth_client.get('/auth/logout')
        response = auth_client.post('/auth/login', data={
            'username': 'auth_user',
            'password': 'new_password'
        }, follow_redirects=True)
        assert response.status_code == 200
        with auth_client.session_transaction() as sess:
            assert '_user_id' in sess

def test_protected_routes(client, auth_client):
    """
    GIVEN a Flask application
    WHEN protected routes are accessed
    THEN check authentication requirements
    """
    # Test unauthenticated access
    response = client.get('/admin/')
    assert response.status_code == 302  # Redirect to login
    
    # Follow the redirect to see the unauthorized message
    response = client.get('/admin/', follow_redirects=True)
    assert response.status_code == 200
    assert b'Acc\xc3\xa8s non autoris\xc3\xa9.' in response.data

    # Test authenticated access
    response = auth_client.get('/admin/')
    assert response.status_code == 302  # Redirect to main page due to insufficient permissions 