{% extends "online_ordering_sites/base.html" %}

{% block title %}Mes Commandes - {{ site.site_name }}{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-history"></i> Mes Commandes</h2>
        <div>
            <a href="{{ url_for('online_ordering_sites.customer_profile') }}" class="btn btn-outline-secondary">
                <i class="fas fa-user"></i> Mon Profil
            </a>
            <a href="{{ url_for('online_ordering_sites.menu') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouvelle Commande
            </a>
        </div>
    </div>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label for="statusFilter" class="form-label">Statut</label>
                    <select class="form-select" id="statusFilter" onchange="filterOrders()">
                        <option value="">Tous les statuts</option>
                        <option value="pending">En attente</option>
                        <option value="confirmed">Confirmée</option>
                        <option value="preparing">En préparation</option>
                        <option value="ready">Prête</option>
                        <option value="out_for_delivery">En livraison</option>
                        <option value="delivered">Livrée</option>
                        <option value="cancelled">Annulée</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="typeFilter" class="form-label">Type</label>
                    <select class="form-select" id="typeFilter" onchange="filterOrders()">
                        <option value="">Tous les types</option>
                        <option value="delivery">Livraison</option>
                        <option value="pickup">À emporter</option>
                        <option value="dine_in">Sur place</option>
                        <option value="drive_through">Drive</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="dateFilter" class="form-label">Période</label>
                    <select class="form-select" id="dateFilter" onchange="filterOrders()">
                        <option value="">Toutes les dates</option>
                        <option value="today">Aujourd'hui</option>
                        <option value="week">Cette semaine</option>
                        <option value="month">Ce mois</option>
                        <option value="year">Cette année</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="searchFilter" class="form-label">Recherche</label>
                    <input type="text" class="form-control" id="searchFilter" placeholder="N° commande..." onkeyup="filterOrders()">
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des commandes -->
    {% if orders.items %}
        <div class="row" id="ordersContainer">
            {% for order in orders.items %}
            <div class="col-md-6 col-lg-4 mb-4 order-card" 
                 data-status="{{ order.status.value }}" 
                 data-type="{{ order.order_type.value }}"
                 data-date="{{ order.ordered_at.strftime('%Y-%m-%d') }}"
                 data-search="{{ order.order_number }}">
                <div class="card h-100 shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">#{{ order.order_number }}</h6>
                        {% if order.status.value == 'pending' %}
                            <span class="badge bg-warning">En attente</span>
                        {% elif order.status.value == 'confirmed' %}
                            <span class="badge bg-info">Confirmée</span>
                        {% elif order.status.value == 'preparing' %}
                            <span class="badge bg-primary">En préparation</span>
                        {% elif order.status.value == 'ready' %}
                            <span class="badge bg-success">Prête</span>
                        {% elif order.status.value == 'out_for_delivery' %}
                            <span class="badge bg-warning">En livraison</span>
                        {% elif order.status.value == 'delivered' %}
                            <span class="badge bg-success">Livrée</span>
                        {% elif order.status.value == 'cancelled' %}
                            <span class="badge bg-danger">Annulée</span>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="text-muted">
                                {% if order.order_type.value == 'delivery' %}
                                    <i class="fas fa-truck"></i> Livraison
                                {% elif order.order_type.value == 'pickup' %}
                                    <i class="fas fa-shopping-bag"></i> À emporter
                                {% elif order.order_type.value == 'dine_in' %}
                                    <i class="fas fa-utensils"></i> Sur place
                                {% elif order.order_type.value == 'drive_through' %}
                                    <i class="fas fa-car"></i> Drive
                                {% endif %}
                            </small>
                            <strong class="text-primary">{{ "%.2f"|format(order.total_amount) }}€</strong>
                        </div>
                        
                        <p class="text-muted mb-2">
                            <i class="fas fa-calendar"></i> {{ order.ordered_at.strftime('%d/%m/%Y à %H:%M') }}
                        </p>
                        
                        <!-- Articles de la commande -->
                        <div class="mb-3">
                            <small class="text-muted">Articles:</small>
                            <ul class="list-unstyled mb-0">
                                {% for item in order.items[:3] %}
                                <li class="small">{{ item.quantity }}x {{ item.product.name }}</li>
                                {% endfor %}
                                {% if order.items.count() > 3 %}
                                <li class="small text-muted">... et {{ order.items.count() - 3 }} autre(s)</li>
                                {% endif %}
                            </ul>
                        </div>
                        
                        {% if order.customer_notes %}
                        <div class="mb-3">
                            <small class="text-muted">Note:</small>
                            <p class="small mb-0">{{ order.customer_notes[:50] }}{% if order.customer_notes|length > 50 %}...{% endif %}</p>
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-flex gap-2">
                            <a href="{{ url_for('online_ordering_sites.track_order', order_number=order.order_number) }}" 
                               class="btn btn-sm btn-outline-primary flex-fill">
                                <i class="fas fa-eye"></i> Suivre
                            </a>
                            {% if order.status.value in ['delivered'] %}
                            <button class="btn btn-sm btn-outline-success" onclick="reorderItems({{ order.id }})">
                                <i class="fas fa-redo"></i> Recommander
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if orders.pages > 1 %}
        <nav aria-label="Navigation des commandes">
            <ul class="pagination justify-content-center">
                {% if orders.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('online_ordering_sites.customer_orders', page=orders.prev_num) }}">Précédent</a>
                    </li>
                {% endif %}
                
                {% for page_num in orders.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != orders.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('online_ordering_sites.customer_orders', page=page_num) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if orders.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('online_ordering_sites.customer_orders', page=orders.next_num) }}">Suivant</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-4x text-muted mb-4"></i>
            <h4>Aucune commande trouvée</h4>
            <p class="text-muted">Vous n'avez pas encore passé de commande ou aucune commande ne correspond à vos critères.</p>
            <a href="{{ url_for('online_ordering_sites.menu') }}" class="btn btn-primary">
                <i class="fas fa-utensils"></i> Découvrir le menu
            </a>
        </div>
    {% endif %}
</div>

<script>
function filterOrders() {
    const statusFilter = document.getElementById('statusFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;
    const searchFilter = document.getElementById('searchFilter').value.toLowerCase();
    
    const orderCards = document.querySelectorAll('.order-card');
    const today = new Date();
    
    orderCards.forEach(card => {
        const status = card.dataset.status;
        const type = card.dataset.type;
        const orderDate = new Date(card.dataset.date);
        const search = card.dataset.search.toLowerCase();
        
        let show = true;
        
        // Filtre par statut
        if (statusFilter && status !== statusFilter) show = false;
        
        // Filtre par type
        if (typeFilter && type !== typeFilter) show = false;
        
        // Filtre par date
        if (dateFilter) {
            const daysDiff = Math.floor((today - orderDate) / (1000 * 60 * 60 * 24));
            switch(dateFilter) {
                case 'today':
                    if (daysDiff !== 0) show = false;
                    break;
                case 'week':
                    if (daysDiff > 7) show = false;
                    break;
                case 'month':
                    if (daysDiff > 30) show = false;
                    break;
                case 'year':
                    if (daysDiff > 365) show = false;
                    break;
            }
        }
        
        // Filtre par recherche
        if (searchFilter && !search.includes(searchFilter)) show = false;
        
        card.style.display = show ? '' : 'none';
    });
}

function reorderItems(orderId) {
    // Fonction pour recommander les mêmes articles
    if (confirm('Voulez-vous ajouter les articles de cette commande à votre panier ?')) {
        // Ici, on pourrait implémenter la logique pour ajouter les articles au panier
        alert('Fonctionnalité à implémenter : ajout des articles au panier');
    }
}
</script>
{% endblock %}
