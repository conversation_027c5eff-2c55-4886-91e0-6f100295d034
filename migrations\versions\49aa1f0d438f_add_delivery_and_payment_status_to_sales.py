"""Add delivery and payment status to sales

Revision ID: 49aa1f0d438f
Revises: 4b961301b40a
Create Date: 2025-07-18 07:10:49.139453

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '49aa1f0d438f'
down_revision = '4b961301b40a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('sales', schema=None) as batch_op:
        batch_op.add_column(sa.Column('delivery_status', sa.String(length=20), nullable=True))
        batch_op.add_column(sa.Column('payment_status', sa.String(length=20), nullable=True))
        batch_op.add_column(sa.Column('order_source', sa.String(length=20), nullable=True))
        batch_op.add_column(sa.Column('online_order_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('customer_user_id', sa.Integer(), nullable=True))
        batch_op.create_foreign_key('fk_sales_online_order_id', 'online_orders', ['online_order_id'], ['id'])
        batch_op.create_foreign_key('fk_sales_customer_user_id', 'customer_users', ['customer_user_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('sales', schema=None) as batch_op:
        batch_op.drop_constraint('fk_sales_customer_user_id', type_='foreignkey')
        batch_op.drop_constraint('fk_sales_online_order_id', type_='foreignkey')
        batch_op.drop_column('customer_user_id')
        batch_op.drop_column('online_order_id')
        batch_op.drop_column('order_source')
        batch_op.drop_column('payment_status')
        batch_op.drop_column('delivery_status')

    # ### end Alembic commands ###
