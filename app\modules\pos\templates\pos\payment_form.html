{% extends "base.html" %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Paiement de la vente #{{ sale.reference }}</h5>
                </div>
                <div class="card-body">
                    <!-- Résumé de la vente -->
                    <div class="mb-4">
                        <h6 class="fw-bold">Résumé de la vente</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <tbody>
                                    <tr>
                                        <th style="width: 150px">Total HT:</th>
                                        <td>{{ "%.2f"|format(sale.total_ht) }} €</td>
                                    </tr>
                                    <tr>
                                        <th>TVA ({{ "%.1f"|format(sale.tax_rate) }}%):</th>
                                        <td>{{ "%.2f"|format(sale.tax_amount) }} €</td>
                                    </tr>
                                    {% if sale.discount_amount > 0 %}
                                    <tr>
                                        <th>Réduction:</th>
                                        <td>-{{ "%.2f"|format(sale.discount_amount) }} €</td>
                                    </tr>
                                    {% endif %}
                                    <tr class="table-primary">
                                        <th>Total TTC:</th>
                                        <td class="fw-bold">{{ "%.2f"|format(sale.total_ttc) }} €</td>
                                    </tr>
                                    {% if sale.total_paid > 0 %}
                                    <tr>
                                        <th>Déjà payé:</th>
                                        <td>{{ "%.2f"|format(sale.total_paid) }} €</td>
                                    </tr>
                                    <tr class="table-success">
                                        <th>Reste à payer:</th>
                                        <td class="fw-bold">{{ "%.2f"|format(sale.total_ttc - sale.total_paid) }} €</td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Boutons de paiement -->
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success btn-lg" onclick="openPaymentModal()">
                            <i class="fas fa-credit-card me-2"></i>Procéder au paiement
                        </button>
                        <a href="{{ url_for('pos.sales') }}" class="btn btn-outline-secondary">Retour aux ventes</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de paiement -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-credit-card me-2"></i>
                    Paiement - Vente #{{ sale.reference }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="paymentTotal" class="form-label">Total à encaisser</label>
                    <h4 class="text-success">
                        <i class="fas fa-euro-sign me-2"></i>
                        <span id="paymentTotal">{{ "%.2f"|format(sale.remaining_amount if sale.remaining_amount > 0 else sale.total) }} €</span>
                    </h4>
                </div>
                <div class="mb-3">
                    <label class="form-label d-block">Choisissez la méthode de paiement</label>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-lg btn-success payment-method" data-method="cash">
                            <i class="fas fa-money-bill-wave me-2"></i> Espèces
                        </button>
                        <button type="button" class="btn btn-lg btn-primary payment-method" data-method="card">
                            <i class="fas fa-credit-card me-2"></i> Carte bancaire
                        </button>
                        <button type="button" class="btn btn-lg btn-info payment-method" data-method="check">
                            <i class="fas fa-money-check me-2"></i> Chèque
                        </button>
                        <button type="button" class="btn btn-lg btn-warning payment-method" data-method="transfer">
                            <i class="fas fa-exchange-alt me-2"></i> Virement
                        </button>
                        <button type="button" class="btn btn-lg btn-secondary payment-method" data-method="other">
                            <i class="fas fa-ellipsis-h me-2"></i> Autre
                        </button>
                    </div>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Information :</strong> Le paiement sera traité immédiatement et le stock sera mis à jour.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block head %}
{{ super() }}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
// Variables globales
let saleId = {{ sale.id }};
let saleTotal = {{ sale.remaining_amount if sale.remaining_amount > 0 else sale.total }};

// Fonction pour ouvrir la modale de paiement
function openPaymentModal() {
    const paymentModal = new bootstrap.Modal(document.getElementById('paymentModal'));
    paymentModal.show();
}

// Fonction pour traiter le paiement
function processFormPayment(method) {
    // Fermer la modale
    const paymentModal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
    if (paymentModal) {
        paymentModal.hide();
    }

    // Créer un formulaire pour soumettre le paiement
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ url_for("pos.payment_form", sale_id=sale.id) }}';

    // Ajouter le token CSRF
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = csrfToken.content;
        form.appendChild(csrfInput);
    }

    // Ajouter la méthode de paiement
    const methodInput = document.createElement('input');
    methodInput.type = 'hidden';
    methodInput.name = 'method';
    methodInput.value = method.toUpperCase(); // Convertir en majuscules pour correspondre aux enum
    form.appendChild(methodInput);

    // Ajouter le montant
    const amountInput = document.createElement('input');
    amountInput.type = 'hidden';
    amountInput.name = 'amount';
    amountInput.value = saleTotal;
    form.appendChild(amountInput);

    // Soumettre le formulaire
    document.body.appendChild(form);
    form.submit();
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    // Attacher les événements aux boutons de paiement
    document.querySelectorAll('.payment-method').forEach(btn => {
        btn.addEventListener('click', function() {
            const method = this.dataset.method;
            processFormPayment(method);
        });
    });

    // Ouvrir automatiquement la modale au chargement de la page
    openPaymentModal();
});
</script>
{% endblock %}