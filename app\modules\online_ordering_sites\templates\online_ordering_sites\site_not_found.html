<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Site non trouvé</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .error-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body>
    <div class="error-page">
        <div class="container text-center">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <i class="fas fa-store-slash fa-5x mb-4 opacity-75"></i>
                    <h1 class="display-4 fw-bold mb-3">Site non trouvé</h1>
                    <p class="lead mb-4">
                        Le restaurant que vous recherchez n'existe pas ou n'a pas encore activé son site de commande en ligne.
                    </p>
                    
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <a href="http://all_businesses.lvh.me:5000" class="btn btn-light btn-lg">
                            <i class="fas fa-store me-2"></i>Voir tous les restaurants
                        </a>
                        <a href="{{ url_for('main.index') }}" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-home me-2"></i>Retour à l'accueil
                        </a>
                    </div>
                    
                    <div class="mt-5">
                        <p class="text-white-50">
                            <i class="fas fa-info-circle me-1"></i>
                            Vous êtes propriétaire d'un restaurant ? 
                            <a href="{{ url_for('auth.register') }}" class="text-white">Inscrivez-vous ici</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
