{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="fas fa-shopping-cart"></i> {{ title }}
        </h2>
        <a href="{{ url_for('pos.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour au POS
        </a>
    </div>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET">
                <!-- Première ligne de filtres -->
                <div class="row g-3 mb-3">
                    <div class="col-md-2">
                        <label class="form-label">Statut</label>
                        <select name="status" class="form-select">
                            <option value="">Tous les statuts</option>
                            {% for status_value, status_label in statuses %}
                            <option value="{{ status_value }}" {% if status == status_value %}selected{% endif %}>
                                {% if status_value == 'pending' %}En attente
                                {% elif status_value == 'kitchen_pending' %}Cuisine en attente
                                {% elif status_value == 'kitchen_ready' %}Cuisine prêt
                                {% elif status_value == 'delivered' %}Servi
                                {% elif status_value == 'paid' %}Payé
                                {% elif status_value == 'cancelled' %}Annulé
                                {% elif status_value == 'completed' %}Terminé
                                {% elif status_value == 'voided' %}Annulé (void)
                                {% elif status_value == 'ready' %}Prêt
                                {% else %}{{ status_value|title }}
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Utilisateur</label>
                        <select name="user_id" class="form-select">
                            <option value="">Tous</option>
                            {% for user in users %}
                            <option value="{{ user.id }}" {% if user_id and user.id == user_id|int %}selected{% endif %}>
                                {{ user.username }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Salle</label>
                        <select name="room_id" class="form-select">
                            <option value="">Toutes</option>
                            {% for room in rooms %}
                            <option value="{{ room.id }}" {% if room_id and room.id == room_id|int %}selected{% endif %}>
                                {{ room.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Type de service</label>
                        <select name="service_type" class="form-select">
                            <option value="">Tous les types</option>
                            {% for service_value, service_label in service_types %}
                            <option value="{{ service_value }}" {% if service_type == service_value %}selected{% endif %}>
                                {{ service_label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Source</label>
                        <select name="order_source" class="form-select">
                            <option value="all" {% if order_source == 'all' %}selected{% endif %}>Toutes</option>
                            <option value="pos" {% if order_source == 'pos' %}selected{% endif %}>POS</option>
                            <option value="online" {% if order_source == 'online' %}selected{% endif %}>En ligne</option>
                        </select>
                    </div>
                </div>

                <!-- Deuxième ligne de filtres -->
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Date début</label>
                        <input type="date" name="date_from" class="form-control" value="{{ date_from.strftime('%Y-%m-%d') if date_from }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Date fin</label>
                        <input type="date" name="date_to" class="form-control" value="{{ date_to.strftime('%Y-%m-%d') if date_to }}">
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> Filtrer
                        </button>
                        <a href="{{ url_for('pos.sales') }}" class="btn btn-secondary ms-2">
                            <i class="fas fa-times"></i> Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des ventes -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Source</th>
                            <th>Référence</th>
                            <th>Date</th>
                            <th>Utilisateur</th>
                            <th>Client</th>
                            <th>Table/Salle</th>
                            <th>Type de service</th>
                            <th>Total</th>
                            <th>Statut Livraison</th>
                            <th>Statut Paiement</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Liste unifiée des commandes -->
                        {% for order in all_orders %}
                        <tr>
                            <!-- Source -->
                            <td>
                                {% if order.type == 'pos' %}
                                    <span class="badge bg-primary">POS</span>
                                {% else %}
                                    <span class="badge bg-success">En ligne</span>
                                {% endif %}
                            </td>

                            <!-- Référence -->
                            <td>{{ order.reference }}</td>

                            <!-- Date -->
                            <td>{{ order.created_at.strftime('%d/%m/%Y %H:%M') }}</td>

                            <!-- Utilisateur POS -->
                            <td>
                                <span class="badge bg-info">{{ order.customer_name }}</span>
                            </td>

                            <!-- Client -->
                            <td>{{ order.client_name }}</td>

                            <!-- Table/Salle -->
                            <td>
                                {% if order.type == 'pos' and order.data.table %}
                                    <strong>Table {{ order.data.table.number }}</strong>
                                    {% if order.data.table.room %}
                                        <br><small class="text-muted">{{ order.data.table.room.name }}</small>
                                    {% elif order.data.table.location %}
                                        <br><small class="text-muted">({{ order.data.table.location }})</small>
                                    {% endif %}
                                {% elif order.type == 'online' %}
                                    {% if order.data.order_type.value == 'delivery' %}
                                        <span class="text-info">Livraison</span>
                                        {% if order.data.delivery_address %}
                                            <br><small class="text-muted">{{ order.data.delivery_address[:30] }}...</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">{{ order.data.order_type.value.replace('_', ' ').title() }}</span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">Non attribuée</span>
                                {% endif %}
                            </td>

                            <!-- Type de service -->
                            <td>
                                {% if order.type == 'pos' and order.data.service_type %}
                                    {% if order.data.service_type == 'dine_in' %}
                                        <span class="badge bg-primary"><i class="fas fa-utensils"></i> Sur place</span>
                                    {% elif order.data.service_type == 'takeaway' %}
                                        <span class="badge bg-warning"><i class="fas fa-shopping-bag"></i> À emporter</span>
                                    {% elif order.data.service_type == 'delivery' %}
                                        <span class="badge bg-info"><i class="fas fa-truck"></i> Livraison</span>
                                    {% elif order.data.service_type == 'drive_thru' %}
                                        <span class="badge bg-success"><i class="fas fa-car"></i> Service au volant</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ order.data.service_type }}</span>
                                    {% endif %}
                                {% elif order.type == 'online' %}
                                    {% if order.data.order_type.value == 'delivery' %}
                                        <span class="badge bg-info"><i class="fas fa-truck"></i> Livraison</span>
                                    {% elif order.data.order_type.value == 'pickup' %}
                                        <span class="badge bg-warning"><i class="fas fa-shopping-bag"></i> À emporter</span>
                                    {% elif order.data.order_type.value == 'dine_in' %}
                                        <span class="badge bg-primary"><i class="fas fa-utensils"></i> Sur place</span>
                                    {% elif order.data.order_type.value == 'drive_through' %}
                                        <span class="badge bg-success"><i class="fas fa-car"></i> Drive</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ order.data.order_type.value }}</span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">Non défini</span>
                                {% endif %}
                            </td>

                            <!-- Total -->
                            <td>{{ "%.2f"|format(order.total) }} €</td>

                            <!-- Statut Livraison -->
                            <td>
                                {% if order.delivery_status == 'delivered' %}
                                    <span class="badge bg-success">Livré</span>
                                {% elif order.delivery_status == 'served' %}
                                    <span class="badge bg-success">Servi</span>
                                {% else %}
                                    <span class="badge bg-warning">En attente</span>
                                {% endif %}
                            </td>

                            <!-- Statut Paiement -->
                            <td>
                                {% if order.payment_status == 'paid' %}
                                    {% if order.payment_status_display %}
                                        <span class="badge bg-success">{{ order.payment_status_display }}</span>
                                    {% else %}
                                        <span class="badge bg-success">Payé</span>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-warning">En attente</span>
                                {% endif %}
                            </td>

                            <!-- Actions -->
                            <td>
                                <div class="btn-group btn-group-sm">
                                    {% if order.type == 'pos' %}
                                        <a href="{{ url_for('pos.sale_details', id=order.data.id) }}"
                                           class="btn btn-info" title="Voir détails">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    {% else %}
                                        <a href="{{ url_for('online_ordering_sites.admin_orders') }}"
                                           class="btn btn-info" title="Voir dans gestion commandes">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    {% endif %}

                                    <!-- Boutons de paiement pour commandes en ligne -->
                                    {% if order.type == 'online' and order.data.payment_method.value == 'cash_on_delivery' and order.payment_status == 'pending' %}
                                        <button class="btn btn-success" onclick="processOnlineOrderPayment({{ order.data.id }})" title="Traiter le paiement">
                                            <i class="fas fa-credit-card"></i>
                                        </button>
                                    {% endif %}

                                    <!-- Bouton marquer comme livré -->
                                    {% if order.delivery_status == 'pending' %}
                                        <button class="btn btn-primary" onclick="markAsDelivered('{{ order.type }}', {{ order.data.id }})" title="Marquer comme livré">
                                            <i class="fas fa-truck"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}

                        {% if not all_orders %}
                        <tr>
                            <td colspan="11" class="text-center">Aucune commande trouvée</td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if sales.pages > 1 %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% for page in range(1, sales.pages + 1) %}
                    <li class="page-item {% if page == sales.page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('pos.sales',
                            page=page,
                            status=status,
                            user_id=user_id,
                            room_id=room_id,
                            service_type=service_type,
                            date_from=date_from.strftime('%Y-%m-%d') if date_from,
                            date_to=date_to.strftime('%Y-%m-%d') if date_to) }}">
                            {{ page }}
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal pour traiter le paiement des commandes en ligne -->
<div class="modal fade" id="onlinePaymentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-credit-card"></i> Traitement du Paiement - Commande en ligne
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Détails de la commande -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-receipt"></i> Détails de la commande</h6>
                    </div>
                    <div class="card-body" id="order-details">
                        <!-- Chargement des détails via JavaScript -->
                    </div>
                </div>

                <!-- Sélection du mode de paiement -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-money-bill-wave"></i> Choisissez la méthode de paiement</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-md-4">
                                <button type="button" class="btn btn-outline-success w-100 payment-method-btn" data-method="cash">
                                    <i class="fas fa-money-bill-wave fa-2x d-block mb-2"></i>
                                    <strong>Espèces</strong>
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button type="button" class="btn btn-outline-primary w-100 payment-method-btn" data-method="card">
                                    <i class="fas fa-credit-card fa-2x d-block mb-2"></i>
                                    <strong>Carte bancaire</strong>
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button type="button" class="btn btn-outline-info w-100 payment-method-btn" data-method="check">
                                    <i class="fas fa-money-check fa-2x d-block mb-2"></i>
                                    <strong>Chèque</strong>
                                </button>
                            </div>
                        </div>
                        <div class="row g-2 mt-2">
                            <div class="col-md-4">
                                <button type="button" class="btn btn-outline-warning w-100 payment-method-btn" data-method="transfer">
                                    <i class="fas fa-exchange-alt fa-2x d-block mb-2"></i>
                                    <strong>Virement</strong>
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button type="button" class="btn btn-outline-secondary w-100 payment-method-btn" data-method="other">
                                    <i class="fas fa-ellipsis-h fa-2x d-block mb-2"></i>
                                    <strong>Autre</strong>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Détails du paiement (affiché seulement pour espèces) -->
                <div class="card mt-3" id="cash-details" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-calculator"></i> Détails du paiement</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="amount-received" class="form-label">Montant reçu</label>
                                <input type="number" class="form-control" id="amount-received" step="0.01" placeholder="0.00">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Total à payer</label>
                                <input type="text" class="form-control" id="total-amount" readonly>
                            </div>
                        </div>
                        <div id="change-amount" class="alert alert-info mt-3" style="display: none;">
                            <i class="fas fa-coins"></i> <strong>Monnaie à rendre : <span id="change-value">0.00</span>€</strong>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Annuler
                </button>
                <button type="button" class="btn btn-success" id="confirm-payment" disabled>
                    <i class="fas fa-check"></i> Confirmer le paiement
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentOrderId = null;
let orderTotal = 0;
let selectedPaymentMethod = null;

function processOnlineOrderPayment(orderId) {
    currentOrderId = orderId;

    // Récupérer les détails de la commande
    fetch(`/pos/get_online_order_details/${orderId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Afficher les détails de la commande
            let orderHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>Commande #${data.order.order_number}</strong><br>
                        <small class="text-muted">Client: ${data.order.customer_name}</small>
                    </div>
                    <div class="col-md-6 text-end">
                        <h5 class="text-primary">Total: ${data.order.total_amount.toFixed(2)}€</h5>
                    </div>
                </div>
                <hr>
                <div class="row">
                    ${data.order.items.map(item => `
                        <div class="col-12 mb-2">
                            <div class="d-flex justify-content-between">
                                <span>${item.product_name} x${item.quantity}</span>
                                <span>${item.total_price.toFixed(2)}€</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;

            document.getElementById('order-details').innerHTML = orderHtml;
            orderTotal = data.order.total_amount;
            document.getElementById('total-amount').value = orderTotal.toFixed(2) + '€';

            const modal = new bootstrap.Modal(document.getElementById('onlinePaymentModal'));
            modal.show();
        } else {
            alert('Erreur lors du chargement des détails de la commande');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Erreur de connexion');
    });
}

// Gestionnaires pour les boutons de méthode de paiement
document.addEventListener('DOMContentLoaded', function() {
    const paymentButtons = document.querySelectorAll('.payment-method-btn');
    const confirmButton = document.getElementById('confirm-payment');
    const cashDetails = document.getElementById('cash-details');
    const amountReceived = document.getElementById('amount-received');

    paymentButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Retirer la sélection des autres boutons
            paymentButtons.forEach(btn => btn.classList.remove('btn-success', 'btn-outline-success', 'btn-outline-primary', 'btn-outline-info', 'btn-outline-warning', 'btn-outline-secondary'));
            paymentButtons.forEach(btn => {
                const method = btn.dataset.method;
                if (method === 'cash') btn.className = 'btn btn-outline-success w-100 payment-method-btn';
                else if (method === 'card') btn.className = 'btn btn-outline-primary w-100 payment-method-btn';
                else if (method === 'check') btn.className = 'btn btn-outline-info w-100 payment-method-btn';
                else if (method === 'transfer') btn.className = 'btn btn-outline-warning w-100 payment-method-btn';
                else btn.className = 'btn btn-outline-secondary w-100 payment-method-btn';
            });

            // Marquer le bouton sélectionné
            this.classList.remove('btn-outline-success', 'btn-outline-primary', 'btn-outline-info', 'btn-outline-warning', 'btn-outline-secondary');
            this.classList.add('btn-success');

            selectedPaymentMethod = this.dataset.method;

            // Afficher les détails pour espèces
            if (selectedPaymentMethod === 'cash') {
                cashDetails.style.display = 'block';
                amountReceived.value = orderTotal.toFixed(2);
            } else {
                cashDetails.style.display = 'none';
            }

            // Activer le bouton de confirmation
            confirmButton.disabled = false;
        });
    });

    // Calculer la monnaie à rendre
    if (amountReceived) {
        amountReceived.addEventListener('input', function() {
            const received = parseFloat(this.value) || 0;
            const change = received - orderTotal;

            const changeDiv = document.getElementById('change-amount');
            const changeValue = document.getElementById('change-value');

            if (change >= 0) {
                changeValue.textContent = change.toFixed(2);
                changeDiv.style.display = 'block';
            } else {
                changeDiv.style.display = 'none';
            }
        });
    }
});

// Confirmer le paiement
document.getElementById('confirm-payment').addEventListener('click', function() {
    if (!selectedPaymentMethod) {
        alert('Veuillez sélectionner une méthode de paiement');
        return;
    }

    let amountReceived = orderTotal;
    if (selectedPaymentMethod === 'cash') {
        amountReceived = parseFloat(document.getElementById('amount-received').value);
        if (amountReceived < orderTotal) {
            alert('Le montant reçu est insuffisant');
            return;
        }
    }

    // Traiter le paiement
    fetch(`/pos/process_online_order_payment`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            order_id: currentOrderId,
            payment_method: selectedPaymentMethod,
            amount_received: amountReceived
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            alert('Paiement traité avec succès');
            location.reload();
        } else {
            alert('Erreur: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Erreur de connexion');
    });
});

// Fonction pour marquer comme livré
function markAsDelivered(orderType, orderId) {
    if (!confirm('Marquer cette commande comme livrée ?')) {
        return;
    }

    let url;
    if (orderType === 'online') {
        url = `/pos/mark_online_order_delivered/${orderId}`;
    } else {
        // Pour les ventes POS, on peut ajouter une route similaire
        url = `/pos/sales/${orderId}/mark_delivered`;
    }

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            status: 'delivered'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            alert('Commande marquée comme livrée');
            location.reload();
        } else {
            alert('Erreur: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Erreur de connexion');
    });
}
</script>
{% endblock %}