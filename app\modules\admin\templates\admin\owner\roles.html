{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Gestion des Rôles</h1>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newRoleModal">
            <i class="fas fa-plus fa-sm"></i> Nouveau rôle
        </button>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Roles Table Card -->
        <div class="col-xl-8 col-lg-7 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Rôles</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Description</th>
                                    <th>Utilisateurs</th>
                                    <th>Permissions</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Administrateur</td>
                                    <td>Accès complet au système</td>
                                    <td>3</td>
                                    <td>Toutes</td>
                                    <td>
                                        <button class="btn btn-sm btn-info" title="Éditer">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Manager</td>
                                    <td>Gestion des opérations quotidiennes</td>
                                    <td>5</td>
                                    <td>Limitées</td>
                                    <td>
                                        <button class="btn btn-sm btn-info" title="Éditer">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role Stats Card -->
        <div class="col-xl-4 col-lg-5 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Statistiques</h6>
                </div>
                <div class="card-body">
                    <!-- Total Roles -->
                    <div class="mb-4">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total des rôles</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">5</div>
                    </div>

                    <!-- Users Distribution -->
                    <div class="mb-4">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Distribution des utilisateurs</div>
                        <div class="small">
                            <div class="d-flex justify-content-between">
                                <span>Administrateur</span>
                                <span>30%</span>
                            </div>
                            <div class="progress progress-sm mb-2">
                                <div class="progress-bar bg-primary" style="width: 30%"></div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Manager</span>
                                <span>50%</span>
                            </div>
                            <div class="progress progress-sm mb-2">
                                <div class="progress-bar bg-success" style="width: 50%"></div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Employé</span>
                                <span>20%</span>
                            </div>
                            <div class="progress progress-sm">
                                <div class="progress-bar bg-info" style="width: 20%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Role Modal -->
<div class="modal fade" id="newRoleModal" tabindex="-1" aria-labelledby="newRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newRoleModalLabel">Nouveau rôle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="roleName" class="form-label">Nom du rôle</label>
                        <input type="text" class="form-control" id="roleName" required>
                    </div>
                    <div class="mb-3">
                        <label for="roleDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="roleDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Permissions</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="permUsers">
                                    <label class="form-check-label" for="permUsers">
                                        Gestion des utilisateurs
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="permProducts">
                                    <label class="form-check-label" for="permProducts">
                                        Gestion des produits
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="permOrders">
                                    <label class="form-check-label" for="permOrders">
                                        Gestion des commandes
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="permInventory">
                                    <label class="form-check-label" for="permInventory">
                                        Gestion du stock
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="permReports">
                                    <label class="form-check-label" for="permReports">
                                        Accès aux rapports
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="permSettings">
                                    <label class="form-check-label" for="permSettings">
                                        Configuration système
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary">Créer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add your JavaScript code here
});
</script>
{% endblock %} 