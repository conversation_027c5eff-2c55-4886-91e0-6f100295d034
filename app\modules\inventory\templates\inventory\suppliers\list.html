{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-truck"></i> Fournisseurs</h1>
        <div>
            <a href="{{ url_for('inventory.supplier_categories') }}" class="btn btn-outline-info me-2">
                <i class="fas fa-tags"></i> Catégories
            </a>
            <a href="{{ url_for('inventory.pending_supplier_contacts') }}" class="btn btn-outline-warning me-2">
                <i class="fas fa-clock"></i> Contacts en attente
            </a>
            <a href="{{ url_for('inventory.add_supplier') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouveau Fournisseur
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Nom</th>
                            <th>Catégorie</th>
                            <th>Contact</th>
                            <th>Email</th>
                            <th>Téléphone</th>
                            <th>Note</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for supplier in suppliers %}
                        <tr>
                            <td>{{ supplier.name }}</td>
                            <td>
                                {% if supplier.category %}
                                    <span class="badge" style="background-color: {{ supplier.category.color }};">
                                        <i class="{{ supplier.category.icon }}"></i> {{ supplier.category.name }}
                                    </span>
                                {% else %}
                                    <span class="text-muted">Sans catégorie</span>
                                {% endif %}
                            </td>
                            <td>{{ supplier.contact_name or '-' }}</td>
                            <td>{{ supplier.email or '-' }}</td>
                            <td>{{ supplier.phone or '-' }}</td>
                            <td>
                                {% if supplier.rating > 0 %}
                                    <span class="text-warning">{{ supplier.display_rating }}</span>
                                {% else %}
                                    <span class="text-muted">Non noté</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if supplier.is_active %}
                                <span class="badge bg-success">Actif</span>
                                {% else %}
                                <span class="badge bg-danger">Inactif</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('inventory.supplier_details', id=supplier.id) }}"
                                       class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('inventory.edit_supplier', id=supplier.id) }}"
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-danger"
                                            onclick="confirmDelete('{{ supplier.id }}', '{{ supplier.name }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <form id="delete-form-{{ supplier.id }}" 
                                      action="{{ url_for('inventory.delete_supplier', id=supplier.id) }}" 
                                      method="POST" 
                                      style="display: none;">
                                </form>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="6" class="text-center">Aucun fournisseur trouvé</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(id, name) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le fournisseur "${name}" ?`)) {
        document.getElementById(`delete-form-${id}`).submit();
    }
}
</script>
{% endblock %} 