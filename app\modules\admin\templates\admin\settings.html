{% extends "base.html" %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">{{ title }}</h1>
    
    <div class="row mt-4">
        <div class="col-xl-12">
            <!-- Paramètres généraux -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-cogs me-1"></i>
                    Paramètres généraux
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin.update_settings') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        
                        <!-- Informations de l'entreprise -->
                        <div class="mb-4">
                            <h5>Informations de l'entreprise</h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="businessName" class="form-label">Nom de l'entreprise</label>
                                    <input type="text" class="form-control" id="businessName" name="business_name"
                                           value="{{ settings.business_name if settings else '' }}" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="businessPhone" class="form-label">Téléphone</label>
                                    <input type="tel" class="form-control" id="businessPhone" name="business_phone"
                                           value="{{ settings.business_phone if settings else '' }}">
                                </div>
                                <div class="col-md-6">
                                    <label for="businessEmail" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="businessEmail" name="business_email"
                                           value="{{ settings.business_email if settings else '' }}">
                                </div>
                                <div class="col-md-6">
                                    <label for="businessAddress" class="form-label">Adresse</label>
                                    <input type="text" class="form-control" id="businessAddress" name="business_address"
                                           value="{{ settings.business_address if settings else '' }}">
                                </div>
                            </div>
                        </div>

                        <!-- Paramètres système -->
                        <div class="mb-4">
                            <h5>Paramètres système</h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="timezone" class="form-label">Fuseau horaire</label>
                                    <select class="form-select" id="timezone" name="timezone">
                                        <option value="Europe/Paris" {{ 'selected' if settings and settings.timezone == 'Europe/Paris' }}>Europe/Paris</option>
                                        <option value="UTC" {{ 'selected' if settings and settings.timezone == 'UTC' }}>UTC</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="currency" class="form-label">Devise</label>
                                    <select class="form-select" id="currency" name="currency">
                                        <option value="EUR" {{ 'selected' if settings and settings.currency == 'EUR' }}>Euro (€)</option>
                                        <option value="USD" {{ 'selected' if settings and settings.currency == 'USD' }}>Dollar ($)</option>
                                        <option value="GBP" {{ 'selected' if settings and settings.currency == 'GBP' }}>Livre Sterling (£)</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="dateFormat" class="form-label">Format de date</label>
                                    <select class="form-select" id="dateFormat" name="date_format">
                                        <option value="%d/%m/%Y" {{ 'selected' if settings and settings.date_format == '%d/%m/%Y' }}>DD/MM/YYYY</option>
                                        <option value="%Y-%m-%d" {{ 'selected' if settings and settings.date_format == '%Y-%m-%d' }}>YYYY-MM-DD</option>
                                        <option value="%m/%d/%Y" {{ 'selected' if settings and settings.date_format == '%m/%d/%Y' }}>MM/DD/YYYY</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="timeFormat" class="form-label">Format d'heure</label>
                                    <select class="form-select" id="timeFormat" name="time_format">
                                        <option value="%H:%M" {{ 'selected' if settings and settings.time_format == '%H:%M' }}>24h (HH:MM)</option>
                                        <option value="%I:%M %p" {{ 'selected' if settings and settings.time_format == '%I:%M %p' }}>12h (HH:MM AM/PM)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Paramètres de notification -->
                        <div class="mb-4">
                            <h5>Notifications</h5>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="emailNotifications" name="email_notifications"
                                       {{ 'checked' if settings and settings.email_notifications }}>
                                <label class="form-check-label" for="emailNotifications">
                                    Activer les notifications par email
                                </label>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="stockAlerts" name="stock_alerts"
                                       {{ 'checked' if settings and settings.stock_alerts }}>
                                <label class="form-check-label" for="stockAlerts">
                                    Alertes de stock bas
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Enregistrer les paramètres
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}