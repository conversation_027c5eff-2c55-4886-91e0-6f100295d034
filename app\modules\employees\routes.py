from flask import render_template, redirect, url_for, flash, request, jsonify, current_app, send_file
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app.modules.employees.models import (
    Employee, EmployeeProfile, WorkSchedule, Attendance, Payroll, Performance, EmployeeDocument,
    EmployeeStatus, AttendanceStatus
)
from app.modules.employees.forms import (
    EmployeeForm, EmployeeProfileForm, WorkScheduleForm, AttendanceForm,
    PayrollForm, PerformanceForm, EmployeeDocumentForm, EmployeeSearchForm
)
from app.modules.auth.models import User
from app import db
from app.utils.decorators import permission_required
from datetime import date, datetime, timedelta
import os
from . import bp

@bp.route('/')
@login_required
@permission_required('can_manage_employees')
def index():
    """Liste des employés avec recherche et filtrage"""
    page = request.args.get('page', 1, type=int)
    owner_id = current_user.get_owner_id

    form = EmployeeSearchForm()
    query = Employee.query.filter_by(owner_id=owner_id)

    # Filtrage par recherche
    if request.args.get('search'):
        search_term = request.args.get('search')
        query = query.filter(
            db.or_(
                Employee.first_name.contains(search_term),
                Employee.last_name.contains(search_term),
                Employee.employee_id.contains(search_term),
                Employee.email.contains(search_term)
            )
        )

    # Filtrage par statut
    if request.args.get('status'):
        query = query.filter_by(status=request.args.get('status'))

    # Filtrage par département
    if request.args.get('department'):
        query = query.filter(Employee.department.contains(request.args.get('department')))

    # Filtrage par poste
    if request.args.get('position'):
        query = query.filter(Employee.position.contains(request.args.get('position')))

    employees = query.order_by(Employee.last_name, Employee.first_name).paginate(
        page=page, per_page=20, error_out=False)

    return render_template('employees/index.html', employees=employees, form=form)

@bp.route('/dashboard')
@login_required
@permission_required('can_manage_employees')
def dashboard():
    """Tableau de bord RH"""
    owner_id = current_user.get_owner_id
    
    # Statistiques générales
    total_employees = Employee.query.filter_by(owner_id=owner_id).count()
    active_employees = Employee.query.filter_by(owner_id=owner_id, status='ACTIVE').count()
    
    # Employés récemment embauchés (30 derniers jours)
    from datetime import date, timedelta
    thirty_days_ago = date.today() - timedelta(days=30)
    recent_hires = Employee.query.filter(
        Employee.owner_id == owner_id,
        Employee.hire_date >= thirty_days_ago
    ).count()
    
    # Présences du jour
    today = date.today()
    today_attendances = Attendance.query.join(Employee).filter(
        Employee.owner_id == owner_id,
        Attendance.date == today
    ).count()
    
    return render_template('employees/dashboard.html', 
                         total_employees=total_employees,
                         active_employees=active_employees,
                         recent_hires=recent_hires,
                         today_attendances=today_attendances)

@bp.route('/new', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def new():
    """Créer un nouvel employé"""
    form = EmployeeForm()

    if form.validate_on_submit():
        employee = Employee(
            employee_id=form.employee_id.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            email=form.email.data,
            phone=form.phone.data,
            address=form.address.data,
            date_of_birth=form.date_of_birth.data,
            national_id=form.national_id.data,
            hire_date=form.hire_date.data,
            termination_date=form.termination_date.data,
            status=form.status.data,
            contract_type=form.contract_type.data,
            department=form.department.data,
            position=form.position.data,
            base_salary=form.base_salary.data,
            hourly_rate=form.hourly_rate.data,
            payment_frequency=form.payment_frequency.data,
            owner_id=current_user.get_owner_id
        )

        db.session.add(employee)
        db.session.commit()

        flash(f'Employé {employee.full_name} créé avec succès!', 'success')
        return redirect(url_for('employees.detail', id=employee.id))

    return render_template('employees/form.html', form=form, title="Nouvel Employé")

@bp.route('/<int:id>')
@login_required
@permission_required('can_manage_employees')
def detail(id):
    """Détails d'un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    # Statistiques récentes
    today = date.today()
    current_month = today.replace(day=1)

    # Présences du mois en cours
    monthly_attendances = Attendance.query.filter(
        Attendance.employee_id == id,
        Attendance.date >= current_month,
        Attendance.status == AttendanceStatus.PRESENT
    ).count()

    # Heures travaillées ce mois
    monthly_hours = employee.get_monthly_hours(today.year, today.month)

    # Dernière évaluation
    last_performance = Performance.query.filter_by(employee_id=id).order_by(Performance.evaluation_date.desc()).first()

    return render_template('employees/detail.html',
                         employee=employee,
                         monthly_attendances=monthly_attendances,
                         monthly_hours=monthly_hours,
                         last_performance=last_performance)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def edit(id):
    """Modifier un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    form = EmployeeForm(obj=employee)
    form.employee = employee  # Pour la validation d'unicité

    if form.validate_on_submit():
        form.populate_obj(employee)
        db.session.commit()

        flash(f'Employé {employee.full_name} mis à jour avec succès!', 'success')
        return redirect(url_for('employees.detail', id=employee.id))

    return render_template('employees/form.html', form=form, employee=employee, title="Modifier Employé")

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
@permission_required('can_manage_employees')
def delete(id):
    """Supprimer un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    # Marquer comme inactif plutôt que supprimer
    employee.status = EmployeeStatus.TERMINATED
    employee.termination_date = date.today()
    db.session.commit()

    flash(f'Employé {employee.full_name} marqué comme terminé.', 'info')
    return redirect(url_for('employees.index'))

# Routes pour la gestion des profils
@bp.route('/<int:id>/profile', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def profile(id):
    """Gérer le profil détaillé d'un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    # Créer le profil s'il n'existe pas
    if not employee.profile:
        employee.profile = EmployeeProfile(employee_id=employee.id)
        db.session.add(employee.profile)
        db.session.commit()

    form = EmployeeProfileForm(obj=employee.profile)

    if form.validate_on_submit():
        form.populate_obj(employee.profile)
        db.session.commit()

        flash('Profil mis à jour avec succès!', 'success')
        return redirect(url_for('employees.detail', id=employee.id))

    # Calculer l'ancienneté
    seniority_info = None
    if employee.hire_date:
        today = date.today()
        days_diff = (today - employee.hire_date).days
        years = days_diff // 365
        months = (days_diff % 365) // 30

        if years > 0:
            seniority_info = f"{years} an{'s' if years > 1 else ''}"
            if months > 0:
                seniority_info += f" et {months} mois"
        elif months > 0:
            seniority_info = f"{months} mois"
        else:
            seniority_info = f"{days_diff} jour{'s' if days_diff > 1 else ''}"

    return render_template('employees/profile.html',
                         form=form,
                         employee=employee,
                         seniority_info=seniority_info)

# Routes pour la gestion des plannings
@bp.route('/<int:id>/schedules')
@login_required
@permission_required('can_manage_employees')
def schedules(id):
    """Liste des plannings d'un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    # Récupérer tous les plannings pour les statistiques et la vue calendaire
    schedules = WorkSchedule.query.filter_by(employee_id=id).order_by(
        WorkSchedule.start_date.desc(), WorkSchedule.day_of_week
    ).all()

    return render_template('employees/schedules.html', employee=employee, schedules=schedules)

@bp.route('/<int:id>/schedules/new', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def new_schedule(id):
    """Créer un nouveau planning pour un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    form = WorkScheduleForm()

    if form.validate_on_submit():
        schedule = WorkSchedule(
            employee_id=employee.id,
            owner_id=current_user.get_owner_id,
            start_date=form.start_date.data,
            end_date=form.end_date.data,
            day_of_week=form.day_of_week.data,
            shift_type=form.shift_type.data,
            start_time=form.start_time.data,
            end_time=form.end_time.data,
            break_duration=form.break_duration.data,
            notes=form.notes.data,
            is_recurring=form.is_recurring.data,
            created_by_id=current_user.id
        )

        db.session.add(schedule)
        db.session.commit()

        flash('Planning créé avec succès!', 'success')
        return redirect(url_for('employees.schedules', id=employee.id))

    return render_template('employees/schedule_form.html', form=form, employee=employee, title="Nouveau Planning")

@bp.route('/schedules/<int:schedule_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def edit_schedule(schedule_id):
    """Modifier un planning"""
    schedule = WorkSchedule.query.get_or_404(schedule_id)
    if schedule.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    form = WorkScheduleForm(obj=schedule)

    if form.validate_on_submit():
        form.populate_obj(schedule)
        db.session.commit()

        flash('Planning mis à jour avec succès!', 'success')
        return redirect(url_for('employees.schedules', id=schedule.employee_id))

    return render_template('employees/schedule_form.html',
                         form=form,
                         employee=schedule.employee,
                         schedule=schedule,
                         title="Modifier Planning")

@bp.route('/schedules/<int:schedule_id>/delete', methods=['POST'])
@login_required
@permission_required('can_manage_employees')
def delete_schedule(schedule_id):
    """Supprimer un planning"""
    schedule = WorkSchedule.query.get_or_404(schedule_id)
    if schedule.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    employee_id = schedule.employee_id
    db.session.delete(schedule)
    db.session.commit()

    flash('Planning supprimé avec succès!', 'success')
    return redirect(url_for('employees.schedules', id=employee_id))

# Routes pour la gestion des présences
@bp.route('/<int:id>/attendance')
@login_required
@permission_required('can_manage_employees')
def attendance(id):
    """Liste des présences d'un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    page = request.args.get('page', 1, type=int)
    month = request.args.get('month', date.today().month, type=int)
    year = request.args.get('year', date.today().year, type=int)

    # Filtrer par mois/année
    start_date = date(year, month, 1)
    if month == 12:
        end_date = date(year + 1, 1, 1)
    else:
        end_date = date(year, month + 1, 1)

    attendances = Attendance.query.filter(
        Attendance.employee_id == id,
        Attendance.date >= start_date,
        Attendance.date < end_date
    ).order_by(Attendance.date.desc()).paginate(
        page=page, per_page=31, error_out=False)

    return render_template('employees/attendance.html',
                         employee=employee,
                         attendances=attendances,
                         current_month=month,
                         current_year=year)

@bp.route('/<int:id>/attendance/new', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def new_attendance(id):
    """Enregistrer une nouvelle présence"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    form = AttendanceForm()

    if form.validate_on_submit():
        attendance = Attendance(
            employee_id=employee.id,
            owner_id=current_user.get_owner_id,
            date=form.date.data,
            status=form.status.data,
            clock_in_time=datetime.combine(form.date.data, form.clock_in_time.data) if form.clock_in_time.data else None,
            clock_out_time=datetime.combine(form.date.data, form.clock_out_time.data) if form.clock_out_time.data else None,
            break_start_time=datetime.combine(form.date.data, form.break_start_time.data) if form.break_start_time.data else None,
            break_end_time=datetime.combine(form.date.data, form.break_end_time.data) if form.break_end_time.data else None,
            break_duration=form.break_duration.data,
            scheduled_hours=form.scheduled_hours.data,
            notes=form.notes.data
        )

        # Calculer automatiquement les heures travaillées
        attendance.calculate_hours_worked()

        db.session.add(attendance)
        db.session.commit()

        flash('Présence enregistrée avec succès!', 'success')
        return redirect(url_for('employees.attendance', id=employee.id))

    return render_template('employees/attendance_form.html', form=form, employee=employee, title="Nouvelle Présence")

@bp.route('/attendance/<int:attendance_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def edit_attendance(attendance_id):
    """Modifier une présence"""
    attendance = Attendance.query.get_or_404(attendance_id)
    if attendance.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    form = AttendanceForm(obj=attendance)

    # Convertir les datetime en time pour le formulaire
    if attendance.clock_in_time:
        form.clock_in_time.data = attendance.clock_in_time.time()
    if attendance.clock_out_time:
        form.clock_out_time.data = attendance.clock_out_time.time()
    if attendance.break_start_time:
        form.break_start_time.data = attendance.break_start_time.time()
    if attendance.break_end_time:
        form.break_end_time.data = attendance.break_end_time.time()

    if form.validate_on_submit():
        attendance.date = form.date.data
        attendance.status = form.status.data
        attendance.clock_in_time = datetime.combine(form.date.data, form.clock_in_time.data) if form.clock_in_time.data else None
        attendance.clock_out_time = datetime.combine(form.date.data, form.clock_out_time.data) if form.clock_out_time.data else None
        attendance.break_start_time = datetime.combine(form.date.data, form.break_start_time.data) if form.break_start_time.data else None
        attendance.break_end_time = datetime.combine(form.date.data, form.break_end_time.data) if form.break_end_time.data else None
        attendance.break_duration = form.break_duration.data
        attendance.scheduled_hours = form.scheduled_hours.data
        attendance.notes = form.notes.data

        # Recalculer les heures travaillées
        attendance.calculate_hours_worked()

        db.session.commit()

        flash('Présence mise à jour avec succès!', 'success')
        return redirect(url_for('employees.attendance', id=attendance.employee_id))

    return render_template('employees/attendance_form.html',
                         form=form,
                         employee=attendance.employee,
                         attendance=attendance,
                         title="Modifier Présence")

# Routes manquantes pour la sidebar
@bp.route('/attendance/today')
@login_required
@permission_required('can_manage_employees')
def attendance_today():
    """Présences du jour"""
    owner_id = current_user.get_owner_id
    today = date.today()

    attendances = Attendance.query.join(Employee).filter(
        Employee.owner_id == owner_id,
        Attendance.date == today
    ).order_by(Employee.last_name, Employee.first_name).all()

    return render_template('employees/attendance_today.html',
                         attendances=attendances,
                         today=today)

@bp.route('/schedules/week')
@login_required
@permission_required('can_manage_employees')
def schedules_week():
    """Planning de la semaine"""
    owner_id = current_user.get_owner_id
    today = date.today()

    # Calculer le début et la fin de la semaine
    start_of_week = today - timedelta(days=today.weekday())
    end_of_week = start_of_week + timedelta(days=6)

    schedules = WorkSchedule.query.join(Employee).filter(
        Employee.owner_id == owner_id,
        WorkSchedule.start_date <= end_of_week,
        WorkSchedule.end_date >= start_of_week
    ).order_by(WorkSchedule.day_of_week, Employee.last_name).all()

    return render_template('employees/schedules_week.html',
                         schedules=schedules,
                         start_of_week=start_of_week,
                         end_of_week=end_of_week)

@bp.route('/payroll/actions')
@login_required
@permission_required('can_manage_payroll')
def payroll_actions():
    """Actions de paie"""
    owner_id = current_user.get_owner_id

    # Employés sans fiche de paie ce mois
    today = date.today()
    current_month = today.replace(day=1)

    employees_without_payroll = Employee.query.filter(
        Employee.owner_id == owner_id,
        Employee.status == 'ACTIVE',
        ~Employee.payrolls.any(
            Payroll.pay_period_start >= current_month
        )
    ).all()

    return render_template('employees/payroll_actions.html',
                         employees_without_payroll=employees_without_payroll,
                         current_month=current_month)

@bp.route('/performance/pending')
@login_required
@permission_required('can_manage_employees')
def performance_pending():
    """Évaluations en attente"""
    owner_id = current_user.get_owner_id

    # Employés sans évaluation dans les 6 derniers mois
    six_months_ago = date.today() - timedelta(days=180)

    employees_need_evaluation = Employee.query.filter(
        Employee.owner_id == owner_id,
        Employee.status == 'ACTIVE',
        ~Employee.performances.any(
            Performance.evaluation_date >= six_months_ago
        )
    ).all()

    return render_template('employees/performance_pending.html',
                         employees_need_evaluation=employees_need_evaluation,
                         six_months_ago=six_months_ago)

@bp.route('/reports/generate')
@login_required
@permission_required('can_view_employee_reports')
def reports_generate():
    """Générer des rapports"""
    return render_template('employees/reports_generate.html')

@bp.route('/bulk-actions')
@login_required
@permission_required('can_manage_employees')
def bulk_actions():
    """Actions en lot sur les employés"""
    owner_id = current_user.get_owner_id

    # Récupérer tous les employés actifs
    active_employees = Employee.query.filter_by(
        owner_id=owner_id,
        status='ACTIVE'
    ).order_by(Employee.last_name, Employee.first_name).all()

    return render_template('employees/bulk_actions.html',
                         active_employees=active_employees)

# Routes API pour la sidebar et actions rapides
@bp.route('/api/stats')
@login_required
@permission_required('can_manage_employees')
def api_stats():
    """API pour les statistiques de la sidebar"""
    owner_id = current_user.get_owner_id

    total_employees = Employee.query.filter_by(owner_id=owner_id, status='ACTIVE').count()

    # Présents aujourd'hui
    today = date.today()
    today_present = Attendance.query.join(Employee).filter(
        Employee.owner_id == owner_id,
        Attendance.date == today,
        Attendance.status == AttendanceStatus.PRESENT
    ).count()

    return jsonify({
        'total_employees': total_employees,
        'today_present': today_present
    })

@bp.route('/api/active-employees')
@login_required
@permission_required('can_manage_employees')
def api_active_employees():
    """API pour la liste des employés actifs"""
    owner_id = current_user.get_owner_id

    employees = Employee.query.filter_by(
        owner_id=owner_id,
        status='ACTIVE'
    ).order_by(Employee.first_name, Employee.last_name).all()

    return jsonify([{
        'id': emp.id,
        'full_name': emp.full_name,
        'employee_id': emp.employee_id
    } for emp in employees])

@bp.route('/api/quick-attendance', methods=['POST'])
@login_required
@permission_required('can_manage_employees')
def api_quick_attendance():
    """API pour le pointage rapide"""
    data = request.get_json()
    employee_id = data.get('employee_id')
    action_type = data.get('action_type')

    if not employee_id or not action_type:
        return jsonify({'success': False, 'message': 'Données manquantes'}), 400

    # Vérifier que l'employé existe et appartient au bon propriétaire
    employee = Employee.query.get_or_404(employee_id)
    if employee.owner_id != current_user.get_owner_id:
        return jsonify({'success': False, 'message': 'Accès non autorisé'}), 403

    today = date.today()
    current_time = datetime.now().time()

    # Chercher ou créer une présence pour aujourd'hui
    attendance = Attendance.query.filter_by(
        employee_id=employee_id,
        date=today
    ).first()

    if not attendance:
        attendance = Attendance(
            employee_id=employee_id,
            owner_id=current_user.get_owner_id,
            date=today,
            status=AttendanceStatus.PRESENT
        )
        db.session.add(attendance)

    # Appliquer l'action
    if action_type == 'clock_in':
        attendance.clock_in_time = datetime.combine(today, current_time)
        attendance.status = AttendanceStatus.PRESENT
    elif action_type == 'clock_out':
        attendance.clock_out_time = datetime.combine(today, current_time)
    elif action_type == 'break_start':
        attendance.break_start_time = datetime.combine(today, current_time)
    elif action_type == 'break_end':
        attendance.break_end_time = datetime.combine(today, current_time)

    # Calculer les heures travaillées
    attendance.calculate_hours_worked()

    db.session.commit()

    action_names = {
        'clock_in': 'Arrivée',
        'clock_out': 'Départ',
        'break_start': 'Début de pause',
        'break_end': 'Fin de pause'
    }

    return jsonify({
        'success': True,
        'message': f'{action_names[action_type]} enregistrée pour {employee.full_name}',
        'time': current_time.strftime('%H:%M'),
        'date': today.strftime('%d/%m/%Y')
    })

# Routes manquantes pour les exports et rapports

# Routes pour l'export et les rapports
@bp.route('/export/all')
@login_required
@permission_required('can_view_employee_reports')
def export_all():
    """Exporter tous les employés"""
    # TODO: Implémenter l'export
    flash('Fonctionnalité d\'export en cours de développement', 'info')
    return redirect(url_for('employees.index'))

@bp.route('/<int:id>/attendance/export')
@login_required
@permission_required('can_view_employee_reports')
def export_attendance(id):
    """Exporter les présences d'un employé"""
    # TODO: Implémenter l'export des présences
    flash('Fonctionnalité d\'export des présences en cours de développement', 'info')
    return redirect(url_for('employees.attendance', id=id))

@bp.route('/reports/attendance')
@login_required
@permission_required('can_view_employee_reports')
def reports_attendance():
    """Rapport de présences"""
    # TODO: Implémenter le rapport de présences
    flash('Fonctionnalité de rapport de présences en cours de développement', 'info')
    return redirect(url_for('employees.reports_generate'))

@bp.route('/reports/performance')
@login_required
@permission_required('can_view_employee_reports')
def reports_performance():
    """Rapport de performances"""
    # TODO: Implémenter le rapport de performances
    flash('Fonctionnalité de rapport de performances en cours de développement', 'info')
    return redirect(url_for('employees.reports_generate'))

@bp.route('/reports/employee-list')
@login_required
@permission_required('can_view_employee_reports')
def reports_employee_list():
    """Liste des employés en format rapport"""
    # TODO: Implémenter la liste des employés
    flash('Fonctionnalité de liste des employés en cours de développement', 'info')
    return redirect(url_for('employees.reports_generate'))

# Route pour supprimer une présence
@bp.route('/attendance/<int:attendance_id>/delete', methods=['POST'])
@login_required
@permission_required('can_manage_employees')
def delete_attendance(attendance_id):
    """Supprimer une présence"""
    attendance = Attendance.query.get_or_404(attendance_id)

    # Vérifier que l'employé appartient au bon propriétaire
    if attendance.employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    employee_id = attendance.employee_id
    db.session.delete(attendance)
    db.session.commit()

    flash('Présence supprimée avec succès', 'success')
    return redirect(url_for('employees.attendance', id=employee_id))

# Importer les routes supplémentaires
from app.modules.employees import routes_payroll, routes_performance
