{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createSystemUserModal">
            <i class="fas fa-user-plus"></i> Créer un Utilisateur
        </button>
    </div>

    <!-- System Users Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Administrateurs Système</h6>
        </div>
        <div class="card-body">
            {% if system_users %}
            <div class="table-responsive">
                <table class="table table-bordered" id="systemUsersTable">
                    <thead>
                        <tr>
                            <th>Nom d'utilisateur</th>
                            <th>Email</th>
                            <th>Statut</th>
                            <th>Dernière connexion</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in system_users %}
                        <tr>
                            <td>{{ user.username }}</td>
                            <td>{{ user.email }}</td>
                            <td>
                                {% if user.is_active %}
                                <span class="badge bg-success">Actif</span>
                                {% else %}
                                <span class="badge bg-danger">Inactif</span>
                                {% endif %}
                            </td>
                            <td>{{ user.last_login|default('Jamais', true) }}</td>
                            <td>
                                <a href="{{ url_for('admin.edit_system_user', id=user.id) }}" class="btn btn-info btn-sm">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button onclick="confirmDelete('{{ user.username }}', {{ user.id }})" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p class="text-muted">Aucun administrateur système</p>
            {% endif %}
        </div>
    </div>

    <!-- Owners Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Propriétaires</h6>
        </div>
        <div class="card-body">
            {% if owners_with_users %}
            <div class="accordion" id="ownersAccordion">
                {% for owner_data in owners_with_users %}
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading{{ owner_data.owner.id }}">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ owner_data.owner.id }}">
                            {{ owner_data.owner.username }} ({{ owner_data.total_users }} utilisateurs)
                        </button>
                    </h2>
                    <div id="collapse{{ owner_data.owner.id }}" class="accordion-collapse collapse" data-bs-parent="#ownersAccordion">
                        <div class="accordion-body">
                            <div class="mb-3">
                                <strong>Email:</strong> {{ owner_data.owner.email }}<br>
                                <strong>Statut:</strong> 
                                {% if owner_data.owner.is_active %}
                                <span class="badge bg-success">Actif</span>
                                {% else %}
                                <span class="badge bg-danger">Inactif</span>
                                {% endif %}
                                <br>
                                <strong>Dernière connexion:</strong> {{ owner_data.owner.last_login|default('Jamais', true) }}
                            </div>
                            <div class="btn-group mb-3">
                                <a href="{{ url_for('admin.edit_system_user', id=owner_data.owner.id) }}" class="btn btn-info btn-sm">
                                    <i class="fas fa-edit"></i> Modifier
                                </a>
                                <button onclick="confirmDelete('{{ owner_data.owner.username }}', {{ owner_data.owner.id }})" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash"></i> Supprimer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p class="text-muted">Aucun propriétaire</p>
            {% endif %}
        </div>
    </div>
</div>

<!-- Create System User Modal -->
<div class="modal fade" id="createSystemUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Créer un Utilisateur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('admin.create_system_user') }}" method="post">
                {{ form.csrf_token }}
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Nom d'utilisateur</label>
                        <input type="text" name="username" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" name="email" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mot de passe</label>
                        <input type="password" name="password" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Rôle</label>
                        <select name="role" id="roleSelect" class="form-select" required>
                            <option value="{{ roles.SYSTEM_ADMIN.value }}">Administrateur Système</option>
                            <option value="{{ roles.OWNER.value }}">Propriétaire</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Créer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Êtes-vous sûr de vouloir supprimer l'utilisateur <strong id="deleteUserName"></strong> ?
            </div>
            <div class="modal-footer">
                <form id="deleteForm" method="post">
                    {{ form.csrf_token }}
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(username, userId) {
    document.getElementById('deleteUserName').textContent = username;
    document.getElementById('deleteForm').action = "{{ url_for('admin.delete_system_user', id=0) }}".replace('0', userId);
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

$(document).ready(function() {
    $('#systemUsersTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
        }
    });
});
</script>
{% endblock %} 