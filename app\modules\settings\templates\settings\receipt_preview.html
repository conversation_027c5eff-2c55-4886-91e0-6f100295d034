<!-- Receipt Preview -->
<div class="receipt" style="font-family: {{ settings.font_family }}; font-size: {{ settings.font_size }}pt; line-height: {{ settings.line_spacing }};">
    <!-- Header -->
    {% if settings.show_logo and settings.business_logo %}
    <div class="text-center mb-3">
        <img src="{{ url_for('static', filename=settings.business_logo) }}" alt="Logo" style="max-width: 150px;">
    </div>
    {% endif %}

    <div class="text-center">
        <h4 class="mb-0">{{ settings.business_name }}</h4>
        <p class="small mb-2">{{ settings.address }}<br>{{ settings.postal_code }} {{ settings.city }}</p>
        {% if settings.header_text %}
        <p class="small mb-3">{{ settings.header_text }}</p>
        {% endif %}
    </div>

    <!-- Sale Info -->
    <div class="mb-3">
        {% if settings.show_order_number %}
        <p class="mb-1">Commande: {{ sale['number'] }}</p>
        {% endif %}
        <p class="mb-1">Date: {{ sale['date'].strftime(settings.date_format) }}</p>
        {% if settings.show_server_name %}
        <p class="mb-1">Serveur: {{ sale['server'] }}</p>
        {% endif %}
        {% if settings.show_table_number %}
        <p class="mb-1">Table: {{ sale['table'] }}</p>
        {% endif %}
    </div>

    <!-- Items -->
    <table class="w-100 mb-3">
        <thead>
            <tr>
                <th class="text-start">Article</th>
                <th class="text-end">Qté</th>
                <th class="text-end">Prix</th>
                <th class="text-end">Total</th>
            </tr>
        </thead>
        <tbody>
            {% for item in sale['items'] %}
            <tr>
                <td>{{ item['name'] }}</td>
                <td class="text-end">{{ item['quantity'] }}</td>
                <td class="text-end">{{ "%.2f"|format(item['price']) }} €</td>
                <td class="text-end">{{ "%.2f"|format(item['total']) }} €</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Totals -->
    <div class="mb-3">
        <div class="d-flex justify-content-between">
            <span>Sous-total</span>
            <span>{{ "%.2f"|format(sale['subtotal']) }} €</span>
        </div>
        {% if settings.show_tax_details %}
        <div class="d-flex justify-content-between">
            <span>TVA (20%)</span>
            <span>{{ "%.2f"|format(sale['tax']) }} €</span>
        </div>
        {% endif %}
        <div class="d-flex justify-content-between fw-bold">
            <span>Total</span>
            <span>{{ "%.2f"|format(sale['total']) }} €</span>
        </div>
    </div>

    <!-- Payment -->
    {% if settings.show_payment_details %}
    <div class="mb-3">
        <p class="mb-1">Mode de paiement: {{ sale['payment_method'] }}</p>
    </div>
    {% endif %}

    <!-- Footer -->
    {% if settings.footer_text %}
    <div class="text-center small mt-4">
        {{ settings.footer_text }}
    </div>
    {% endif %}
</div> 