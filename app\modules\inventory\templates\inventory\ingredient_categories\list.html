{% extends "base.html" %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">{{ title }}</h1>
    
    <!-- Barre d'outils -->
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="get" class="d-flex gap-2">
                <div class="input-group">
                    <input type="text" name="search" class="form-control" placeholder="Rechercher une catégorie..." value="{{ search }}">
                    <button class="btn btn-outline-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <select name="per_page" class="form-select" style="width: auto;" onchange="this.form.submit()">
                    <option value="10" {% if per_page == 10 %}selected{% endif %}>10 par page</option>
                    <option value="25" {% if per_page == 25 %}selected{% endif %}>25 par page</option>
                    <option value="50" {% if per_page == 50 %}selected{% endif %}>50 par page</option>
                    <option value="100" {% if per_page == 100 %}selected{% endif %}>100 par page</option>
                </select>
            </form>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ url_for('inventory.add_ingredient_category') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Ajouter une catégorie
            </a>
        </div>
    </div>

    <!-- Tableau des catégories -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" style="background-color: #A7C7E7;">
                    <thead>
                        <tr>
                            <th style="width: 80px;">Image</th>
                            <th>Nom</th>
                            <th>Description</th>
                            <th style="width: 120px;" class="text-center">Total Ingrédients</th>
                            <th style="width: 120px;" class="text-center">En Rupture</th>
                            <th style="width: 150px;" class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in categories.items %}
                        <tr>
                            <td>
                                {% if category.image_path %}
                                <img src="{{ url_for('static', filename=category.image_path) }}" 
                                     alt="{{ category.name }}" 
                                     class="img-thumbnail" 
                                     style="width: 50px; height: 50px; object-fit: cover;">
                                {% else %}
                                <div class="bg-light d-flex align-items-center justify-content-center" 
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-folder text-secondary"></i>
                                </div>
                                {% endif %}
                            </td>
                            <td>{{ category.name }}</td>
                            <td>{{ category.description or '-' }}</td>
                            <td class="text-center">
                                <span class="badge bg-primary">{{ stats[category.id]['total_ingredients'] }}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge {% if stats[category.id]['out_of_stock'] > 0 %}bg-danger{% else %}bg-success{% endif %}">
                                    {{ stats[category.id]['out_of_stock'] }}
                                </span>
                            </td>
                            <td class="text-end">
                                <div class="btn-group">
                                    <a href="{{ url_for('inventory.edit_ingredient_category', id=category.id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-danger"
                                            onclick="confirmDelete({{ category.id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="6" class="text-center">
                                {% if search %}
                                Aucune catégorie ne correspond à votre recherche.
                                {% else %}
                                Aucune catégorie n'a été créée.
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if categories.pages > 1 %}
    <nav aria-label="Navigation des pages">
        <ul class="pagination justify-content-center">
            <li class="page-item {% if not categories.has_prev %}disabled{% endif %}">
                <a class="page-link" href="{% if categories.has_prev %}{{ url_for('inventory.list_ingredient_categories', page=categories.prev_num, search=search, per_page=per_page) }}{% else %}#{% endif %}">Précédent</a>
            </li>
            
            {% for page_num in range(1, categories.pages + 1) %}
            <li class="page-item {% if page_num == categories.page %}active{% endif %}">
                <a class="page-link" href="{{ url_for('inventory.list_ingredient_categories', page=page_num, search=search, per_page=per_page) }}">{{ page_num }}</a>
            </li>
            {% endfor %}
            
            <li class="page-item {% if not categories.has_next %}disabled{% endif %}">
                <a class="page-link" href="{% if categories.has_next %}{{ url_for('inventory.list_ingredient_categories', page=categories.next_num, search=search, per_page=per_page) }}{% else %}#{% endif %}">Suivant</a>
            </li>
        </ul>
    </nav>
    {% endif %}
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Êtes-vous sûr de vouloir supprimer cette catégorie ? Cette action est irréversible.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(categoryId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const form = document.getElementById('deleteForm');
    form.action = "{{ url_for('inventory.delete_ingredient_category', id=0) }}".replace('0', categoryId);
    modal.show();
}
</script>
{% endblock %} 