"""Add payment_method to online_orders

Revision ID: 6185b869dea3
Revises: 5da43654db95
Create Date: 2025-06-20 04:18:47.950349

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6185b869dea3'
down_revision = '5da43654db95'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('online_orders', schema=None) as batch_op:
        batch_op.add_column(sa.Column('payment_method', sa.Enum('CASH_ON_DELIVERY', 'ONLINE_PAYMENT', 'CASH', 'CARD', name='paymentmethod'), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('online_orders', schema=None) as batch_op:
        batch_op.drop_column('payment_method')

    # ### end Alembic commands ###
