from flask import render_template, redirect, url_for, flash, request, send_file, jsonify, current_app
from flask_login import login_required, current_user
from app.modules.auth.models import UserRole
from app.modules.settings.models_settings import Settings
from app import db
import pandas as pd
import io
import json
from datetime import datetime
import os

from . import bp

@bp.route('/api-settings')
@login_required
def api_settings():
    """Paramètres API et intégrations"""
    if not (current_user.is_system_admin or current_user.is_owner or current_user.role in [UserRole.ADMIN, UserRole.MANAGER]):
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    return render_template('advanced/api_settings.html',
                         title='API & Intégrations')

@bp.route('/import-export')
@login_required
def import_export():
    """Import/Export de données"""
    if not (current_user.is_system_admin or current_user.is_owner or current_user.role in [UserRole.ADMIN, UserRole.MANAGER]):
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    return render_template('advanced/import_export.html',
                         title='Import/Export')

@bp.route('/customization')
@login_required
def customization():
    """Personnalisation"""
    if not (current_user.is_system_admin or current_user.is_owner or current_user.role in [UserRole.ADMIN, UserRole.MANAGER]):
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    return render_template('advanced/customization.html',
                         title='Personnalisation')

@bp.route('/automation')
@login_required
def automation():
    """Automatisation"""
    if not (current_user.is_system_admin or current_user.is_owner or current_user.role in [UserRole.ADMIN, UserRole.MANAGER]):
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    return render_template('advanced/automation.html',
                         title='Automatisation')

@bp.route('/notifications')
@login_required
def notifications():
    """Notifications"""
    if not (current_user.is_system_admin or current_user.is_owner or current_user.role in [UserRole.ADMIN, UserRole.MANAGER]):
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    return render_template('advanced/notifications.html',
                         title='Notifications')

@bp.route('/import-data', methods=['POST'])
@login_required
def import_data():
    """Import data from file"""
    if not (current_user.is_system_admin or current_user.is_owner or current_user.role in [UserRole.ADMIN, UserRole.MANAGER]):
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    if 'file' not in request.files:
        flash('Aucun fichier sélectionné.', 'error')
        return redirect(url_for('advanced.import_export'))
    
    file = request.files['file']
    if file.filename == '':
        flash('Aucun fichier sélectionné.', 'error')
        return redirect(url_for('advanced.import_export'))
    
    data_type = request.form.get('type')
    update_existing = request.form.get('update_existing') == 'on'
    
    try:
        # Read file based on extension
        if file.filename.endswith('.csv'):
            df = pd.read_csv(file)
        elif file.filename.endswith('.xlsx'):
            df = pd.read_excel(file)
        else:
            flash('Format de fichier non supporté. Utilisez CSV ou Excel.', 'error')
            return redirect(url_for('advanced.import_export'))
        
        # TODO: Process the data based on type
        # This is a placeholder - implement actual import logic
        flash(f'Import de {len(df)} lignes réussi.', 'success')
        
    except Exception as e:
        flash(f'Erreur lors de l\'import: {str(e)}', 'error')
    
    return redirect(url_for('advanced.import_export'))

@bp.route('/export-data', methods=['POST'])
@login_required
def export_data():
    """Export data to file"""
    if not (current_user.is_system_admin or current_user.is_owner or current_user.role in [UserRole.ADMIN, UserRole.MANAGER]):
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    data_type = request.form.get('type')
    export_format = request.form.get('format')
    start_date = request.form.get('start_date')
    end_date = request.form.get('end_date')
    
    try:
        # TODO: Get data based on type and date range
        # This is a placeholder - implement actual export logic
        data = []  # Replace with actual data
        
        if export_format == 'csv':
            output = io.StringIO()
            pd.DataFrame(data).to_csv(output, index=False)
            output.seek(0)
            return send_file(
                io.BytesIO(output.getvalue().encode('utf-8')),
                mimetype='text/csv',
                as_attachment=True,
                download_name=f'{data_type}_{datetime.now().strftime("%Y%m%d")}.csv'
            )
        
        elif export_format == 'xlsx':
            output = io.BytesIO()
            pd.DataFrame(data).to_excel(output, index=False)
            output.seek(0)
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f'{data_type}_{datetime.now().strftime("%Y%m%d")}.xlsx'
            )
        
        elif export_format == 'json':
            return send_file(
                io.BytesIO(json.dumps(data).encode('utf-8')),
                mimetype='application/json',
                as_attachment=True,
                download_name=f'{data_type}_{datetime.now().strftime("%Y%m%d")}.json'
            )
        
        else:
            flash('Format d\'export non supporté.', 'error')
            return redirect(url_for('advanced.import_export'))
        
    except Exception as e:
        flash(f'Erreur lors de l\'export: {str(e)}', 'error')
        return redirect(url_for('advanced.import_export'))

@bp.route('/save-theme', methods=['POST'])
@login_required
def save_theme():
    """Sauvegarder les paramètres du thème"""
    if not (current_user.is_system_admin or current_user.is_owner or current_user.role in [UserRole.ADMIN, UserRole.MANAGER]):
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    settings = Settings.query.filter_by(owner_id=current_user.id).first()
    if not settings:
        settings = Settings(owner_id=current_user.id)
        db.session.add(settings)
    
    settings.primary_color = request.form.get('primary_color', '#4e73df')
    settings.secondary_color = request.form.get('secondary_color', '#858796')
    settings.theme = request.form.get('theme', 'light')
    settings.font = request.form.get('font', 'nunito')
    
    try:
        db.session.commit()
        flash('Thème enregistré avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'enregistrement du thème: {str(e)}', 'error')
    
    return redirect(url_for('advanced.customization'))

@bp.route('/save-layout', methods=['POST'])
@login_required
def save_layout():
    """Sauvegarder les paramètres de mise en page"""
    if not (current_user.is_system_admin or current_user.is_owner or current_user.role in [UserRole.ADMIN, UserRole.MANAGER]):
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    settings = Settings.query.filter_by(owner_id=current_user.id).first()
    if not settings:
        settings = Settings(owner_id=current_user.id)
        db.session.add(settings)
    
    settings.sidebar_position = request.form.get('sidebar_position', 'left')
    settings.container_width = request.form.get('container_width', 'fluid')
    settings.sticky_header = request.form.get('sticky_header') == 'on'
    settings.sticky_footer = request.form.get('sticky_footer') == 'on'
    
    try:
        db.session.commit()
        flash('Mise en page enregistrée avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'enregistrement de la mise en page: {str(e)}', 'error')
    
    return redirect(url_for('advanced.customization'))

@bp.route('/save-branding', methods=['POST'])
@login_required
def save_branding():
    """Sauvegarder les paramètres de marque"""
    if not (current_user.is_system_admin or current_user.is_owner or current_user.role in [UserRole.ADMIN, UserRole.MANAGER]):
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    settings = Settings.query.filter_by(owner_id=current_user.id).first()
    if not settings:
        settings = Settings(owner_id=current_user.id)
        db.session.add(settings)
    
    # Gérer le logo
    if 'logo' in request.files:
        logo = request.files['logo']
        if logo.filename:
            # Sauvegarder le fichier
            filename = f"logo_{current_user.id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.png"
            logo.save(os.path.join(current_app.config['UPLOAD_FOLDER'], filename))
            settings.logo_path = filename
    
    # Gérer le favicon
    if 'favicon' in request.files:
        favicon = request.files['favicon']
        if favicon.filename:
            # Sauvegarder le fichier
            filename = f"favicon_{current_user.id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.ico"
            favicon.save(os.path.join(current_app.config['UPLOAD_FOLDER'], filename))
            settings.favicon_path = filename
    
    # Gérer le nom de l'application
    settings.app_name = request.form.get('app_name', '')
    
    try:
        db.session.commit()
        flash('Image de marque enregistrée avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'enregistrement de l\'image de marque: {str(e)}', 'error')
    
    return redirect(url_for('advanced.customization'))

@bp.route('/save-email-templates', methods=['POST'])
@login_required
def save_email_templates():
    """Sauvegarder les modèles d'emails"""
    if not (current_user.is_system_admin or current_user.is_owner or current_user.role in [UserRole.ADMIN, UserRole.MANAGER]):
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    settings = Settings.query.filter_by(owner_id=current_user.id).first()
    if not settings:
        settings = Settings(owner_id=current_user.id)
        db.session.add(settings)
    
    template_type = request.form.get('template')
    subject = request.form.get('subject')
    content = request.form.get('content')
    
    # Stocker les modèles d'emails dans un format JSON
    if not settings.email_templates:
        settings.email_templates = {}
    
    settings.email_templates[template_type] = {
        'subject': subject,
        'content': content
    }
    
    try:
        db.session.commit()
        flash('Modèle d\'email enregistré avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'enregistrement du modèle d\'email: {str(e)}', 'error')
    
    return redirect(url_for('advanced.customization'))

@bp.route('/email-template/<template_type>')
@login_required
def get_email_template(template_type):
    """Récupérer un modèle d'email"""
    if not (current_user.is_system_admin or current_user.is_owner or current_user.role in [UserRole.ADMIN, UserRole.MANAGER]):
        return jsonify({'error': 'Accès non autorisé'}), 403
    
    settings = Settings.query.filter_by(owner_id=current_user.id).first()
    if not settings or not settings.email_templates or template_type not in settings.email_templates:
        return jsonify({
            'subject': '',
            'content': ''
        })
    
    return jsonify(settings.email_templates[template_type])

