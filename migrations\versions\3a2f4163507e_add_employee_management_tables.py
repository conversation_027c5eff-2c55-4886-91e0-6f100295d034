"""Add employee management tables

Revision ID: 3a2f4163507e
Revises: add_ai_support_001
Create Date: 2025-07-13 01:44:12.515649

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3a2f4163507e'
down_revision = 'add_ai_support_001'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('employees',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('employee_id', sa.String(length=20), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('owner_id', sa.Integer(), nullable=False),
    sa.Column('first_name', sa.String(length=50), nullable=False),
    sa.Column('last_name', sa.String(length=50), nullable=False),
    sa.Column('email', sa.String(length=120), nullable=True),
    sa.Column('phone', sa.String(length=20), nullable=True),
    sa.Column('address', sa.Text(), nullable=True),
    sa.Column('date_of_birth', sa.Date(), nullable=True),
    sa.Column('national_id', sa.String(length=50), nullable=True),
    sa.Column('hire_date', sa.Date(), nullable=False),
    sa.Column('termination_date', sa.Date(), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'SUSPENDED', 'TERMINATED', name='employeestatus'), nullable=True),
    sa.Column('contract_type', sa.Enum('FULL_TIME', 'PART_TIME', 'TEMPORARY', 'INTERNSHIP', 'FREELANCE', name='contracttype'), nullable=True),
    sa.Column('department', sa.String(length=100), nullable=True),
    sa.Column('position', sa.String(length=100), nullable=False),
    sa.Column('base_salary', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('hourly_rate', sa.Numeric(precision=8, scale=2), nullable=True),
    sa.Column('payment_frequency', sa.Enum('HOURLY', 'DAILY', 'WEEKLY', 'BIWEEKLY', 'MONTHLY', name='paymentfrequency'), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('employee_id')
    )
    op.create_table('attendances',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('employee_id', sa.Integer(), nullable=False),
    sa.Column('owner_id', sa.Integer(), nullable=False),
    sa.Column('date', sa.Date(), nullable=False),
    sa.Column('status', sa.Enum('PRESENT', 'ABSENT', 'LATE', 'SICK_LEAVE', 'VACATION', 'PERSONAL_LEAVE', name='attendancestatus'), nullable=False),
    sa.Column('clock_in_time', sa.DateTime(), nullable=True),
    sa.Column('clock_out_time', sa.DateTime(), nullable=True),
    sa.Column('break_start_time', sa.DateTime(), nullable=True),
    sa.Column('break_end_time', sa.DateTime(), nullable=True),
    sa.Column('scheduled_hours', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('hours_worked', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('overtime_hours', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('break_duration', sa.Integer(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('approved_by_id', sa.Integer(), nullable=True),
    sa.Column('approved_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['approved_by_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.id'], ),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('employee_documents',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('employee_id', sa.Integer(), nullable=False),
    sa.Column('owner_id', sa.Integer(), nullable=False),
    sa.Column('document_name', sa.String(length=200), nullable=False),
    sa.Column('document_type', sa.String(length=50), nullable=False),
    sa.Column('file_path', sa.String(length=500), nullable=False),
    sa.Column('file_size', sa.Integer(), nullable=True),
    sa.Column('mime_type', sa.String(length=100), nullable=True),
    sa.Column('upload_date', sa.DateTime(), nullable=True),
    sa.Column('expiry_date', sa.Date(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_confidential', sa.Boolean(), nullable=True),
    sa.Column('uploaded_by_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.id'], ),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['uploaded_by_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('employee_profiles',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('employee_id', sa.Integer(), nullable=False),
    sa.Column('emergency_contact_name', sa.String(length=100), nullable=True),
    sa.Column('emergency_contact_phone', sa.String(length=20), nullable=True),
    sa.Column('emergency_contact_relationship', sa.String(length=50), nullable=True),
    sa.Column('bank_name', sa.String(length=100), nullable=True),
    sa.Column('account_number', sa.String(length=50), nullable=True),
    sa.Column('routing_number', sa.String(length=20), nullable=True),
    sa.Column('skills', sa.Text(), nullable=True),
    sa.Column('certifications', sa.Text(), nullable=True),
    sa.Column('languages', sa.Text(), nullable=True),
    sa.Column('preferred_shifts', sa.Text(), nullable=True),
    sa.Column('availability', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('payrolls',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('employee_id', sa.Integer(), nullable=False),
    sa.Column('owner_id', sa.Integer(), nullable=False),
    sa.Column('pay_period_start', sa.Date(), nullable=False),
    sa.Column('pay_period_end', sa.Date(), nullable=False),
    sa.Column('pay_date', sa.Date(), nullable=False),
    sa.Column('regular_hours', sa.Numeric(precision=8, scale=2), nullable=True),
    sa.Column('overtime_hours', sa.Numeric(precision=8, scale=2), nullable=True),
    sa.Column('regular_rate', sa.Numeric(precision=8, scale=2), nullable=False),
    sa.Column('overtime_rate', sa.Numeric(precision=8, scale=2), nullable=False),
    sa.Column('gross_pay', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('tax_deduction', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('social_security_deduction', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('health_insurance_deduction', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('other_deductions', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('total_deductions', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('bonus', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('commission', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('adjustments', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('net_pay', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('is_processed', sa.Boolean(), nullable=True),
    sa.Column('processed_at', sa.DateTime(), nullable=True),
    sa.Column('processed_by_id', sa.Integer(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.id'], ),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['processed_by_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('performances',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('employee_id', sa.Integer(), nullable=False),
    sa.Column('owner_id', sa.Integer(), nullable=False),
    sa.Column('evaluation_period_start', sa.Date(), nullable=False),
    sa.Column('evaluation_period_end', sa.Date(), nullable=False),
    sa.Column('evaluation_date', sa.Date(), nullable=False),
    sa.Column('evaluator_id', sa.Integer(), nullable=False),
    sa.Column('punctuality_score', sa.Integer(), nullable=True),
    sa.Column('quality_of_work_score', sa.Integer(), nullable=True),
    sa.Column('teamwork_score', sa.Integer(), nullable=True),
    sa.Column('communication_score', sa.Integer(), nullable=True),
    sa.Column('initiative_score', sa.Integer(), nullable=True),
    sa.Column('customer_service_score', sa.Integer(), nullable=True),
    sa.Column('overall_score', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('strengths', sa.Text(), nullable=True),
    sa.Column('areas_for_improvement', sa.Text(), nullable=True),
    sa.Column('goals_next_period', sa.Text(), nullable=True),
    sa.Column('evaluator_comments', sa.Text(), nullable=True),
    sa.Column('employee_comments', sa.Text(), nullable=True),
    sa.Column('is_finalized', sa.Boolean(), nullable=True),
    sa.Column('employee_acknowledged', sa.Boolean(), nullable=True),
    sa.Column('acknowledged_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.id'], ),
    sa.ForeignKeyConstraint(['evaluator_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('work_schedules',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('employee_id', sa.Integer(), nullable=False),
    sa.Column('owner_id', sa.Integer(), nullable=False),
    sa.Column('start_date', sa.Date(), nullable=False),
    sa.Column('end_date', sa.Date(), nullable=False),
    sa.Column('day_of_week', sa.Integer(), nullable=False),
    sa.Column('shift_type', sa.Enum('MORNING', 'AFTERNOON', 'EVENING', 'NIGHT', 'FULL_DAY', name='shifttype'), nullable=False),
    sa.Column('start_time', sa.Time(), nullable=False),
    sa.Column('end_time', sa.Time(), nullable=False),
    sa.Column('break_duration', sa.Integer(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('is_recurring', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('created_by_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.id'], ),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('work_schedules')
    op.drop_table('performances')
    op.drop_table('payrolls')
    op.drop_table('employee_profiles')
    op.drop_table('employee_documents')
    op.drop_table('attendances')
    op.drop_table('employees')
    # ### end Alembic commands ###
