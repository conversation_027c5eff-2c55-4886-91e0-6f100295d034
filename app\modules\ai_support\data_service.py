"""
Service de données sécurisé pour l'IA
Fournit uniquement les données non sensibles nécessaires pour l'assistance
"""

from flask import current_app
from app.extensions import db
from app.modules.inventory.models_product import Product, ProductCategory
from app.modules.inventory.models_ingredient import Ingredient, IngredientCategory
from app.modules.tables.models_table import Table, Room
from app.modules.pos.models_sale import Sale, SaleItem, SaleStatus
from app.modules.cash_register.models_cash_register import CashRegister
from app.modules.settings.models_settings import Settings
from sqlalchemy import func, and_
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class AIDataService:
    """Service pour fournir des données sécurisées à l'IA"""
    
    def __init__(self, user_id: int):
        self.user_id = user_id
    
    def get_user_context(self) -> dict:
        """Récupère le contexte utilisateur sécurisé"""
        try:
            # Informations de base du restaurant
            settings = Settings.query.filter_by(owner_id=self.user_id).first()
            
            # État de la caisse
            cash_register = CashRegister.get_current(self.user_id)
            
            context = {
                'restaurant_info': {
                    'name': settings.business_name if settings else 'Restaurant',
                    'type': settings.business_type if settings else 'Restaurant',
                    'currency': settings.currency if settings else 'EUR'
                },
                'cash_register': {
                    'is_open': cash_register.is_open if cash_register else False,
                    'status': 'Ouverte' if (cash_register and cash_register.is_open) else 'Fermée'
                }
            }
            
            return context
            
        except Exception as e:
            logger.error(f"Erreur lors de la récupération du contexte utilisateur: {e}")
            return {}
    
    def get_inventory_summary(self) -> dict:
        """Récupère un résumé sécurisé de l'inventaire"""
        try:
            # Produits avec stock faible
            low_stock_products = Product.query.filter(
                and_(
                    Product.owner_id == self.user_id,
                    Product.stock_quantity <= Product.minimum_stock,
                    Product.is_active == True
                )
            ).all()
            
            # Catégories de produits
            categories = ProductCategory.query.filter_by(owner_id=self.user_id).all()
            
            # Total des produits
            total_products = Product.query.filter_by(
                owner_id=self.user_id, 
                is_active=True
            ).count()
            
            # Produits indisponibles
            unavailable_products = Product.query.filter_by(
                owner_id=self.user_id,
                is_available=False
            ).all()
            
            return {
                'total_products': total_products,
                'categories_count': len(categories),
                'categories': [{'name': cat.name, 'id': cat.id} for cat in categories],
                'low_stock_products': [
                    {
                        'name': p.name,
                        'current_stock': p.stock_quantity,
                        'minimum_stock': p.minimum_stock,
                        'unit': p.unit
                    } for p in low_stock_products
                ],
                'unavailable_products': [
                    {'name': p.name, 'reason': 'Stock épuisé' if p.stock_quantity <= 0 else 'Désactivé'}
                    for p in unavailable_products
                ]
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de la récupération de l'inventaire: {e}")
            return {}
    
    def get_tables_status(self) -> dict:
        """Récupère l'état des tables de manière sécurisée"""
        try:
            # Salles et tables
            rooms = Room.query.filter_by(owner_id=self.user_id, is_active=True).all()
            
            tables_summary = {
                'rooms_count': len(rooms),
                'rooms': []
            }
            
            for room in rooms:
                tables = Table.query.filter_by(room_id=room.id).all()
                
                room_data = {
                    'name': room.name,
                    'total_tables': len(tables),
                    'available_tables': len([t for t in tables if t.is_available()]),
                    'occupied_tables': len([t for t in tables if t.status == 'occupied']),
                    'reserved_tables': len([t for t in tables if t.status == 'reserved'])
                }
                
                tables_summary['rooms'].append(room_data)
            
            return tables_summary
            
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des tables: {e}")
            return {}
    
    def get_sales_statistics(self) -> dict:
        """Récupère des statistiques de vente agrégées (non sensibles)"""
        try:
            today = datetime.now().date()
            week_ago = today - timedelta(days=7)
            
            # Statistiques du jour (agrégées)
            today_sales = Sale.query.filter(
                and_(
                    Sale.owner_id == self.user_id,
                    func.date(Sale.created_at) == today,
                    Sale.status == SaleStatus.PAID
                )
            ).all()
            
            # Statistiques de la semaine
            week_sales = Sale.query.filter(
                and_(
                    Sale.owner_id == self.user_id,
                    func.date(Sale.created_at) >= week_ago,
                    Sale.status == SaleStatus.PAID
                )
            ).count()
            
            # Produits les plus vendus (cette semaine)
            top_products = db.session.query(
                Product.name,
                func.sum(SaleItem.quantity).label('total_sold')
            ).join(SaleItem).join(Sale).filter(
                and_(
                    Sale.owner_id == self.user_id,
                    func.date(Sale.created_at) >= week_ago,
                    Sale.status == SaleStatus.PAID
                )
            ).group_by(Product.name).order_by(func.sum(SaleItem.quantity).desc()).limit(5).all()
            
            return {
                'today': {
                    'orders_count': len(today_sales),
                    'has_sales': len(today_sales) > 0
                },
                'week': {
                    'orders_count': week_sales
                },
                'top_products': [
                    {'name': name, 'quantity_sold': int(qty)} 
                    for name, qty in top_products
                ]
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des statistiques: {e}")
            return {}
    
    def get_current_orders_summary(self) -> dict:
        """Récupère un résumé des commandes en cours (non sensible)"""
        try:
            # Commandes en attente
            pending_orders = Sale.query.filter(
                and_(
                    Sale.owner_id == self.user_id,
                    Sale.status.in_([SaleStatus.PENDING, SaleStatus.KITCHEN_PENDING])
                )
            ).count()
            
            # Commandes prêtes
            ready_orders = Sale.query.filter(
                and_(
                    Sale.owner_id == self.user_id,
                    Sale.status == SaleStatus.KITCHEN_READY
                )
            ).count()
            
            return {
                'pending_orders': pending_orders,
                'ready_orders': ready_orders,
                'has_pending_orders': pending_orders > 0,
                'has_ready_orders': ready_orders > 0
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des commandes: {e}")
            return {}
    
    def get_usage_history(self) -> dict:
        """Résumé de l'historique d'utilisation de l'utilisateur"""
        from app.modules.auth.models import User
        user = User.query.get(self.user_id)
        if not user:
            return {}
        return {
            'created_at': user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at else 'N/A',
            'last_login': user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'N/A',
            # login_count simulé, à remplacer si champ réel
            'login_count': getattr(user, 'login_count', 'N/A')
        }

    def get_user_preferences(self) -> dict:
        """Préférences utilisateur (exemple, à adapter selon modèle réel)"""
        # Simulé, à remplacer par un vrai modèle si disponible
        return {
            'language': 'fr',
            'notifications': True
        }

    def get_alerts(self) -> list:
        """Génère des alertes personnalisées pour l'utilisateur"""
        alerts = []
        inv = self.get_inventory_summary()
        if inv.get('low_stock_products'):
            alerts.append("Attention : certains produits sont en stock critique.")
        stats = self.get_sales_statistics()
        if stats.get('today', {}).get('orders_count', 0) == 0:
            alerts.append("Aucune vente enregistrée aujourd'hui.")
        # Ajoute d'autres règles d’alerte ici
        return alerts

    def get_complete_context(self) -> dict:
        """Récupère le contexte complet sécurisé pour l'IA"""
        try:
            context = {
                'user_context': self.get_user_context(),
                'inventory': self.get_inventory_summary(),
                'tables': self.get_tables_status(),
                'sales_stats': self.get_sales_statistics(),
                'current_orders': self.get_current_orders_summary(),
                'usage_history': self.get_usage_history(),
                'preferences': self.get_user_preferences(),
                'alerts': self.get_alerts(),
                'timestamp': datetime.now().isoformat()
            }
            return context
        except Exception as e:
            logger.error(f"Erreur lors de la récupération du contexte complet: {e}")
            return {
                'error': 'Impossible de récupérer les données contextuelles',
                'timestamp': datetime.now().isoformat()
            }

def get_ai_context(user_id: int) -> dict:
    """Fonction utilitaire pour récupérer le contexte IA"""
    service = AIDataService(user_id)
    return service.get_complete_context()
