// Initialisation des tooltips Bootstrap
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    })
});

// Gestion des alertes flash
document.addEventListener('DOMContentLoaded', function() {
    var alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
});

// Confirmation de suppression
function confirmDelete(message) {
    return confirm(message || 'Êtes-vous sûr de vouloir supprimer cet élément ?');
}

// Format des nombres
function formatNumber(number) {
    return new Intl.NumberFormat('fr-FR').format(number);
}

// Format monétaire
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'EUR'
    }).format(amount);
}

// Gestion des formulaires dynamiques
function addFormField(containerId, template) {
    const container = document.getElementById(containerId);
    const newField = template.cloneNode(true);
    container.appendChild(newField);
}

function removeFormField(button) {
    button.closest('.form-group').remove();
}

// Fonctions spécifiques au POS
function updateCartTotal() {
    const cartItems = document.querySelectorAll('.cart-item');
    let total = 0;
    cartItems.forEach(item => {
        const quantity = parseInt(item.querySelector('.quantity').value);
        const price = parseFloat(item.querySelector('.price').dataset.price);
        total += quantity * price;
    });
    document.getElementById('cartTotal').textContent = formatCurrency(total);
}

function updateStockDisplay(productId, newQuantity) {
    const stockElement = document.querySelector(`[data-product-id="${productId}"] .stock-quantity`);
    if (stockElement) {
        stockElement.textContent = newQuantity;
    }
}

// Gestion des événements du POS
document.addEventListener('DOMContentLoaded', function() {
    // Gestion des changements de quantité
    document.querySelectorAll('.quantity').forEach(input => {
        input.addEventListener('change', function() {
            const productId = this.closest('.cart-item').dataset.productId;
            const newQuantity = parseInt(this.value);
            updateStockDisplay(productId, newQuantity);
            updateCartTotal();
        });
    });

    // Gestion des boutons de paiement
    document.querySelectorAll('.payment-method').forEach(btn => {
        btn.addEventListener('click', function() {
            const method = this.dataset.method;
            processPayment(method);
        });
    });
}); 