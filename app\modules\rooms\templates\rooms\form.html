{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">{{ title }}</h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.name.label(class="form-label") }}
                                    {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.name.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.background_color.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.background_color(class="form-control", type="color") }}
                                        <span class="input-group-text">#</span>
                                    </div>
                                    {% if form.background_color.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.background_color.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control", rows="3") }}
                            {% if form.description.errors %}
                                <div class="text-danger small">
                                    {% for error in form.description.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.width.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.width(class="form-control" + (" is-invalid" if form.width.errors else "")) }}
                                        <span class="input-group-text">px</span>
                                    </div>
                                    {% if form.width.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.width.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">Largeur recommandée: 800-1200px</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.height.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.height(class="form-control" + (" is-invalid" if form.height.errors else "")) }}
                                        <span class="input-group-text">px</span>
                                    </div>
                                    {% if form.height.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.height.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">Hauteur recommandée: 600-800px</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        {{ form.is_default(class="form-check-input") }}
                                        {{ form.is_default.label(class="form-check-label") }}
                                    </div>
                                    <div class="form-text">La salle par défaut sera sélectionnée automatiquement</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        {{ form.is_active(class="form-check-input") }}
                                        {{ form.is_active.label(class="form-check-label") }}
                                    </div>
                                    <div class="form-text">Seules les salles actives sont utilisables</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('rooms.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Prévisualisation de la couleur
    const colorInput = document.getElementById('background_color');
    if (colorInput) {
        colorInput.addEventListener('change', function() {
            // Vous pouvez ajouter une prévisualisation ici
        });
    }
});
</script>
{% endblock %}
