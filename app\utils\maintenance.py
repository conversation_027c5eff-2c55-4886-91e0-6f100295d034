import os
import shutil
import gzip
import json
import psutil
import zipfile
from datetime import datetime, timedelta
from sqlalchemy import text, inspect
from flask import current_app
from app import db, redis_client
from app.utils.files import get_file_size
from app.extensions import cache

def create_backup(owner_id, automated=False):
    """Create a database backup"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = os.path.join(current_app.config['BACKUP_DIR'], str(owner_id))
        os.makedirs(backup_dir, exist_ok=True)
        
        # Create backup file path
        backup_file = os.path.join(backup_dir, f'backup_{timestamp}.sql')
        
        # Get database URL parts
        db_url = current_app.config['SQLALCHEMY_DATABASE_URL']
        db_name = db_url.split('/')[-1]
        
        # Execute pg_dump
        os.system(f'pg_dump {db_name} > {backup_file}')
        
        # Create backup record
        backup = Backup(
            owner_id=owner_id,
            filename=os.path.basename(backup_file),
            size=os.path.getsize(backup_file),
            automated=automated
        )
        db.session.add(backup)
        db.session.commit()
        
        return True
    except Exception as e:
        current_app.logger.error(f"Failed to create backup: {str(e)}")
        return False

def restore_backup(backup_id):
    """Restore a database from backup"""
    try:
        backup = Backup.query.get(backup_id)
        if not backup:
            return False
        
        backup_file = os.path.join(
            current_app.config['BACKUP_DIR'],
            str(backup.owner_id),
            backup.filename
        )
        
        if not os.path.exists(backup_file):
            return False
        
        # Get database URL parts
        db_url = current_app.config['SQLALCHEMY_DATABASE_URL']
        db_name = db_url.split('/')[-1]
        
        # Execute psql restore
        os.system(f'psql {db_name} < {backup_file}')
        
        return True
    except Exception as e:
        current_app.logger.error(f"Failed to restore backup: {str(e)}")
        return False

def cleanup_old_backups(owner_id, retention_days):
    """Remove backups older than retention_days"""
    try:
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        old_backups = Backup.query.filter(
            Backup.owner_id == owner_id,
            Backup.created_at < cutoff_date
        ).all()
        
        for backup in old_backups:
            backup_file = os.path.join(
                current_app.config['BACKUP_DIR'],
                str(owner_id),
                backup.filename
            )
            if os.path.exists(backup_file):
                os.remove(backup_file)
            db.session.delete(backup)
        
        db.session.commit()
        return True
    except Exception as e:
        current_app.logger.error(f"Failed to cleanup old backups: {str(e)}")
        return False

def clear_cache():
    """Clear the application cache"""
    try:
        cache.clear()
        return True
    except Exception as e:
        current_app.logger.error(f"Failed to clear cache: {str(e)}")
        return False

def optimize_database(owner_id):
    """Optimize database tables"""
    try:
        # Vacuum analyze all tables
        db.session.execute(text('VACUUM ANALYZE'))
        
        # Update table statistics
        stats = get_db_table_stats()
        
        return True
    except Exception as e:
        current_app.logger.error(f"Failed to optimize database: {str(e)}")
        return False

def get_log_files():
    """Get list of log files with their sizes"""
    try:
        log_dir = current_app.config['LOG_DIR']
        log_files = []
        
        for filename in os.listdir(log_dir):
            if filename.endswith('.log'):
                file_path = os.path.join(log_dir, filename)
                log_files.append({
                    'name': filename,
                    'size': os.path.getsize(file_path),
                    'modified': datetime.fromtimestamp(os.path.getmtime(file_path))
                })
        
        return sorted(log_files, key=lambda x: x['modified'], reverse=True)
    except Exception as e:
        current_app.logger.error(f"Failed to get log files: {str(e)}")
        return []

def clear_logs():
    """Clear all log files"""
    try:
        log_dir = current_app.config['LOG_DIR']
        for filename in os.listdir(log_dir):
            if filename.endswith('.log'):
                os.remove(os.path.join(log_dir, filename))
        return True
    except Exception as e:
        current_app.logger.error(f"Failed to clear logs: {str(e)}")
        return False

def update_system_metrics(owner_id):
    """Update system metrics"""
    try:
        # Get cache size from Redis
        cache_size = 0
        if redis_client:
            cache_size = sum(redis_client.memory_usage(key) or 0 for key in redis_client.keys('*'))

        # Get logs size
        logs_size = 0
        log_dir = current_app.config['LOG_DIR']
        if os.path.exists(log_dir):
            for filename in os.listdir(log_dir):
                if filename.endswith('.log'):
                    file_path = os.path.join(log_dir, filename)
                    logs_size += os.path.getsize(file_path)

        metrics = SystemMetrics(
            owner_id=owner_id,
            cpu_usage=psutil.cpu_percent(),
            memory_usage=psutil.virtual_memory().percent,
            disk_usage=psutil.disk_usage('/').percent,
            db_size=get_database_size(),
            cache_size=cache_size,
            logs_size=logs_size,
            storage_usage=psutil.disk_usage('/').percent
        )
        db.session.add(metrics)
        db.session.commit()
        return metrics
    except Exception as e:
        current_app.logger.error(f"Failed to update system metrics: {str(e)}")
        return None

def get_database_size():
    """Get total database size in bytes"""
    try:
        result = db.session.execute(text("""
            SELECT pg_database_size(current_database())
        """)).scalar()
        return result
    except Exception as e:
        current_app.logger.error(f"Failed to get database size: {str(e)}")
        return 0

def get_db_table_stats():
    """Get statistics for all database tables"""
    try:
        stats = []
        inspector = inspect(db.engine)
        
        for table_name in inspector.get_table_names():
            # Get row count
            result = db.session.execute(
                text(f"SELECT COUNT(*) as count FROM {table_name}")
            ).first()
            row_count = result[0] if result else 0
            
            # Get table size
            result = db.session.execute(text(f"""
                SELECT pg_size_pretty(pg_total_relation_size('{table_name}')) as size,
                       pg_total_relation_size('{table_name}') as raw_size
            """)).first()
            size = result[0] if result else '0 bytes'
            raw_size = result[1] if result else 0
            
            # Get last analyzed time
            result = db.session.execute(text(f"""
                SELECT last_analyze
                FROM pg_stat_user_tables
                WHERE relname = '{table_name}'
            """)).first()
            last_analyzed = result[0] if result else None
            
            stats.append({
                'name': table_name,
                'rows': row_count,
                'size': size,
                'raw_size': raw_size,
                'last_analyzed': last_analyzed
            })
        
        return sorted(stats, key=lambda x: x['raw_size'], reverse=True)
    except Exception as e:
        current_app.logger.error(f"Failed to get database table stats: {str(e)}")
        return []

def format_size(size_in_bytes):
    """Format size in bytes to human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_in_bytes < 1024:
            return f"{size_in_bytes:.1f} {unit}"
        size_in_bytes /= 1024
    return f"{size_in_bytes:.1f} TB"

def get_cache_size():
    """Get Redis cache size in bytes"""
    try:
        info = redis_client.info()
        return info.get('used_memory', 0)
    except:
        return 0

def get_db_size():
    """Get SQLite database size in bytes"""
    try:
        db_path = current_app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
        if os.path.exists(db_path):
            return os.path.getsize(db_path)
        return 0
    except:
        return 0

def get_logs_size():
    """Get total logs size in bytes"""
    try:
        log_dir = current_app.config['LOG_DIR']
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(log_dir):
            for f in filenames:
                fp = os.path.join(dirpath, f)
                total_size += os.path.getsize(fp)
        return total_size
    except:
        return 0

def get_db_tables():
    """Get database tables info"""
    try:
        from app import db
        tables = []
        for table in db.metadata.tables.keys():
            result = db.session.execute(f"SELECT COUNT(*) as count FROM {table}")
            count = result.scalar()
            tables.append({
                'name': table,
                'rows': count,
                'size': 0,  # SQLite doesn't provide per-table size
                'size_formatted': 'N/A'
            })
        return tables
    except:
        return []

def get_log_files():
    """Get list of log files with their info"""
    try:
        log_dir = current_app.config['LOG_DIR']
        logs = []
        for f in os.listdir(log_dir):
            if f.endswith('.log'):
                path = os.path.join(log_dir, f)
                stat = os.stat(path)
                logs.append({
                    'name': f,
                    'size': stat.st_size,
                    'size_formatted': format_size(stat.st_size),
                    'modified_at': datetime.fromtimestamp(stat.st_mtime)
                })
        return sorted(logs, key=lambda x: x['modified_at'], reverse=True)
    except:
        return []

def clear_cache():
    """Clear Redis cache"""
    try:
        redis_client.flushall()
        return True
    except:
        return False

def optimize_database():
    """Optimize SQLite database"""
    try:
        from app import db
        db.session.execute("VACUUM")
        db.session.execute("ANALYZE")
        return True
    except:
        return False

def clear_logs():
    """Clear all log files"""
    try:
        log_dir = current_app.config['LOG_DIR']
        for f in os.listdir(log_dir):
            if f.endswith('.log'):
                os.remove(os.path.join(log_dir, f))
        return True
    except:
        return False

def create_logs_archive():
    """Create a zip archive of log files"""
    try:
        log_dir = current_app.config['LOG_DIR']
        if not os.path.exists(log_dir):
            return None

        # Create a temporary zip file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        zip_filename = f'logs_{timestamp}.zip'
        zip_path = os.path.join(current_app.config['TEMP_DIR'], zip_filename)

        # Create temp directory if it doesn't exist
        os.makedirs(os.path.dirname(zip_path), exist_ok=True)

        # Create zip file
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for filename in os.listdir(log_dir):
                if filename.endswith('.log'):
                    file_path = os.path.join(log_dir, filename)
                    zipf.write(file_path, filename)

        return zip_path
    except Exception as e:
        current_app.logger.error(f"Failed to create logs archive: {str(e)}")
        return None

def cleanup_temp_files():
    """Clean up old temporary files"""
    try:
        temp_dir = current_app.config['TEMP_DIR']
        if not os.path.exists(temp_dir):
            return True

        # Remove files older than 24 hours
        cutoff_time = datetime.now() - timedelta(hours=24)
        for filename in os.listdir(temp_dir):
            file_path = os.path.join(temp_dir, filename)
            if os.path.getctime(file_path) < cutoff_time.timestamp():
                os.remove(file_path)
        return True
    except Exception as e:
        current_app.logger.error(f"Failed to cleanup temp files: {str(e)}")
        return False

def ensure_temp_dir():
    """Ensure temporary directory exists"""
    try:
        temp_dir = current_app.config['TEMP_DIR']
        os.makedirs(temp_dir, exist_ok=True)
        return True
    except Exception as e:
        current_app.logger.error(f"Failed to create temp directory: {str(e)}")
        return False
  