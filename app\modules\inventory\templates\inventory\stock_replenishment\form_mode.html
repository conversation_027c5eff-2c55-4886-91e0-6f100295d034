{% extends "base.html" %}

{% block title %}Mode Formulaire - Approvisionnement{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
<script src="{{ url_for('inventory.static', filename='js/stock_replenishment_form.js') }}"></script>
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête Mode Formulaire -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-2">
                <h4 class="mb-0">
                    <i class="fas fa-edit"></i> Mode Formulaire - Approvisionnement
                </h4>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.stock_replenishment_pos_mode') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-cash-register"></i> Mode POS
                    </a>
                    <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- Colonne gauche - Formulaire -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-plus-circle"></i> Ajouter des Articles à la Commande</h5>
                    </div>
                    <div class="card-body">
                        <form id="stockReplenishmentForm">
                            {{ form.hidden_tag() }}
                            
                            <!-- Sélection du fournisseur -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.supplier_category_id.label }}</label>
                                    {{ form.supplier_category_id(class="form-select", id="supplierCategorySelect") }}
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.supplier_id.label }}</label>
                                    {{ form.supplier_id(class="form-select", id="supplierSelect") }}
                                </div>
                            </div>

                            <!-- Type d'article -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.item_type.label }}</label>
                                    {{ form.item_type(class="form-select", id="itemTypeSelect") }}
                                </div>
                                <div class="col-md-6">
                                    <!-- Sélection de l'article (dynamique) -->
                                    <label class="form-label" id="itemSelectLabel">Article</label>
                                    <div id="productSelectDiv" style="display: none;">
                                        {{ form.product_id(class="form-select", id="productSelect") }}
                                    </div>
                                    <div id="ingredientSelectDiv" style="display: none;">
                                        {{ form.ingredient_id(class="form-select", id="ingredientSelect") }}
                                    </div>
                                </div>
                            </div>

                            <!-- Quantité et prix -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label class="form-label">{{ form.quantity.label }}</label>
                                    {{ form.quantity(class="form-control", step="0.01", min="0.01") }}
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">{{ form.unit_price.label }}</label>
                                    {{ form.unit_price(class="form-control", step="0.01", min="0") }}
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Total</label>
                                    <input type="text" class="form-control" id="totalPrice" readonly>
                                </div>
                            </div>

                            <!-- Date de livraison -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.expected_delivery_date.label }}</label>
                                    {{ form.expected_delivery_date(class="form-control", type="datetime-local") }}
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mt-4">
                                        {{ form.is_order_only(class="form-check-input") }}
                                        <label class="form-check-label" for="{{ form.is_order_only.id }}">
                                            {{ form.is_order_only.label.text }}
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="mb-3">
                                <label class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes(class="form-control", rows="3") }}
                            </div>

                            <!-- Boutons d'action -->
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                    <i class="fas fa-undo"></i> Réinitialiser
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Ajouter à la commande
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Informations sur l'article sélectionné -->
                <div class="card mt-3" id="itemInfoCard" style="display: none;">
                    <div class="card-header">
                        <h6><i class="fas fa-info-circle"></i> Informations sur l'article</h6>
                    </div>
                    <div class="card-body" id="itemInfoContent">
                        <!-- Contenu dynamique -->
                    </div>
                </div>
            </div>

            <!-- Colonne droite - Commande en cours -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-shopping-cart"></i> Commande en Cours</h5>
                    </div>
                    <div class="card-body">
                        <!-- Informations de la commande -->
                        <div class="order-info mb-3">
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">Référence:</small><br>
                                    <strong id="orderReference">PO{{ moment().strftime('%y%m%d%H%M%S') if moment else now.strftime('%y%m%d%H%M%S') }}</strong>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Date:</small><br>
                                    <strong>{{ moment().strftime('%d/%m/%Y') if moment else now.strftime('%d/%m/%Y') }}</strong>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <small class="text-muted">Fournisseur:</small><br>
                                    <strong id="selectedSupplierName">Autres</strong>
                                </div>
                            </div>
                        </div>

                        <!-- Liste des articles -->
                        <div class="order-items" id="orderItems">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-box-open fa-2x mb-2"></i>
                                <p>Aucun article ajouté</p>
                            </div>
                        </div>

                        <!-- Total -->
                        <div class="order-total mt-3 pt-3 border-top">
                            <div class="d-flex justify-content-between">
                                <strong>Total:</strong>
                                <strong id="orderTotal">0.00 €</strong>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="order-actions mt-3">
                            <button class="btn btn-outline-danger btn-sm w-100 mb-2" onclick="clearOrder()">
                                <i class="fas fa-trash"></i> Vider la commande
                            </button>
                            <button class="btn btn-success w-100" id="finalizeOrderBtn" onclick="finalizeOrder()" disabled>
                                <i class="fas fa-check"></i> Finaliser la commande
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Raccourcis -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-bolt"></i> Raccourcis</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="loadLastOrder()">
                                <i class="fas fa-history"></i> Dernière commande
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="loadTemplate()">
                                <i class="fas fa-template"></i> Modèle de commande
                            </button>
                            <button class="btn btn-outline-warning btn-sm" onclick="showLowStockItems()">
                                <i class="fas fa-exclamation-triangle"></i> Articles en stock bas
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de finalisation -->
<div class="modal fade" id="finalizeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle"></i> Finaliser la Commande
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Résumé de la commande -->
                <div class="order-summary mb-4">
                    <h6>Résumé de la commande</h6>
                    <div id="orderSummaryContent">
                        <!-- Contenu dynamique -->
                    </div>
                </div>

                <!-- Options de traitement -->
                <div class="processing-options">
                    <h6>Options de traitement</h6>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="processingType" 
                               id="receiveAndPay" value="receive_and_pay" checked>
                        <label class="form-check-label" for="receiveAndPay">
                            Recevoir la marchandise et payer maintenant
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="processingType" 
                               id="receivePayLater" value="receive_pay_later">
                        <label class="form-check-label" for="receivePayLater">
                            Recevoir la marchandise et payer plus tard
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="processingType" 
                               id="orderOnly" value="order_only">
                        <label class="form-check-label" for="orderOnly">
                            Bon de commande seulement (ne pas mettre à jour le stock)
                        </label>
                    </div>
                </div>

                <!-- Notes générales -->
                <div class="mt-3">
                    <label class="form-label">Notes générales</label>
                    <textarea class="form-control" id="generalNotes" rows="3" 
                              placeholder="Notes supplémentaires pour cette commande..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    Annuler
                </button>
                <button type="button" class="btn btn-success" onclick="submitOrder()">
                    <i class="fas fa-paper-plane"></i> Envoyer la commande
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Variables globales pour le mode formulaire
window.formModeData = {
    currentOrder: [],
    selectedSupplier: null,
    orderTotal: 0
};
</script>
{% endblock %}
