import pytest
from app.modules.auth.models import User, UserRole
from app.extensions import db

def test_new_user(app):
    """
    GIVEN a User model
    WHEN a new User is created
    THEN check the email, password_hash, and role fields are defined correctly
    """
    with app.app_context():
        user = User(
            username='test_user',
            email='<EMAIL>',
            role=UserRole.MANAGER
        )
        user.set_password('test_password')
        db.session.add(user)
        db.session.commit()

        assert user.email == '<EMAIL>'
        assert user.username == 'test_user'
        assert user.role == UserRole.MANAGER
        assert user.password_hash is not None
        assert user.password_hash != 'test_password'

def test_password_hashing(app):
    """
    GIVEN a User model
    WHEN setting the user's password
    THEN check the password is hashed and verifies correctly
    """
    with app.app_context():
        user = User(
            username='test_user',
            email='<EMAIL>'
        )
        user.set_password('test_password')
        
        assert user.check_password('test_password') is True
        assert user.check_password('wrong_password') is False

def test_user_roles(app):
    """
    GIVEN a User model
    WHEN creating users with different roles
    THEN check the roles are assigned correctly
    """
    with app.app_context():
        owner = User(username='owner', email='<EMAIL>', role=UserRole.OWNER)
        admin = User(username='admin', email='<EMAIL>', role=UserRole.ADMIN)
        manager = User(username='manager', email='<EMAIL>', role=UserRole.MANAGER)
        cashier = User(username='cashier', email='<EMAIL>', role=UserRole.CASHIER)

        db.session.add_all([owner, admin, manager, cashier])
        db.session.commit()

        assert owner.role == UserRole.OWNER
        assert admin.role == UserRole.ADMIN
        assert manager.role == UserRole.MANAGER
        assert cashier.role == UserRole.CASHIER

def test_user_representation(app):
    """
    GIVEN a User model
    WHEN representing the user as a string
    THEN check the string representation is correct
    """
    with app.app_context():
        user = User(username='test_user', email='<EMAIL>')
        assert str(user) == '<User test_user>'

def test_unique_email_constraint(app):
    """
    GIVEN a User model
    WHEN creating two users with the same email
    THEN check it raises an integrity error
    """
    with app.app_context():
        user1 = User(username='user1', email='<EMAIL>')
        user2 = User(username='user2', email='<EMAIL>')

        db.session.add(user1)
        db.session.commit()

        db.session.add(user2)
        with pytest.raises(Exception):  # SQLAlchemy IntegrityError
            db.session.commit()
        db.session.rollback() 