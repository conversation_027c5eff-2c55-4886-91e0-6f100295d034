{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-3">
            <a href="{{ url_for('inventory.add_product_category') }}" class="btn btn-primary w-100">
                <i class="fas fa-plus"></i> Nouvelle catégorie
            </a>
        </div>
        <div class="col-md-9">
            <form method="GET" class="row g-3">
                <div class="col-md-7">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" 
                               placeholder="Rechercher une catégorie..." value="{{ search }}">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="per_page" onchange="this.form.submit()">
                        {% for n in [10, 25, 50, 100] %}
                        <option value="{{ n }}" {% if per_page == n %}selected{% endif %}>
                            {{ n }} par page
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <a href="{{ url_for('inventory.product_categories') }}" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des catégories -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th style="width: 80px">Image</th>
                            <th>Nom</th>
                            <th>Description</th>
                            <th>Statistiques</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in categories.items %}
                        <tr>
                            <td>
                                {% if category.image_path %}
                                <img src="{{ url_for('static', filename=category.image_path) }}" 
                                     alt="{{ category.name }}"
                                     class="img-thumbnail"
                                     style="width: 60px; height: 60px; object-fit: cover;">
                                {% else %}
                                <div class="bg-light d-flex align-items-center justify-content-center rounded" 
                                     style="width: 60px; height: 60px;">
                                    <i class="fas fa-folder text-muted"></i>
                                </div>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge" style="background-color: {{ category.color or '#6c757d' }}">
                                    {{ category.name }}
                                </span>
                            </td>
                            <td>
                                <small class="text-muted">{{ category.description or 'Aucune description' }}</small>
                            </td>
                            <td>
                                <div class="small">
                                    <div class="mb-1">
                                        <i class="fas fa-box"></i> Total produits: 
                                        <strong>{{ stats[category.id].total_products }}</strong>
                                    </div>
                                    <div class="mb-1">
                                        <i class="fas fa-check-circle text-success"></i> Produits actifs: 
                                        <strong>{{ stats[category.id].active_products }}</strong>
                                    </div>
                                    <div>
                                        <i class="fas fa-exclamation-circle text-danger"></i> En rupture: 
                                        <strong>{{ stats[category.id].out_of_stock }}</strong>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('inventory.edit_product_category', id=category.id) }}" 
                                       class="btn btn-outline-primary btn-sm"
                                       title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if stats[category.id].total_products == 0 %}
                                    <form action="{{ url_for('inventory.delete_product_category', id=category.id) }}" 
                                          method="POST" 
                                          class="d-inline"
                                          onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ?')">
                                        <button type="submit" class="btn btn-outline-danger btn-sm" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                    {% else %}
                                    <button class="btn btn-outline-danger btn-sm" 
                                            title="Impossible de supprimer une catégorie contenant des produits"
                                            disabled>
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="5" class="text-center py-4">
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-info-circle"></i> Aucune catégorie trouvée.
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if categories.pages > 1 %}
            <nav aria-label="Navigation des pages" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if categories.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inventory.product_categories', page=categories.prev_num, search=search, per_page=per_page) }}">
                            Précédent
                        </a>
                    </li>
                    {% endif %}

                    {% for page_num in range(1, categories.pages + 1) %}
                    <li class="page-item {{ 'active' if page_num == categories.page else '' }}">
                        <a class="page-link" href="{{ url_for('inventory.product_categories', page=page_num, search=search, per_page=per_page) }}">
                            {{ page_num }}
                        </a>
                    </li>
                    {% endfor %}

                    {% if categories.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inventory.product_categories', page=categories.next_num, search=search, per_page=per_page) }}">
                            Suivant
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}


