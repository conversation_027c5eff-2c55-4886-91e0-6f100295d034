from app import db
from datetime import datetime

class Supplier(db.Model):
    __tablename__ = 'suppliers'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    category_id = db.Column(db.<PERSON>, db.<PERSON><PERSON>('supplier_categories.id'), nullable=True)
    contact_name = db.Column(db.String(100))
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    website = db.Column(db.String(200))
    tax_id = db.Column(db.String(50))  # Numéro de TVA ou SIRET
    payment_terms = db.Column(db.String(100))  # Conditions de paiement
    address = db.Column(db.Text)
    notes = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    rating = db.Column(db.Integer, default=0)  # Note de 0 à 5
    owner_id = db.Column(db.Integer, db.<PERSON><PERSON><PERSON>('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Supplier {self.name}>'

    @property
    def category_name(self):
        """Nom de la catégorie du fournisseur"""
        return self.category.name if self.category else 'Sans catégorie'

    @property
    def display_rating(self):
        """Affichage de la note sous forme d'étoiles"""
        if self.rating == 0:
            return 'Non noté'
        return '★' * self.rating + '☆' * (5 - self.rating)