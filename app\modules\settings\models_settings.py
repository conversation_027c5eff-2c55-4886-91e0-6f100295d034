from app import db
from datetime import datetime
# from sqlalchemy import Column, String, Boolean, Float  # Ajoutez cette ligne

class Settings(db.Model):
    __tablename__ = 'settings'
    
    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('users.id'), nullable=False)
    
    cash_register_name = db.Column(db.String(100))  # Assurez-vous que cet attribut existe
    cash_register_number = db.Column(db.String(100))
    require_float = db.Column(db.Boolean)
    default_float = db.Column(db.Float)
    require_close = db.Column(db.Boolean)
    allow_delete = db.Column(db.Boolean)
    allow_void = db.Column(db.Boolean)
    require_reason = db.Column(db.Boolean)
    # Business Settings
    business_name = db.Column(db.String(100))
    business_type = db.Column(db.String(50))
    address = db.Column(db.Text)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    website = db.Column(db.String(200))
    logo_path = db.Column(db.String(255))
    favicon_path = db.Column(db.String(255))
    app_name = db.Column(db.String(100))
    currency = db.Column(db.String(3), default='EUR')
    timezone = db.Column(db.String(50), default='Europe/Paris')
    language = db.Column(db.String(2), default='fr')
    
    # Theme Settings
    primary_color = db.Column(db.String(7), default='#4e73df')
    secondary_color = db.Column(db.String(7), default='#858796')
    theme = db.Column(db.String(20), default='light')
    font = db.Column(db.String(50), default='nunito')
    
    # Layout Settings
    sidebar_position = db.Column(db.String(10), default='left')
    container_width = db.Column(db.String(10), default='fluid')
    sticky_header = db.Column(db.Boolean, default=True)
    sticky_footer = db.Column(db.Boolean, default=True)
    
    # Email Templates
    email_templates = db.Column(db.JSON)
    
    # Payment Settings
    accept_cash = db.Column(db.Boolean, default=True)
    accept_card = db.Column(db.Boolean, default=True)
    accept_check = db.Column(db.Boolean, default=False)
    accept_transfer = db.Column(db.Boolean, default=False)
    accept_mobile_payment = db.Column(db.Boolean, default=False)
    default_payment_method = db.Column(db.String(20), default='cash')
    allow_partial_payment = db.Column(db.Boolean, default=False)
    allow_split_payment = db.Column(db.Boolean, default=True)
    require_payment_reference = db.Column(db.Boolean, default=False)
    round_amounts = db.Column(db.String(10), default='none')
    accepted_payment_methods = db.Column(db.JSON)  # Store list of accepted payment methods
    allow_refund = db.Column(db.Boolean, default=True)
    refund_limit = db.Column(db.Float, default=0)
    require_approval = db.Column(db.Boolean, default=False)
    approval_limit = db.Column(db.Float, default=0)
    
    # Card Payment Settings
    card_terminal = db.Column(db.String(20), default='none')
    card_minimum = db.Column(db.Float, default=0)
    card_fee = db.Column(db.Float, default=0)
    
    # Tax Settings
    tax_number = db.Column(db.String(50))
    tax_enabled = db.Column(db.Boolean, default=True)
    prices_include_tax = db.Column(db.Boolean, default=True)
    default_tax_rate = db.Column(db.Float, default=20.0)
    
    # Receipt Settings
    receipt_header = db.Column(db.Text)
    receipt_footer = db.Column(db.Text)
    receipt_header_image = db.Column(db.String(255))
    receipt_footer_image = db.Column(db.String(255))
    show_logo = db.Column(db.Boolean, default=True)
    show_tax = db.Column(db.Boolean, default=True)
    show_order_number = db.Column(db.Boolean, default=True)
    show_server_name = db.Column(db.Boolean, default=True)
    show_table_number = db.Column(db.Boolean, default=True)
    date_format = db.Column(db.String(20), default='%d/%m/%Y %H:%M')
    font_family = db.Column(db.String(50), default='Arial')
    font_size = db.Column(db.Integer, default=10)
    line_spacing = db.Column(db.Float, default=1.2)
    paper_size = db.Column(db.String(20), default='80mm')
    paper_width = db.Column(db.Float, default=80)
    margin_top = db.Column(db.Float, default=5)
    margin_bottom = db.Column(db.Float, default=5)
    margin_left = db.Column(db.Float, default=5)
    margin_right = db.Column(db.Float, default=5)
    
    # Sale Settings
    allow_decimal_quantity = db.Column(db.Boolean, default=True)
    allow_price_change = db.Column(db.Boolean, default=False)
    allow_discount = db.Column(db.Boolean, default=True)
    require_customer_info = db.Column(db.Boolean, default=False)
    require_table_selection = db.Column(db.Boolean, default=False)
    enable_kitchen_print = db.Column(db.Boolean, default=False)
    enable_low_stock_alert = db.Column(db.Boolean, default=True)
    
    # Cash Register Settings
    require_opening_amount = db.Column(db.Boolean, default=True)
    require_closing_amount = db.Column(db.Boolean, default=True)
    allow_negative_balance = db.Column(db.Boolean, default=False)
    max_difference = db.Column(db.Float, default=10.0)
    
    # System Settings
    session_lifetime = db.Column(db.Integer, default=60)  # minutes
    max_login_attempts = db.Column(db.Integer, default=5)
    password_expiry_days = db.Column(db.Integer, default=90)
    enable_2fa = db.Column(db.Boolean, default=False)
    
    # Alert Settings
    enable_email_alerts = db.Column(db.Boolean, default=True)
    alert_email = db.Column(db.String(120))
    low_stock_threshold = db.Column(db.Float, default=10)
    high_amount_threshold = db.Column(db.Float, default=1000)
    monthly_budget = db.Column(db.Float, default=0)  # Monthly budget for expenses
    
    # Maintenance Settings
    backup_enabled = db.Column(db.Boolean, default=True)
    backup_frequency = db.Column(db.String(20), default='daily')
    backup_time = db.Column(db.Time, default=datetime.strptime('00:00', '%H:%M').time())
    backup_retention = db.Column(db.Integer, default=30)  # days
    backup_location = db.Column(db.String(255))
    backup_compress = db.Column(db.Boolean, default=True)
    last_backup = db.Column(db.DateTime)
    maintenance_mode = db.Column(db.Boolean, default=False)
    maintenance_message = db.Column(db.Text, default='Site en maintenance. Veuillez réessayer plus tard.')
    allowed_ips = db.Column(db.Text)  # Comma-separated list of allowed IPs during maintenance
    cleanup_enabled = db.Column(db.Boolean, default=True)
    cleanup_frequency = db.Column(db.String(20), default='daily')
    cleanup_age = db.Column(db.Integer, default=30)  # days
    optimize_tables = db.Column(db.Boolean, default=True)
    optimize_frequency = db.Column(db.String(20), default='weekly')

    # Online Ordering Settings
    online_ordering_enabled = db.Column(db.Boolean, default=False)
    online_ordering_subdomain = db.Column(db.String(50))
    online_ordering_site_name = db.Column(db.String(100))
    online_ordering_description = db.Column(db.Text)
    online_ordering_primary_color = db.Column(db.String(7), default='#4e73df')
    online_ordering_secondary_color = db.Column(db.String(7), default='#858796')
    online_ordering_logo_path = db.Column(db.String(255))
    online_ordering_banner_path = db.Column(db.String(255))

    # Delivery Settings
    delivery_enabled = db.Column(db.Boolean, default=True)
    pickup_enabled = db.Column(db.Boolean, default=True)
    dine_in_enabled = db.Column(db.Boolean, default=True)
    drive_through_enabled = db.Column(db.Boolean, default=False)
    delivery_fee = db.Column(db.Float, default=0.0)
    minimum_order_amount = db.Column(db.Float, default=0.0)
    delivery_radius = db.Column(db.Float, default=10.0)  # en km

    # Online Ordering Hours (JSON format)
    online_ordering_hours = db.Column(db.JSON)
    delivery_hours = db.Column(db.JSON)
    pickup_hours = db.Column(db.JSON)

    # Notification Settings for Online Orders
    notify_kitchen_online_orders = db.Column(db.Boolean, default=True)
    notify_customer_order_confirmed = db.Column(db.Boolean, default=True)
    notify_customer_order_ready = db.Column(db.Boolean, default=True)
    notify_customer_out_for_delivery = db.Column(db.Boolean, default=True)
    auto_assign_deliverer = db.Column(db.Boolean, default=False)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Settings {self.business_name}>' 