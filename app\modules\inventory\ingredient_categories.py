from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app.extensions import db
from app.modules.inventory.models_ingredient import IngredientCategory, Ingredient
from app.utils.decorators import inventory_access_required as permission_required
from . import bp
from .forms import IngredientCategoryForm
import os

@bp.route('/ingredient-categories')
@login_required
@permission_required
def ingredient_categories():
    """Liste des catégories d'ingrédients"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    search = request.args.get('search', '')
    
    query = IngredientCategory.query.filter_by(owner_id=current_user.id)
    
    if search:
        query = query.filter(IngredientCategory.name.ilike(f'%{search}%'))
    
    categories = query.order_by(IngredientCategory.name).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Calculer les statistiques pour chaque catégorie
    stats = {}
    for category in categories.items:
        total_ingredients = category.ingredients.count()
        
        # Calculer les ingrédients en rupture de stock en utilisant la syntaxe SQLAlchemy correcte
        out_of_stock = Ingredient.query.filter(
            Ingredient.category_id == category.id,
            Ingredient.owner_id == current_user.id,
            Ingredient.stock_quantity <= Ingredient.minimum_stock
        ).count()
        
        stats[category.id] = {
            'total_ingredients': total_ingredients,
            'out_of_stock': out_of_stock
        }
    
    return render_template('inventory/ingredient_categories/list.html',
                         title='Catégories d\'ingrédients',
                         categories=categories,
                         search=search,
                         per_page=per_page,
                         stats=stats)

@bp.route('/ingredient-categories/add', methods=['GET', 'POST'])
@login_required
@permission_required
def add_ingredient_category():
    """Ajouter une catégorie d'ingrédients"""
    form = IngredientCategoryForm()
    
    if form.validate_on_submit():
        category = IngredientCategory(
            name=form.name.data,
            description=form.description.data,
            owner_id=current_user.id
        )
        
        if form.image.data:
            try:
                from app.utils.files import save_image
                image_path = save_image(form.image.data, 'ingredient_categories')
                if image_path:
                    category.image_path = image_path
                else:
                    flash("Erreur lors de l'upload de l'image. La catégorie sera créée sans image.", "warning")
            except Exception as e:
                flash(f"Erreur lors de l'upload de l'image: {str(e)}", "error")
        
        db.session.add(category)
        db.session.commit()
        flash('Catégorie ajoutée avec succès!', 'success')
        return redirect(url_for('inventory.ingredient_categories'))
    
    return render_template('inventory/ingredient_categories/form.html',
                         title='Ajouter une catégorie',
                         form=form)

@bp.route('/ingredient-categories/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required
def edit_ingredient_category(id):
    """Modifier une catégorie d'ingrédients"""
    category = IngredientCategory.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    form = IngredientCategoryForm(obj=category)
    
    if form.validate_on_submit():
        category.name = form.name.data
        category.description = form.description.data
        
        if form.image.data:
            try:
                # Supprimer l'ancienne image si elle existe
                if category.image_path:
                    old_image_path = os.path.join('app', 'static', category.image_path)
                    if os.path.exists(old_image_path):
                        os.remove(old_image_path)
                
                # Enregistrer la nouvelle image
                from app.utils.files import save_image
                image_path = save_image(form.image.data, 'ingredient_categories')
                if image_path:
                    category.image_path = image_path
                else:
                    flash("Erreur lors de l'upload de l'image. La catégorie sera mise à jour sans nouvelle image.", "warning")
            except Exception as e:
                flash(f"Erreur lors de l'upload de l'image: {str(e)}", "error")
        
        db.session.commit()
        flash('Catégorie modifiée avec succès!', 'success')
        return redirect(url_for('inventory.ingredient_categories'))
    
    return render_template('inventory/ingredient_categories/form.html',
                         title='Modifier une catégorie',
                         form=form,
                         category=category)

@bp.route('/ingredient-categories/<int:id>/delete', methods=['POST'])
@login_required
@permission_required
def delete_ingredient_category(id):
    """Supprimer une catégorie d'ingrédients"""
    category = IngredientCategory.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    
    # Vérifier si des ingrédients sont liés à cette catégorie
    if category.ingredients.count() > 0:
        flash('Impossible de supprimer cette catégorie car elle contient des ingrédients.', 'error')
        return redirect(url_for('inventory.ingredient_categories'))
    
    db.session.delete(category)
    db.session.commit()
    flash('Catégorie supprimée avec succès!', 'success')
    return redirect(url_for('inventory.ingredient_categories')) 