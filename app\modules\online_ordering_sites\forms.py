from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, TextAreaField, BooleanField, FloatField, SelectField, HiddenField
from wtforms.validators import DataRequired, Email, Length, EqualTo, Optional, NumberRange, ValidationError
from wtforms.widgets import TextArea

class CustomerRegistrationForm(FlaskForm):
    """Formulaire d'inscription pour les clients"""
    first_name = StringField('Prénom', validators=[
        DataRequired(message='Le prénom est requis'),
        Length(min=2, max=50, message='Le prénom doit contenir entre 2 et 50 caractères')
    ])
    
    last_name = StringField('Nom', validators=[
        DataRequired(message='Le nom est requis'),
        Length(min=2, max=50, message='Le nom doit contenir entre 2 et 50 caractères')
    ])
    
    email = StringField('Email', validators=[
        DataRequired(message='L\'email est requis'),
        Email(message='<PERSON>euillez entrer une adresse email valide'),
        Length(max=120, message='L\'email ne peut pas dépasser 120 caractères')
    ])
    
    phone = StringField('Téléphone', validators=[
        DataRequired(message='Le numéro de téléphone est requis'),
        Length(min=10, max=20, message='Le numéro de téléphone doit contenir entre 10 et 20 caractères')
    ])
    
    password = PasswordField('Mot de passe', validators=[
        DataRequired(message='Le mot de passe est requis'),
        Length(min=6, message='Le mot de passe doit contenir au moins 6 caractères')
    ])
    
    password_confirm = PasswordField('Confirmer le mot de passe', validators=[
        DataRequired(message='Veuillez confirmer votre mot de passe'),
        EqualTo('password', message='Les mots de passe ne correspondent pas')
    ])
    
    address = TextAreaField('Adresse', validators=[
        DataRequired(message='L\'adresse est requise'),
        Length(min=10, max=500, message='L\'adresse doit contenir entre 10 et 500 caractères')
    ], widget=TextArea())
    
    city = StringField('Ville', validators=[
        DataRequired(message='La ville est requise'),
        Length(min=2, max=50, message='La ville doit contenir entre 2 et 50 caractères')
    ])
    
    postal_code = StringField('Code postal', validators=[
        Optional(),
        Length(max=10, message='Le code postal ne peut pas dépasser 10 caractères')
    ])
    
    country = StringField('Pays', validators=[
        DataRequired(message='Le pays est requis'),
        Length(min=2, max=50, message='Le pays doit contenir entre 2 et 50 caractères')
    ])
    
    email_notifications = BooleanField('Recevoir les notifications par email', default=True)
    sms_notifications = BooleanField('Recevoir les notifications par SMS', default=True)
    
    def validate_email(self, field):
        """Vérifier que l'email n'est pas déjà utilisé"""
        from app.modules.online_ordering_sites.models import CustomerUser
        if CustomerUser.query.filter_by(email=field.data).first():
            raise ValidationError('Cette adresse email est déjà utilisée.')

class CustomerLoginForm(FlaskForm):
    """Formulaire de connexion pour les clients"""
    email = StringField('Email', validators=[
        DataRequired(message='L\'email est requis'),
        Email(message='Veuillez entrer une adresse email valide')
    ])
    
    password = PasswordField('Mot de passe', validators=[
        DataRequired(message='Le mot de passe est requis')
    ])
    
    remember_me = BooleanField('Se souvenir de moi')

class OnlineOrderingSettingsForm(FlaskForm):
    """Formulaire pour les paramètres de commande en ligne"""
    # Activation
    online_ordering_enabled = BooleanField('Activer la commande en ligne')
    
    # Configuration du site
    online_ordering_subdomain = StringField('Sous-domaine', validators=[
        Optional(),
        Length(min=3, max=50, message='Le sous-domaine doit contenir entre 3 et 50 caractères')
    ], description='Ex: monrestaurant (accessible via monrestaurant.lvh.me:5000)')
    
    online_ordering_site_name = StringField('Nom du site', validators=[
        Optional(),
        Length(max=100, message='Le nom du site ne peut pas dépasser 100 caractères')
    ])
    
    online_ordering_description = TextAreaField('Description du site', validators=[
        Optional(),
        Length(max=500, message='La description ne peut pas dépasser 500 caractères')
    ])
    
    # Personnalisation
    online_ordering_primary_color = StringField('Couleur principale', validators=[
        Optional(),
        Length(min=7, max=7, message='La couleur doit être au format #RRGGBB')
    ], default='#4e73df')
    
    online_ordering_secondary_color = StringField('Couleur secondaire', validators=[
        Optional(),
        Length(min=7, max=7, message='La couleur doit être au format #RRGGBB')
    ], default='#858796')
    
    # Services disponibles
    delivery_enabled = BooleanField('Livraison à domicile', default=True)
    pickup_enabled = BooleanField('À emporter', default=True)
    dine_in_enabled = BooleanField('Sur place', default=True)
    drive_through_enabled = BooleanField('Drive (au volant)', default=False)
    
    # Configuration de livraison
    delivery_fee = FloatField('Frais de livraison (€)', validators=[
        Optional(),
        NumberRange(min=0, max=50, message='Les frais de livraison doivent être entre 0 et 50€')
    ], default=0.0)
    
    minimum_order_amount = FloatField('Commande minimum (€)', validators=[
        Optional(),
        NumberRange(min=0, max=500, message='La commande minimum doit être entre 0 et 500€')
    ], default=0.0)
    
    delivery_radius = FloatField('Rayon de livraison (km)', validators=[
        Optional(),
        NumberRange(min=1, max=50, message='Le rayon de livraison doit être entre 1 et 50 km')
    ], default=10.0)
    
    # Notifications
    notify_kitchen_online_orders = BooleanField('Notifier la cuisine des commandes en ligne', default=True)
    notify_customer_order_confirmed = BooleanField('Notifier le client quand la commande est confirmée', default=True)
    notify_customer_order_ready = BooleanField('Notifier le client quand la commande est prête', default=True)
    notify_customer_out_for_delivery = BooleanField('Notifier le client quand la commande est en livraison', default=True)
    auto_assign_deliverer = BooleanField('Assigner automatiquement un livreur', default=False)
    
    def validate_online_ordering_subdomain(self, field):
        """Vérifier que le sous-domaine n'est pas déjà utilisé"""
        if field.data:
            # Vérifier les sous-domaines réservés
            reserved_subdomains = ['all_businesses', 'www', 'admin', 'api', 'app']
            if field.data.lower() in reserved_subdomains:
                raise ValidationError('Ce sous-domaine est réservé.')

            # Vérifier que le sous-domaine n'est pas déjà utilisé par un autre site
            from app.modules.online_ordering_sites.models import OnlineOrderingSite
            from flask_login import current_user
            existing_site = OnlineOrderingSite.query.filter_by(subdomain=field.data).first()
            if existing_site and existing_site.owner_id != current_user.id:
                raise ValidationError('Ce sous-domaine est déjà utilisé.')

class DeliveryAddressForm(FlaskForm):
    """Formulaire pour ajouter/modifier une adresse de livraison"""
    name = StringField('Nom de l\'adresse', validators=[
        DataRequired(message='Le nom de l\'adresse est requis'),
        Length(min=2, max=50, message='Le nom doit contenir entre 2 et 50 caractères')
    ], description='Ex: Maison, Bureau, etc.')
    
    address = TextAreaField('Adresse complète', validators=[
        DataRequired(message='L\'adresse est requise'),
        Length(min=10, max=500, message='L\'adresse doit contenir entre 10 et 500 caractères')
    ])
    
    city = StringField('Ville', validators=[
        DataRequired(message='La ville est requise'),
        Length(min=2, max=50, message='La ville doit contenir entre 2 et 50 caractères')
    ])
    
    postal_code = StringField('Code postal', validators=[
        Optional(),
        Length(max=10, message='Le code postal ne peut pas dépasser 10 caractères')
    ])
    
    instructions = TextAreaField('Instructions de livraison', validators=[
        Optional(),
        Length(max=500, message='Les instructions ne peuvent pas dépasser 500 caractères')
    ], description='Ex: Sonner à l\'interphone, 2ème étage, etc.')
    
    is_default = BooleanField('Définir comme adresse par défaut')

class OrderForm(FlaskForm):
    """Formulaire pour passer une commande"""
    order_type = SelectField('Type de commande', choices=[
        ('delivery', 'Livraison à domicile'),
        ('pickup', 'À emporter'),
        ('dine_in', 'Sur place'),
        ('drive_through', 'Drive (au volant)')
    ], validators=[DataRequired(message='Veuillez choisir un type de commande')])
    
    delivery_address_id = SelectField('Adresse de livraison', coerce=int, validators=[Optional()])
    table_number = StringField('Numéro de table', validators=[Optional()])
    
    customer_notes = TextAreaField('Notes pour la commande', validators=[
        Optional(),
        Length(max=500, message='Les notes ne peuvent pas dépasser 500 caractères')
    ])
    
    requested_delivery_time = StringField('Heure de livraison souhaitée', validators=[Optional()])
    
    # Champs cachés pour les données du panier
    cart_data = HiddenField('Données du panier', validators=[DataRequired()])
