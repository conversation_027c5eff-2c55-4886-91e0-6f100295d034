<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Ticket de caisse</title>
    <style>
        @media print {
            body {
                margin: 0;
                padding: {{ settings.margin_top }}mm {{ settings.margin_right }}mm {{ settings.margin_bottom }}mm {{ settings.margin_left }}mm;
                font-family: {{ settings.font_family }};
                font-size: {{ settings.font_size }}pt;
                line-height: {{ settings.line_spacing }};
                width: {{ settings.paper_width }}mm;
            }
            
            .receipt {
                width: 100%;
            }
            
            table {
                width: 100%;
                border-collapse: collapse;
            }
            
            th, td {
                padding: 2px 4px;
            }
            
            .text-end {
                text-align: right;
            }
            
            .text-center {
                text-align: center;
            }
            
            .mb-0 { margin-bottom: 0; }
            .mb-1 { margin-bottom: 0.25rem; }
            .mb-2 { margin-bottom: 0.5rem; }
            .mb-3 { margin-bottom: 1rem; }
            .mb-4 { margin-bottom: 1.5rem; }
            
            .mt-4 { margin-top: 1.5rem; }
            
            .small {
                font-size: 85%;
            }
            
            .fw-bold {
                font-weight: bold;
            }
            
            @page {
                margin: 0;
                size: {{ settings.paper_width }}mm auto;
            }
        }
    </style>
</head>
<body>
    <!-- Receipt Content -->
    <div class="receipt">
        <!-- Header -->
        {% if settings.show_logo and settings.business_logo %}
        <div class="text-center mb-3">
            <img src="{{ url_for('static', filename=settings.business_logo, _external=True) }}" alt="Logo" style="max-width: 150px;">
        </div>
        {% endif %}

        <div class="text-center">
            <h4 class="mb-0">{{ settings.business_name }}</h4>
            <p class="small mb-2">{{ settings.address }}<br>{{ settings.postal_code }} {{ settings.city }}</p>
            {% if settings.header_text %}
            <p class="small mb-3">{{ settings.header_text }}</p>
            {% endif %}
        </div>

        <!-- Sale Info -->
        <div class="mb-3">
            {% if settings.show_order_number %}
            <p class="mb-1">Commande: {{ sale.number }}</p>
            {% endif %}
            <p class="mb-1">Date: {{ sale.date.strftime(settings.date_format) }}</p>
            {% if settings.show_server_name %}
            <p class="mb-1">Serveur: {{ sale.server }}</p>
            {% endif %}
            {% if settings.show_table_number %}
            <p class="mb-1">Table: {{ sale.table }}</p>
            {% endif %}
        </div>

        <!-- Items -->
        <table class="mb-3">
            <thead>
                <tr>
                    <th class="text-start">Article</th>
                    <th class="text-end">Qté</th>
                    <th class="text-end">Prix</th>
                    <th class="text-end">Total</th>
                </tr>
            </thead>
            <tbody>
                {% for item in sale.items %}
                <tr>
                    <td>{{ item.name }}</td>
                    <td class="text-end">{{ item.quantity }}</td>
                    <td class="text-end">{{ "%.2f"|format(item.price) }} €</td>
                    <td class="text-end">{{ "%.2f"|format(item.total) }} €</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Totals -->
        <div class="mb-3">
            <div style="display: flex; justify-content: space-between;">
                <span>Sous-total</span>
                <span>{{ "%.2f"|format(sale.subtotal) }} €</span>
            </div>
            {% if settings.show_tax_details %}
            <div style="display: flex; justify-content: space-between;">
                <span>TVA (20%)</span>
                <span>{{ "%.2f"|format(sale.tax) }} €</span>
            </div>
            {% endif %}
            <div style="display: flex; justify-content: space-between; font-weight: bold;">
                <span>Total</span>
                <span>{{ "%.2f"|format(sale.total) }} €</span>
            </div>
        </div>

        <!-- Payment -->
        {% if settings.show_payment_details %}
        <div class="mb-3">
            <p class="mb-1">Mode de paiement: {{ sale.payment_method }}</p>
        </div>
        {% endif %}

        <!-- Footer -->
        {% if settings.footer_text %}
        <div class="text-center small mt-4">
            {{ settings.footer_text }}
        </div>
        {% endif %}
    </div>

    <script>
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html> 