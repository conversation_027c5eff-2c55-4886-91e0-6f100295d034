{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-history"></i> Historique des Contacts - {{ supplier.name }}</h1>
        <div>
            <a href="{{ url_for('inventory.add_supplier_contact', supplier_id=supplier.id) }}" class="btn btn-success me-2">
                <i class="fas fa-plus"></i> Nouveau Contact
            </a>
            <a href="{{ url_for('inventory.supplier_details', id=supplier.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour au Fournisseur
            </a>
        </div>
    </div>

    <!-- Informations du fournisseur -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5>{{ supplier.name }}</h5>
                    {% if supplier.category %}
                        <span class="badge" style="background-color: {{ supplier.category.color }};">
                            <i class="{{ supplier.category.icon }}"></i> {{ supplier.category.name }}
                        </span>
                    {% endif %}
                </div>
                <div class="col-md-6 text-end">
                    {% if supplier.email %}
                        <a href="mailto:{{ supplier.email }}" class="btn btn-sm btn-outline-primary me-2">
                            <i class="fas fa-envelope"></i> {{ supplier.email }}
                        </a>
                    {% endif %}
                    {% if supplier.phone %}
                        <a href="tel:{{ supplier.phone }}" class="btn btn-sm btn-outline-success">
                            <i class="fas fa-phone"></i> {{ supplier.phone }}
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des contacts -->
    {% if contacts %}
        <div class="row">
            {% for contact in contacts %}
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <i class="{{ contact.contact_type_icon }} text-primary"></i>
                            <strong>{{ contact.contact_type_display }}</strong>
                            {% if contact.is_important %}
                                <i class="fas fa-star text-warning ms-2" title="Important"></i>
                            {% endif %}
                        </div>
                        <small class="text-muted">{{ contact.contact_date.strftime('%d/%m/%Y %H:%M') }}</small>
                    </div>
                    <div class="card-body">
                        {% if contact.subject %}
                            <h6 class="card-title">{{ contact.subject }}</h6>
                        {% endif %}
                        <p class="card-text">{{ contact.content|truncate(150) }}</p>
                        
                        <div class="mt-3">
                            <span class="badge {{ contact.status_badge_class }}">
                                {{ contact.status.title() }}
                            </span>
                            {% if contact.follow_up_date %}
                                <small class="text-muted ms-2">
                                    <i class="fas fa-calendar-alt"></i>
                                    Suivi: {{ contact.follow_up_date.strftime('%d/%m/%Y') }}
                                    {% if contact.is_overdue %}
                                        <span class="text-danger">(En retard)</span>
                                    {% endif %}
                                </small>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100">
                            <a href="{{ url_for('inventory.edit_supplier_contact', supplier_id=supplier.id, contact_id=contact.id) }}" 
                               class="btn btn-sm btn-outline-warning">
                                <i class="fas fa-edit"></i> Modifier
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                    onclick="confirmDelete({{ contact.id }}, '{{ contact.contact_type_display }}')">
                                <i class="fas fa-trash"></i> Supprimer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
            <h3 class="text-muted">Aucun contact enregistré</h3>
            <p class="text-muted">Commencez par ajouter votre premier contact avec ce fournisseur.</p>
            <a href="{{ url_for('inventory.add_supplier_contact', supplier_id=supplier.id) }}" class="btn btn-success">
                <i class="fas fa-plus"></i> Ajouter un Contact
            </a>
        </div>
    {% endif %}
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer ce contact <strong id="contactType"></strong> ?</p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    Cette action ne peut pas être annulée.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteForm" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(contactId, contactType) {
    document.getElementById('contactType').textContent = contactType;
    document.getElementById('deleteForm').action = `/inventory/suppliers/{{ supplier.id }}/contacts/${contactId}/delete`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
